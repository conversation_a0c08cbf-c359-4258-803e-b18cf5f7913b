package com.dbj.classpal.admin.service.biz.app.config.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.admin.service.biz.app.config.IAppConfigTypeBiz;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigType;
import com.dbj.classpal.admin.service.mapper.app.config.AppConfigTypeMapper;
import org.springframework.stereotype.Service;

/**
 * @Classname IAppConfigTypeBusinessImpl
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:52:09
 * @Created by xuezhi
 */
@Service
public class IAppConfigTypeBizImpl extends ServiceImpl<AppConfigTypeMapper, AppConfigType> implements IAppConfigTypeBiz {

}
    