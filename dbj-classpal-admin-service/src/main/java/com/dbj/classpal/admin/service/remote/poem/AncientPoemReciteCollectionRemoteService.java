package com.dbj.classpal.admin.service.remote.poem;


import com.dbj.classpal.books.client.api.poem.AncientPoemReciteCollectionApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateCoverUrlBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateDescBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateStatusBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateTitleBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 古诗背诵合集表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Component
public class AncientPoemReciteCollectionRemoteService {

    @Resource
    AncientPoemReciteCollectionApi ancientPoemReciteCollectionService;

    /**
     * 分页查询
     */
    public List<AncientPoemReciteCollectionDTO> listAncientPoemReciteCollection(AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO) throws BusinessException {
        RestResponse<List<AncientPoemReciteCollectionDTO>> result = ancientPoemReciteCollectionService.listAncientPoemReciteCollection(ancientPoemReciteCollectionBO);
        return result.returnProcess(result);
    }

    /**
     * 获取详情
     */
    public AncientPoemReciteCollectionDetailDTO getAncientPoemReciteCollection(Integer id) throws BusinessException{
        RestResponse<AncientPoemReciteCollectionDetailDTO> result = ancientPoemReciteCollectionService.getAncientPoemReciteCollection(id);
        return result.returnProcess(result);
    }

    /**
     * 删除
     */
    public Boolean deleteAncientPoemReciteCollection(List<Integer> ids) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.deleteAncientPoemReciteCollection(ids);
        return result.returnProcess(result);
    }
    /**
     * 保存
     */
    public Boolean saveAncientPoemReciteCollection(AncientPoemReciteCollectionSaveBO ancientPoemReciteCollectionSaveBO) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.saveAncientPoemReciteCollection(ancientPoemReciteCollectionSaveBO);
        return result.returnProcess(result);
    }
    public Boolean updateTitle(AncientPoemReciteCollectionUpdateTitleBO ancientPoemReciteCollectionUpdateTitleBO) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.updateTitle(ancientPoemReciteCollectionUpdateTitleBO);
        return result.returnProcess(result);
    }
    public Boolean updateDescription(AncientPoemReciteCollectionUpdateDescBO ancientPoemReciteCollectionUpdateDescBO) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.updateDescription(ancientPoemReciteCollectionUpdateDescBO);
        return result.returnProcess(result);
    }
    public Boolean updateStatus(AncientPoemReciteCollectionUpdateStatusBO ancientPoemReciteCollectionUpdateStatusBO) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.updateStatus(ancientPoemReciteCollectionUpdateStatusBO);
        return result.returnProcess(result);
    }
    public Boolean updateCoverUrl(AncientPoemReciteCollectionUpdateCoverUrlBO anotherCollectionUpdateCoverUrlBO) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.updateCoverUrl(anotherCollectionUpdateCoverUrlBO);
        return result.returnProcess(result);
    }

    public Boolean update(AncientPoemReciteCollectionUpdateBO ancientPoemReciteCollectionUpdateBO) throws BusinessException {
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.updateAncientPoemReciteCollection(ancientPoemReciteCollectionUpdateBO);
        return result.returnProcess(result);
    }


    public Boolean sort(List<AncientPoemReciteCollectionSortBO> sortBOList) throws BusinessException{
        RestResponse<Boolean> result = ancientPoemReciteCollectionService.sort(sortBOList);
        return result.returnProcess(result);
    }
}
