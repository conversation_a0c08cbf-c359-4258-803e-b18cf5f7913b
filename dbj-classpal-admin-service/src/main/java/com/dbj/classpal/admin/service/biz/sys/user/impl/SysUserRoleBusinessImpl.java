package com.dbj.classpal.admin.service.biz.sys.user.impl;

import com.dbj.classpal.admin.common.dto.sys.user.SysUserRoleDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.dbj.classpal.admin.service.mapper.sys.user.SysUserRoleMapper;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserRoleBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户与角色关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
public class SysUserRoleBusinessImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleBusiness {

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public List<SysUserRoleDTO> getRoleList(List<Integer> userIds) {
        return sysUserRoleMapper.getRoleList(userIds);
    }
}
