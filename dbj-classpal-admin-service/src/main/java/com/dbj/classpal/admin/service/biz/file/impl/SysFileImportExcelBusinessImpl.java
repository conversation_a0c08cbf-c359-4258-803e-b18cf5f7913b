package com.dbj.classpal.admin.service.biz.file.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobBO;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobUpdateBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelQueryBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelTypeBO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.admin.service.mapper.file.SysFileImportExcelMapper;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 导入文件记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Service
public class SysFileImportExcelBusinessImpl extends ServiceImpl<SysFileImportExcelMapper, SysFileImportExcel> implements ISysFileImportExcelBusiness {

    @Resource
    private SysFileImportExcelMapper sysFileImportExcelMapper;

    @Override
    public Page<SysFileImportExcelDTO> pageSysFileImportExcel(PageInfo<SysFileImportExcelBO> page) {

        return sysFileImportExcelMapper.pageSysFileImportExcel(page.getPage(), page.getData());
    }

    @Override
    public List<SysFileImportExcelCountDTO> sysFileImportExcelCount() {
        return sysFileImportExcelMapper.sysFileImportExcelCount(ContextUtil.getUserIdInt());
    }

    @Override
    public List<SysFileImportExcelDTO> getFileInfoTop3(SysFileImportExcelTypeBO bo) {
        return sysFileImportExcelMapper.getFileInfoTop3(bo);
    }

    @Override
    public List<SysFileImportExcelDTO> checkProcessMd5File(SysFileImportExcelQueryBO bo) {
        return sysFileImportExcelMapper.checkProcessMd5File(bo);
    }

    @Override
    public List<SysFileImportExcel> getFailImportExcel(AppMaterialJobBO appMaterialJobBO) {
        return sysFileImportExcelMapper.getFailImportExcel(appMaterialJobBO);
    }

    @Override
    public Boolean updateFailImportExcel(AppMaterialJobUpdateBO bo) {
        return sysFileImportExcelMapper.updateFailImportExcel(bo);
    }


    @Override
    public List<ExcelFileImportQueryApiDTO> getByAnalysisJobId(ExcelFileImportQueryApiBO bo) {
        return sysFileImportExcelMapper.getByAnalysisJobId(bo);
    }

    @Override
    public List<ExcelFileImportQueryApiDTO> getByTransCodeJobId(ExcelFileImportQueryApiBO bo) {
        return sysFileImportExcelMapper.getByTransCodeJobId(bo);
    }
}
