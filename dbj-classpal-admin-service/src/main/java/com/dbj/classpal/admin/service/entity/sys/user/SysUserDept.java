package com.dbj.classpal.admin.service.entity.sys.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户与部门关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_dept")
@Tag(name ="SysUserDept对象", description="用户与部门关系表")
public class SysUserDept extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    @TableField("user_id")
    private Integer userId;

    @Schema(description = "是否主管 0 否 1 是")
    @TableField("is_manager")
    private Integer isManager;

    @Schema(description = "是否主部门 0 否 1 是")
    @TableField("is_main_dept")
    private Integer isMainDept;

    @Schema(description = "部门id")
    @TableField("dept_id")
    private Integer deptId;



}
