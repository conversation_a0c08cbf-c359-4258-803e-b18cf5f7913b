package com.dbj.classpal.admin.service.biz.file;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobBO;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobUpdateBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelQueryBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelTypeBO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 导入文件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISysFileImportExcelBusiness extends IService<SysFileImportExcel> {


    /**
     * <AUTHOR>
     * @Description  分页查询文件导入数据
     * @Date 2025/3/20 11:56 
     * @param 
     * @return 
     **/
    Page<SysFileImportExcelDTO> pageSysFileImportExcel(PageInfo<SysFileImportExcelBO> page);

    List<SysFileImportExcelCountDTO> sysFileImportExcelCount();


    /**
     * top3
     * @param bo
     * @return
     */
    List<SysFileImportExcelDTO> getFileInfoTop3(SysFileImportExcelTypeBO bo);


    /**
     * 判断是否存在文件正在处理的md5文件
     * @param bo
     * @return
     */
    List<SysFileImportExcelDTO> checkProcessMd5File(SysFileImportExcelQueryBO bo);

    /**
     * 查询处理超时2小时的所有文件
     * @return
     */
    List<SysFileImportExcel> getFailImportExcel(AppMaterialJobBO appMaterialJobBO);


    /**
     * 修改处理超时2小时的所有文件状态为处理失败
     * @return
     */
    Boolean updateFailImportExcel(AppMaterialJobUpdateBO bo);

    /**
     * 根据分析模板任务id查询导入信息
     * @param bo
     * @return
     */
    List<ExcelFileImportQueryApiDTO> getByAnalysisJobId(ExcelFileImportQueryApiBO bo);


    /**
     * 根据转码任务id查询导入信息
     * @param bo
     * @return
     */
    List<ExcelFileImportQueryApiDTO> getByTransCodeJobId(ExcelFileImportQueryApiBO bo);
}
