package com.dbj.classpal.admin.service.remote.books.book;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.books.AdminBooksApi;
import com.dbj.classpal.books.client.bo.books.*;
import com.dbj.classpal.books.client.dto.books.BooksInfoDetailApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 图书表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Component
public class BooksInfoRemoteService {

    @Resource
    private AdminBooksApi adminBooksApi;
    public Page<BooksInfoPageApiDTO> pageInfo(PageInfo<BooksInfoPageBO> pageRequest) throws BusinessException {
        RestResponse<Page<BooksInfoPageApiDTO>>  result = adminBooksApi.pageInfo(pageRequest);
        return result.returnProcess(result);
    }

    public Boolean save(BooksInfoSaveApiBO saveBO) throws BusinessException {
        RestResponse<Boolean>  result = adminBooksApi.save(saveBO);
        return result.returnProcess(result);
    }

    public Boolean update(BooksInfoUpdApiBO updBO) throws BusinessException {
        RestResponse<Boolean>  result = adminBooksApi.update(updBO);
        return result.returnProcess(result);
    }

    public Boolean batchDelete(List<Integer> ids) throws BusinessException {
        RestResponse<Boolean>  result = adminBooksApi.batchDelete(ids);
        return result.returnProcess(result);
    }

    public BooksInfoDetailApiDTO detail(Integer id) throws BusinessException {
        RestResponse<BooksInfoDetailApiDTO>  result = adminBooksApi.detail(id);
        return result.returnProcess(result);
    }

    public Boolean batchHide(BooksBatchHideApiBO batchHideBO) throws BusinessException {
        RestResponse<Boolean>  result = adminBooksApi.batchHide(batchHideBO);
        return result.returnProcess(result);
    }

    public Boolean batchLaunch(BooksBatchLaunchApiBO batchLaunchBO) throws BusinessException {
        RestResponse<Boolean>  result = adminBooksApi.batchLaunch(batchLaunchBO);
        return result.returnProcess(result);
    }

    /**
     * 根据产品名称或者编码获取列表
     * @param request
     * @return
     */
    public List<ProductInfoApiDTO> queryProductListByCodeOrName(ProductInfoApiBO request) throws BusinessException {
        RestResponse<List<ProductInfoApiDTO>> result = adminBooksApi.queryProductListByCodeOrName(request);
        return result.returnProcess(result);
    }

    /**
     * 根据产品ID获取详情
     * @param request
     * @return
     */
    public ProductInfoDetailApiDTO queryProductInfoById(ProductInfoIdApiBO request) throws BusinessException {
        RestResponse<ProductInfoDetailApiDTO> result = adminBooksApi.queryProductInfoById(request);
        return result.returnProcess(result);
    }

    /**
     * 获取图书列表
     * @param request
     * @return
     */
    public List<BooksInfoPageApiDTO> list(BooksInfoPageBO request) throws BusinessException {
        RestResponse<List<BooksInfoPageApiDTO>> result = adminBooksApi.list(request);
        return result.returnProcess(result);
    }
}
