package com.dbj.classpal.admin.service.mapper.file;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelBO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 导出文件记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Mapper
public interface SysFileExportExcelMapper extends BaseMapper<SysFileExportExcel> {


    /**
     * 分页查询数据
     * @param page
     * @return
     */
    Page<SysFileExportExcelDTO> pageSysFileExportExcel(Page page,@Param("bo") SysFileExportExcelBO sysFileExportExcelBO);

    List<SysFileExportExcelCountDTO> sysFileExportExcelCount(@Param("userId")Integer userId);

}
