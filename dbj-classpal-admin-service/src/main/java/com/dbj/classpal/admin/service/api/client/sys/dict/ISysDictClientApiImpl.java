package com.dbj.classpal.admin.service.api.client.sys.dict;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.client.api.sys.dict.ISysDictApi;
import com.dbj.classpal.admin.client.bo.app.dict.DictItemApiQueryBo;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictApiDTO;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.admin.common.bo.sys.dict.DictItemQueryBo;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.admin.service.mapper.sys.dict.SysDictItemMapper;
import com.dbj.classpal.admin.service.service.sys.dict.ISysDictService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDictService
 * @description
 * @date 2025-03-14 15:58
 **/
@RestController
public class ISysDictClientApiImpl implements ISysDictApi {


    @Resource
    private ISysDictService sysDictService;
    @Resource
    private ISysDictBusiness sysDictBusiness;

    @Resource
    private ISysDictItemBusiness sysDictItemBusiness;

    @Override
    public RestResponse<Map<String,List<SysDictItemApiDTO>>> getSysDictInfoAll() throws BusinessException {
        Map<String,List<SysDictItemDTO>> sysdictMapping =  sysDictService.getSysDictInfoAll();
        Map<String,List<SysDictItemApiDTO>> sysdictMappingNew = new HashMap<>();
        for(String key : sysdictMapping.keySet()){
            sysdictMappingNew.put(key, BeanUtil.copyToList(sysdictMapping.get(key),SysDictItemApiDTO.class));
        }
        return RestResponse.success(sysdictMappingNew);
    }
    public RestResponse<SysDictItemApiDTO> getAdjacentDictItem(DictItemApiQueryBo apiQueryBo) throws BusinessException {
        DictItemQueryBo queryBo = BeanUtil.copyProperties(apiQueryBo, DictItemQueryBo.class);
        SysDictItemDTO sysDictItemDTO =  sysDictService.getAdjacentDictItem(queryBo);
        if(sysDictItemDTO != null){
            SysDict sysDict = sysDictBusiness.getById(sysDictItemDTO.getDictId());
            if(sysDict != null){
                sysDictItemDTO.setDictCode(sysDict.getDictCode());
            }
        }
        return RestResponse.success(BeanUtil.copyProperties(sysDictItemDTO, SysDictItemApiDTO.class));

    }

    @Override
    public RestResponse<SysDictItemApiDTO> getDictItem(DictItemApiQueryBo apiQueryBo) throws BusinessException {
        DictItemQueryBo queryBo = BeanUtil.copyProperties(apiQueryBo, DictItemQueryBo.class);
        return RestResponse.success(sysDictService.getDictItem(queryBo));
    }
}


