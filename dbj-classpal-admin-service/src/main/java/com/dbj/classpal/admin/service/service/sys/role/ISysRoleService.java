package com.dbj.classpal.admin.service.service.sys.role;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRolePageBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleSaveBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleUpdBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleUpdStatusBO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRoleDTO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className SysRoleService
 * @description
 * @date 2025-03-17 16:32
 **/
public interface ISysRoleService {

    /**
     * <AUTHOR>
     * @Description  获取单个角色信息
     * @Date 2025/3/17 15:54
     * @param id 主键id
     * @return SysRoleDTO 角色信息
     **/
    SysRoleDTO getSysRoleInfo(Integer id) throws BusinessException;

    /**
     * 添加SysRole数据
     * @Title: saveSysRole
     * @Description: 添加SysRole数据
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean saveSysRole(SysRoleSaveBO bo) throws BusinessException;

    /**
     * 修改数据
     * @Title: UpdateSysRole
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    Boolean updateSysRole(SysRoleUpdBO bo) throws BusinessException;
    /**
     * 修改角色状态
     * @Title: UpdateSysRole
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    Boolean updateSysRoleStatus(SysRoleUpdStatusBO bo) throws BusinessException;

    /**
     * 获取单个SysRole数据
     * @Title: saveSysRole
     * @Description: 添加SysRole数据
     * @param baseIdsBO
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean delSysRoleInfo(BaseIdsBO baseIdsBO) throws BusinessException;

    /**
     * 分页查询数据
     * @Title: getSysRoleAll
     * @Description: 分页查询数据
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    Page<SysRolePageDTO> pageSysRole(PageInfo<SysRolePageBO> bo);
    /**
     * 查询所有角色
     * @Title: getSysRoleAll
     * @Description: 查询所有角色
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    List<SysRolePageDTO> getSysRoleAll(SysRolePageBO bo);
}
