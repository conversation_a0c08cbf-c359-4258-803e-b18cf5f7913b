package com.dbj.classpal.admin.service.service.app.config.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigTypeQueryDTO;
import com.dbj.classpal.admin.common.enums.StatusEnum;
import com.dbj.classpal.admin.service.biz.app.config.IAppConfigTypeBiz;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigType;
import com.dbj.classpal.admin.service.service.app.config.IAppConfigTypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname IAppConfigTypeServiceImpl
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:48:31
 * @Created by xuezhi
 */
@Service
public class IAppConfigTypeServiceImpl implements IAppConfigTypeService {
    @Autowired
    private IAppConfigTypeBiz business;

    @Override
    public List<AppConfigTypeQueryDTO> getAllAppConfigTypeList() {
        List<AppConfigType> list = business.list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(appConfigType -> {
            AppConfigTypeQueryDTO dto = new AppConfigTypeQueryDTO();
            BeanUtil.copyProperties(appConfigType,dto);
            StatusEnum statusEnum = StatusEnum.getByCode(appConfigType.getTypeStatus());
            if (statusEnum != null) {
                dto.setTypeStatusStr(statusEnum.getName());
            }
            return dto;
        }).collect(Collectors.toList());
    }
}
    