package com.dbj.classpal.admin.service.remote.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.config.BasicConfigApi;
import com.dbj.classpal.books.client.bo.config.BasicConfigApiBO;
import com.dbj.classpal.books.client.bo.config.BasicConfigIdApiBO;
import com.dbj.classpal.books.client.bo.config.BasicConfigIdsApiBO;
import com.dbj.classpal.books.client.bo.config.BasicConfigQueryApiBO;
import com.dbj.classpal.books.client.dto.config.BasicConfigApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:23:01
 */
@Component
public class BasicConfigRemoteService {
    @Resource
    private BasicConfigApi configApi;

    /**
     * 创建配置
     */
    public Integer create(@RequestBody BasicConfigApiBO apiBO) throws BusinessException{
        RestResponse<Integer> result = configApi.create(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 更新配置
     */
    public Void update(@RequestBody BasicConfigApiBO apiBO) throws BusinessException{
        RestResponse<Void> result = configApi.update(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 删除配置
     */
    public Void batchDelete(@RequestBody BasicConfigIdsApiBO idsApiBO) throws BusinessException{
        RestResponse<Void> result = configApi.batchDelete(idsApiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取配置详情
     */
    public BasicConfigApiDTO detail(@RequestBody BasicConfigIdApiBO idApiBO) throws BusinessException {
        RestResponse<BasicConfigApiDTO> result = configApi.detail(idApiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取配置列表
     */
    public Page<BasicConfigApiDTO> pageList(@RequestBody PageInfo<BasicConfigQueryApiBO> queryApiBO) throws BusinessException {
        RestResponse<Page<BasicConfigApiDTO>> result = configApi.pageList(queryApiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取配置列表
     */
    public List<BasicConfigApiDTO> list(@RequestBody BasicConfigQueryApiBO queryApiBO) throws BusinessException {
        RestResponse<List<BasicConfigApiDTO>> result = configApi.list(queryApiBO);
        return result.returnProcess(result);
    }

}