package com.dbj.classpal.admin.service.mapper.sys.user;

import com.dbj.classpal.admin.common.dto.sys.user.SysUserRoleDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户与角色关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * <AUTHOR>
     * @Description  根据用户id集合查询对应的角色名称信息
     * @Date 2025/3/18 9:05
     * @param
     * @return
     **/
    List<SysUserRoleDTO> getRoleList(@Param("userIds") List<Integer> userIds);
}
