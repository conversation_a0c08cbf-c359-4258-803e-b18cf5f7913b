package com.dbj.classpal.admin.service.mq.listener.file.imports.handle;

import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.dto.CommonExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.file.ExcelFileStrategy;
import com.dbj.classpal.framework.utils.param.ExportExcelParam;
import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className AdminExcelFileStrategy
 * @description
 * @date 2025-03-24 15:58
 **/
public abstract class AdminExcelFileStrategy<T extends CommonExcelBO> extends ExcelFileStrategy<T> {



    @Resource
    private ISysFileImportExcelBusiness systemFileImportExcelBusiness;




    /**
     * 系统失败时调用 犹豫该处理是再各系统中进行，所有不同系统的调用方式不一样
     * @param fileDomain 需要修改的文件数据
     */
    @Override
    public void handleProcessingFailedSys(SysFileImportExcelBO fileDomain) {
        systemFileImportExcelBusiness.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getErrorMsg, fileDomain.getErrorMsg())
                .set(SysFileImportExcel::getHandleEndTime,LocalDateTime.now())
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_SYS.getCode()).update();
    }

    /**
     * 业务失败时调用 犹豫该处理是再各系统中进行，所有不同系统的调用方式不一样
     * @param importBOList 导入的数据
     * @param fileDomain 文件状态数据
     * @param errFileName 错误文件名称
     * @param errorNum 错误数量
     */
    @Override
    public void handleProcessingFailedBusiness(List<T> importBOList, SysFileImportExcelBO fileDomain, String errFileName, Integer errorNum) {
        ExportExcelParam exportExcelParam = new ExportExcelParam();
        exportExcelParam.setData(importBOList);
        String errUrl = updateOss(exportExcelParam,errFileName);
        systemFileImportExcelBusiness.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getProcessedUrl, errUrl)
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_BUSINESS.getCode())
                .set(SysFileImportExcel::getErrorNum, errorNum)
                .set(SysFileImportExcel::getHandleEndTime,LocalDateTime.now())
                .set(SysFileImportExcel::getSubNum,importBOList.size() - errorNum)
                .update();
    }


    /**
     * 修改为处理完成
     * @param fileDomain
     * @param susFileName
     * @param totalNum
     */
    @Override
    public void updateFileProcessed(SysFileImportExcelBO fileDomain, String susFileName, Integer totalNum) {
        systemFileImportExcelBusiness.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getSubNum, totalNum)
                .set(SysFileImportExcel::getErrorNum, 0)
                .set(SysFileImportExcel::getHandleEndTime, LocalDateTime.now())
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSED.getCode()).update();
    }
}