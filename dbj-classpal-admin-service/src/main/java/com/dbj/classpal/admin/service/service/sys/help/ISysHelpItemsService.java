package com.dbj.classpal.admin.service.service.sys.help;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.help.BatchUpdHelpStatusBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsDetailBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsSaveBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsUpdBO;
import com.dbj.classpal.admin.common.dto.help.SysHelpItemsDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysHelpItemsService
 * @description
 * @date 2025-03-13 09:04
 **/
public interface ISysHelpItemsService {


    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param
     * @return
     **/
    SysHelpItemsDTO getSysHelpItemsInfo(SysHelpItemsDetailBO SysHelpItemsBO ) throws BusinessException;


    /**
     * <AUTHOR>
     * @Description  获取单个帮助文档
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    SysHelpItemsDTO getSysHelpItemsInfoByPageId(Integer menuPageId );

    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param
     * @return
     **/
    Boolean saveSysHelpItems(SysHelpItemsSaveBO bo) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param
     * @return
     **/
    Boolean updateSysHelpItems(SysHelpItemsUpdBO bo) throws BusinessException;


    /**
     * <AUTHOR>
     * @Description  修改帮助文档
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    Boolean batchUpdHelpStatus(BatchUpdHelpStatusBO bo) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description  批量删除帮助文档状态
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    Boolean batchDelHelp(BatchUpdHelpStatusBO bo) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description  批量删除帮助文档状态
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    Page<SysHelpItemsDTO> pageSysHelpItems(PageInfo<SysHelpItemsBO> bo) throws BusinessException;

}
