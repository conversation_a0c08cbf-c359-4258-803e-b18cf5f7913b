package com.dbj.classpal.admin.service.remote.appgray;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.api.gray.AppFeatureWhitelistClientApi;
import com.dbj.classpal.app.client.bo.gray.WhitelistBatchSaveApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistIdApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistIdsApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistQueryApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistSaveApiBO;
import com.dbj.classpal.app.client.dto.gray.WhitelistDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:33:47
 */
@Component
public class AppFeatureWhitelistRemoteService {
    @Resource
    AppFeatureWhitelistClientApi appFeatureWhitelistClientApi;

    public Page<WhitelistDetailApiDTO> pageWhitelists(PageInfo<WhitelistQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<WhitelistDetailApiDTO>> result =  appFeatureWhitelistClientApi.pageWhitelists(pageRequest);
        return result.returnProcess(result);
    }

    public void saveWhitelist(WhitelistSaveApiBO whitelist) throws BusinessException {
        RestResponse<Void> result =  appFeatureWhitelistClientApi.saveWhitelist(whitelist);
        result.returnProcess(result);
    }

    public void batchSaveWhitelists(WhitelistBatchSaveApiBO whitelists) throws BusinessException {
        RestResponse<Void> result =   appFeatureWhitelistClientApi.batchSaveWhitelists(whitelists);
        result.returnProcess(result);
    }

    public WhitelistDetailApiDTO getWhitelistDetail(WhitelistIdApiBO whitelistId) throws BusinessException {
        RestResponse<WhitelistDetailApiDTO> result = appFeatureWhitelistClientApi.getWhitelistDetail(whitelistId);
        return result.returnProcess(result);
    }

    public List<WhitelistDetailApiDTO> batchGetWhitelistDetails(WhitelistIdsApiBO whitelistIds) throws BusinessException {
        RestResponse<List<WhitelistDetailApiDTO>> result =  appFeatureWhitelistClientApi.batchGetWhitelistDetails(whitelistIds);
        return result.returnProcess(result);
    }

    public void batchDelete(WhitelistIdsApiBO ids) throws BusinessException {
        RestResponse<Void> result =   appFeatureWhitelistClientApi.batchDelete(ids);
        result.returnProcess(result);
    }
}