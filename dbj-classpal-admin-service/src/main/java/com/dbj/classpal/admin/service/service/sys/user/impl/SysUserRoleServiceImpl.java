package com.dbj.classpal.admin.service.service.sys.user.impl;

import com.dbj.classpal.admin.service.biz.sys.user.ISysUserRoleBusiness;
import com.dbj.classpal.admin.common.bo.sys.user.AllocationRoleBO;
import com.dbj.classpal.admin.common.bo.sys.user.AllocationUserBO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserRoleService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class SysUserRoleServiceImpl implements ISysUserRoleService {


    @Resource
    private ISysUserRoleBusiness sysUserRoleBusiness;

    @Override
    public Boolean allocationUser(AllocationUserBO allocationUser) {
        List<Integer> userIds = allocationUser.getUserIds();
        Integer roleId = allocationUser.getRoleId();
        sysUserRoleBusiness.lambdaUpdate().eq(SysUserRole::getRoleId,roleId).remove();
        List<SysUserRole> sysUserRoleList = new ArrayList<>();
        for(Integer userId : userIds){
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(userId);
            sysUserRole.setRoleId(roleId);
            sysUserRoleList.add(sysUserRole);
        }
        sysUserRoleBusiness.saveBatch(sysUserRoleList);
        return true;
    }

    @Override
    public Boolean allocationRole(AllocationRoleBO allocationRoleBO) {
        sysUserRoleBusiness.lambdaUpdate().in(SysUserRole::getUserId,allocationRoleBO.getUserIds()).remove();
        List<SysUserRole> sysUserRoleList = new ArrayList<>();
        for (Integer userId : allocationRoleBO.getUserIds()){
            for (Integer roleId : allocationRoleBO.getRoleIds()){
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(userId);
                sysUserRole.setRoleId(roleId);
                sysUserRoleList.add(sysUserRole);
            }
        }
        sysUserRoleBusiness.saveBatch(sysUserRoleList);
        return true;
    }

    @Override
    public List<Integer> getUserByRoleId(Integer roleId) {
        List<SysUserRole> sysUserRoleList = sysUserRoleBusiness.lambdaQuery().eq(SysUserRole::getRoleId, roleId).list();
        List<Integer> userIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(sysUserRoleList)){
            userIds = sysUserRoleList.stream().map(SysUserRole::getUserId).toList();

        }
        return userIds;
    }
}
