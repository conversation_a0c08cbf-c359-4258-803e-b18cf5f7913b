package com.dbj.classpal.admin.service.entity.file;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导出文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_file_export_excel")
@Tag(name="SysFileExportExcel对象", description="导出文件记录")
public class SysFileExportExcel extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "服务标识 system app books")
    @TableField("sign")
    private String sign;

    @Schema(description = "所属模块名称")
    @TableField("module_name")
    private String moduleName;

    @Schema(description = "导出文件名称")
    @TableField("file_name")
    private String fileName;

    @Schema(description = "导出文件url")
    @TableField("file_url")
    private String fileUrl;

    @Schema(description = "开始处理时间")
    @TableField("handle_start_time")
    private LocalDateTime handleStartTime;

    @Schema(description = "结束处理时间")
    @TableField("handle_end_time")
    private LocalDateTime handleEndTime;

    @Schema(description = "上传文件业务类型")
    @TableField("type")
    private String type;

    @Schema(description = "处理失败(还没有开始处理数据就失败的原因)")
    @TableField("error_msg")
    private String errorMsg;

    @Schema(description = "额外参数")
    @TableField("param_json")
    private String paramJson;
    @Schema(description = "是否已读 0-否 1-是")
    @TableField("is_read")
    private Integer isRead;

    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 5 已取消")
    @TableField("status")
    private Integer status;


}
