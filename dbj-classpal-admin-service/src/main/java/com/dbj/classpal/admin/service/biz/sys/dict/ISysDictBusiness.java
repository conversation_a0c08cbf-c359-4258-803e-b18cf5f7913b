package com.dbj.classpal.admin.service.biz.sys.dict;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 数据字典主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface ISysDictBusiness extends IService<SysDict> {


    Page<SysDictDTO> pageSysDictInfo(PageInfo<SysDictBO> page) throws BusinessException;


    /**
     * 不分页查询
     * @param sysDictBO
     * @return
     */
    List<SysDictDTO> listSysDictInfo(SysDictBO sysDictBO);
}
