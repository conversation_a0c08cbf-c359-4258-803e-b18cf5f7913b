package com.dbj.classpal.admin.service.service.file.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.admin.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelBO;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelClearBO;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelSaveBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.biz.file.ISysFileExportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.admin.service.mq.constant.ExchangeConstant;
import com.dbj.classpal.admin.service.mq.constant.RoutingKeyConstant;
import com.dbj.classpal.admin.service.service.file.ISysFileExportExcelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.text.MessageFormat;
import java.util.List;

/**
 * <p>
 * 导出文件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Service
@Slf4j
public class SysFileExportExcelServiceImpl implements ISysFileExportExcelService {

    @Resource
    private ISysFileExportExcelBusiness sysFileExportBusiness;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Override
    public SysFileExportExcelDTO getSysFileExportExcel(Integer id) throws BusinessException {
        SysFileExportExcel sysFileExportExcel =  sysFileExportBusiness.getById(id);
        if(sysFileExportExcel == null){
            throw new BusinessException(AdminErrorCode.EXPORT_EXCEL_FILE_NOT_EXIST_CODE,AdminErrorCode.EXPORT_EXCEL_FILE_NOT_EXIST_MSG);
        }
        return BeanUtil.copyProperties(sysFileExportExcel, SysFileExportExcelDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveExportFile(SysFileExportExcelSaveBO saveBO) throws Exception {
        SysFileExportExcel domain = new SysFileExportExcel();
        BeanUtils.copyProperties(saveBO, domain);
        if (sysFileExportBusiness.save(domain)) {
            ExcelFileEntity excelFileEntity = new ExcelFileEntity();
            excelFileEntity.setId(domain.getId());
            log.info("保存文件成功，开始发送mq消息：{}", JSON.toJSONString(excelFileEntity));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, ExchangeConstant.CLASSPAL_FILE_EXCHANGE, MessageFormat.format(RoutingKeyConstant.CLASSPAL_EXPORT_FILE_SERVICE_ROUTING_KEY, saveBO.getSign()));
                }
            });
        }
        return domain.getId();
    }

    @Override
    public List<SysFileExportExcelCountDTO> sysFileExportExcelCount() {
        return sysFileExportBusiness.sysFileExportExcelCount();
    }

    @Override
    public Page<SysFileExportExcelDTO> pageSysFileExportExcel(PageInfo<SysFileExportExcelBO> page) {
        page.getData().setCreateBy(ContextUtil.getUserIdInt());

        List<Integer> status = page.getData().getStatus();
        if(CollectionUtils.isNotEmpty(status)){
            sysFileExportBusiness.lambdaUpdate().in( SysFileExportExcel::getStatus,status)
                    .eq( SysFileExportExcel::getCreateBy,ContextUtil.getUserIdInt())
                    .set(SysFileExportExcel::getIsRead, YesOrNoEnum.YES.getCode()).update();
        }

        Page<SysFileExportExcelDTO> pageInfo = sysFileExportBusiness.pageSysFileExportExcel(page);
        return pageInfo;
    }

    @Override
    public Boolean deleteClear(SysFileExportExcelClearBO bo) {
        sysFileExportBusiness.lambdaUpdate().in(SysFileExportExcel::getStatus, bo.getStatus())
                .eq(SysFileExportExcel::getCreateBy,ContextUtil.getUserIdInt()).remove();
        return true;
    }
}
