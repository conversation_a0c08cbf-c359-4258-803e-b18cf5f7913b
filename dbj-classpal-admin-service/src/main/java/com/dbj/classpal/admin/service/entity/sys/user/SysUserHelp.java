package com.dbj.classpal.admin.service.entity.sys.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户与部门关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_help")
@Tag(name = "SysUserHelp对象", description="用户与部门关系表")
public class SysUserHelp extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description ="用户id")
    @TableField("user_id")
    private Integer userId;

    @Schema(description ="是否折叠 0 否 1 是")
    @TableField("is_fold")
    private Integer isFold;

    @Schema(description ="帮助文档id")
    @TableField("help_id")
    private Integer helpId;

    @Schema(description ="版本号")
    @TableField("version")
    private Integer version;


}
