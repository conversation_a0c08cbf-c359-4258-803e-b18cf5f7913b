package com.dbj.classpal.admin.service.mapper.sys.dict;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据字典项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Mapper
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {


    Page<SysDictItemDTO> pageSysDictItemInfo(Page page,@Param("bo") SysDictItemBO sysDictItemBO);
}
