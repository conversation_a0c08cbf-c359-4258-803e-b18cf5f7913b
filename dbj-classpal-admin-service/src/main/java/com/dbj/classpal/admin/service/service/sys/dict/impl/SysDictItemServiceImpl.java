package com.dbj.classpal.admin.service.service.sys.dict.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemDetailBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemUpdBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemUpdStatusBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.admin.service.service.sys.dict.ISysDictItemService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDictItemService
 * @description
 * @date 2025-03-14 15:58
 **/
@Service
public class SysDictItemServiceImpl implements ISysDictItemService {

    @Resource
    private ISysDictItemBusiness sysDictItemBusiness;

    @Override
    public SysDictItemDTO getSysDictItemInfo(SysDictItemDetailBO reqBo) throws BusinessException {
        SysDictItem sysDictItem = sysDictItemBusiness.getById(reqBo.getId());
        if(sysDictItem == null){
            throw new BusinessException(AdminErrorCode.DICT_ITEM_NOT_EXIST_CODE,AdminErrorCode.DICT_ITEM_NOT_EXIST_MSG);
        }
        return BeanUtil.copyProperties(sysDictItem, SysDictItemDTO.class);
    }

    @Override
    public Boolean saveSysDictItem(SysDictItemSaveBO bo) throws BusinessException {
        //判断是否有重复
        List<SysDictItem> sysDictItemList = sysDictItemBusiness.lambdaQuery().eq(SysDictItem::getItemValue,bo.getItemValue())
                .eq(SysDictItem::getDictId,bo.getDictId()).list();
        if(CollectionUtils.isNotEmpty(sysDictItemList)){
            throw new BusinessException(AdminErrorCode.DICT_CODE_REPEAT_CODE,AdminErrorCode.DICT_CODE_REPEAT_MSG);
        }

        sysDictItemList = sysDictItemBusiness.lambdaQuery().eq(SysDictItem::getDictId,bo.getDictId())
                .eq(SysDictItem::getItemName,bo.getItemName()).list();
        if(CollectionUtils.isNotEmpty(sysDictItemList)){
            throw new BusinessException(AdminErrorCode.DICT_ITEM_EXIST_CODE,AdminErrorCode.DICT_ITEM_EXIST_MSG);
        }
        sysDictItemBusiness.save(BeanUtil.copyProperties(bo, SysDictItem.class));
        return true;
    }

    @Override
    public Boolean updateSysDictItem(SysDictItemUpdBO bo) throws BusinessException {
        //判断数据是否存在，是否有重复
        SysDictItem sysDictItem = sysDictItemBusiness.getById(bo.getId());
        if(sysDictItem == null){
            throw new BusinessException(AdminErrorCode.DICT_ITEM_NOT_EXIST_CODE,AdminErrorCode.DICT_ITEM_NOT_EXIST_MSG);
        }
        //判断是否有重复
        if(!StringUtils.equals(sysDictItem.getItemValue(),bo.getItemValue())){
            List<SysDictItem> sysDictItemList = sysDictItemBusiness.lambdaQuery().eq(SysDictItem::getDictId,bo.getDictId())
                    .eq(SysDictItem::getItemValue,bo.getItemValue()).list();
            if(CollectionUtils.isNotEmpty(sysDictItemList)){
                throw new BusinessException(AdminErrorCode.DICT_CODE_REPEAT_CODE,AdminErrorCode.DICT_CODE_REPEAT_MSG);
            }
        }

        if(!StringUtils.equals(sysDictItem.getItemName(),bo.getItemName())){
            List<SysDictItem> sysDictItemList = sysDictItemBusiness.lambdaQuery().eq(SysDictItem::getDictId,bo.getDictId())
                    .eq(SysDictItem::getItemName,bo.getItemName()).list();
            if(CollectionUtils.isNotEmpty(sysDictItemList)){
                throw new BusinessException(AdminErrorCode.DICT_ITEM_EXIST_CODE,AdminErrorCode.DICT_ITEM_EXIST_MSG);
            }
        }
        sysDictItemBusiness.updateById(BeanUtil.copyProperties(bo, SysDictItem.class));
        return true;
    }

    @Override
    public Boolean batchDelSysDictItemInfo(SysDictItemUpdStatusBO reqBo) throws BusinessException {
        //判断是否有子集 有子集不允许删除
        //判断数据是否存在，是否有重复
        sysDictItemBusiness.removeByIds(reqBo.getIds());
        return true;
    }

    @Override
    public Boolean batchUpdStatus(SysDictItemUpdStatusBO reqBo) throws BusinessException {
        sysDictItemBusiness.lambdaUpdate().in(SysDictItem::getId,reqBo.getIds()).set(SysDictItem::getStatus,reqBo.getStatus()).update();
        return true;
    }

    @Override
    public Page<SysDictItemDTO> pageSysDictItemInfo(PageInfo<SysDictItemBO> page) throws BusinessException {
        Page<SysDictItemDTO> SysDictItemPage = sysDictItemBusiness.pageSysDictItemInfo(page);
        return SysDictItemPage;
    }

}
