package com.dbj.classpal.admin.service.api.client.app.agreement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dbj.classpal.admin.client.api.app.agreement.AppAgreementApi;
import com.dbj.classpal.admin.client.dto.app.agreement.AppAgreementApiQueryDTO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementQueryBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.app.agreement.AppAgreementQueryDTO;
import com.dbj.classpal.admin.common.enums.StatusEnum;
import com.dbj.classpal.admin.service.service.app.agreement.IAppAgreementService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname AppAgreementApiImpl
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-20 17:02:17
 * @Created by xuezhi
 */
@RestController
public class AppAgreementApiImpl implements AppAgreementApi {

    @Resource
    private IAppAgreementService service;


    @Override
    public RestResponse<List<AppAgreementApiQueryDTO>> getApiAllAppAgreements() {
        return RestResponse.success(service.getAllAppAgreements().stream()
                .filter(d -> d.getAgreementStatus() != null && StatusEnum.AGREEMENT_STATUS_YES.getCode().equals(d.getAgreementStatus()))
                .map(d -> {
                    AppAgreementApiQueryDTO dto = new AppAgreementApiQueryDTO();
                    BeanUtil.copyProperties(d, dto);
                    return dto;
                }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<AppAgreementApiQueryDTO> getApiAppAgreementById(Integer id) throws BusinessException {
        if (ObjectUtils.isEmpty(id)) {
            throw new BusinessException(AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_CODE,AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_MSG);
        }
        AppAgreementQueryDTO appAgreementById = service.getAppAgreementById(id);
        if (ObjectUtil.isNull(appAgreementById) || appAgreementById.getAgreementStatus().equals(StatusEnum.AGREEMENT_STATUS_NO.getCode())) {
            return null;
        }
        AppAgreementApiQueryDTO dto = new AppAgreementApiQueryDTO();
        BeanUtil.copyProperties(appAgreementById, dto);
        return RestResponse.success(dto);
    }

    @Override
    public RestResponse<AppAgreementApiQueryDTO> getApiAppAgreementByTypeId(Integer agreementType) throws BusinessException {
        if (ObjectUtils.isEmpty(agreementType)) {
            throw new BusinessException(AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_CODE,AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_MSG);
        }
        AppAgreementQueryBO queryBO = new AppAgreementQueryBO();
        queryBO.setAgreementType(agreementType);
        AppAgreementQueryDTO appAgreementById = service.getAppAgreementByTypeId(queryBO);
        if (ObjectUtils.isEmpty(appAgreementById) || appAgreementById.getAgreementStatus().equals(StatusEnum.AGREEMENT_STATUS_NO.getCode())) {
            return null;
        }
        AppAgreementApiQueryDTO dto = new AppAgreementApiQueryDTO();
        BeanUtil.copyProperties(appAgreementById, dto);
        return RestResponse.success(dto);
    }
}
    