package com.dbj.classpal.admin.service.remote.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pointreading.PointReadingChapterApi;
import com.dbj.classpal.books.client.bo.pointreading.*;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingChapterApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 点读章节相关远程服务
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Component
public class PointReadingChapterRemoteService {

    @Resource
    private PointReadingChapterApi pointReadingChapterApi;

    /**
     * 分页查询点读书章节
     */
    public Page<PointReadingChapterApiDTO> pageInfo(PageInfo<PointReadingChapterQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<PointReadingChapterApiDTO>> result = pointReadingChapterApi.pagePage(pageRequest);
        return result.returnProcess(result);
    }

    /**
     * 查询章节详情
     */
    public PointReadingChapterApiDTO detail(Integer id) throws BusinessException {
        RestResponse<PointReadingChapterApiDTO> result = pointReadingChapterApi.detail(id);
        return result.returnProcess(result);
    }

    /**
     * 保存点读书章节
     */
    public Integer save(PointReadingChapterSaveApiBO saveBO) throws BusinessException {
        RestResponse<Integer> result = pointReadingChapterApi.save(saveBO);
        return result.returnProcess(result);
    }

    /**
     * 更新点读书章节
     */
    public Boolean update(PointReadingChapterUpdateApiBO updateBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.update(updateBO);
        return result.returnProcess(result);
    }

    /**
     * 删除点读书章节
     */
    public Boolean delete(Integer id) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.delete(id);
        return result.returnProcess(result);
    }

    /**
     * 批量删除点读书章节
     */
    public Boolean deleteBatch(PointReadingChapterBatchApiBO batchBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.deleteBatch(batchBO);
        return result.returnProcess(result);
    }

    /**
     * 查询目录下的章节列表
     */
    public List<PointReadingChapterApiDTO> getPagesByMenu(Integer menuId) throws BusinessException {
        RestResponse<List<PointReadingChapterApiDTO>> result = pointReadingChapterApi.getPagesByMenu(menuId);
        return result.returnProcess(result);
    }

    /**
     * 查询点读书下的章节列表
     */
    public List<PointReadingChapterApiDTO> getPagesByBook(Integer bookId) throws BusinessException {
        RestResponse<List<PointReadingChapterApiDTO>> result = pointReadingChapterApi.getPagesByBook(bookId);
        return result.returnProcess(result);
    }

    /**
     * 更新排序
     */
    public Boolean updateSort(PointReadingChapterSortApiBO sortBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.updateSort(sortBO);
        return result.returnProcess(result);
    }

    /**
     * 启用章节
     */
    public Boolean enable(Integer id) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.enable(id);
        return result.returnProcess(result);
    }

    /**
     * 禁用章节
     */
    public Boolean disable(Integer id) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.disable(id);
        return result.returnProcess(result);
    }

    /**
     * 批量启用章节
     */
    public Boolean enableBatch(PointReadingChapterBatchApiBO batchBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.enableBatch(batchBO);
        return result.returnProcess(result);
    }

    /**
     * 批量禁用章节
     */
    public Boolean disableBatch(PointReadingChapterBatchApiBO batchBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingChapterApi.disableBatch(batchBO);
        return result.returnProcess(result);
    }
}
