package com.dbj.classpal.admin.service.remote.poem;


import com.dbj.classpal.books.client.api.poem.AncientPoemReciteCategoryApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategoryUpdateBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 古诗文背诵分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Component
public class AncientPoemReciteCategoryRemoteService {

    @Resource
    private AncientPoemReciteCategoryApi ancientPoemReciteCategoryApi;



    public List<AncientPoemReciteCategoryDTO> listAncientPoemReciteCategory() throws BusinessException {
        RestResponse<List<AncientPoemReciteCategoryDTO>> restResponse = ancientPoemReciteCategoryApi.listAncientPoemReciteCategory();
        return restResponse.returnProcess(restResponse);
    }

    public AncientPoemReciteCategoryDetailDTO getAncientPoemReciteCategory(Integer id) throws BusinessException{
        RestResponse<AncientPoemReciteCategoryDetailDTO> restResponse = ancientPoemReciteCategoryApi.getAncientPoemReciteCategory(id);
        return restResponse.returnProcess(restResponse);

    }
    public Boolean save(AncientPoemReciteCategorySaveBO ancientPoemReciteCategorySaveBO) throws BusinessException {
        RestResponse<Boolean> restResponse = ancientPoemReciteCategoryApi.save(ancientPoemReciteCategorySaveBO);
        return restResponse.returnProcess(restResponse);

    }

    public Boolean update(AncientPoemReciteCategoryUpdateBO ancientPoemReciteCategoryUpdateBO) throws BusinessException{
        RestResponse<Boolean> restResponse = ancientPoemReciteCategoryApi.update(ancientPoemReciteCategoryUpdateBO);
        return restResponse.returnProcess(restResponse);

    }

    public Boolean delete(Integer id) throws BusinessException{
        RestResponse<Boolean> restResponse = ancientPoemReciteCategoryApi.delete(id);
        return restResponse.returnProcess(restResponse);

    }

    public Boolean sort(List<AncientPoemReciteCategorySortBO> ancientPoemReciteCategorySortBOs) throws BusinessException{
        RestResponse<Boolean> restResponse = ancientPoemReciteCategoryApi.sort(ancientPoemReciteCategorySortBOs);
        return restResponse.returnProcess(restResponse);

    }

}
