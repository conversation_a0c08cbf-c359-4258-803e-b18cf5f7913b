package com.dbj.classpal.admin.service.mq.listener.file.exports.handle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictExportBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictImportBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.common.enums.dict.DictTypeEnum;
import com.dbj.classpal.admin.service.biz.file.ISysFileExportExcelBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.admin.service.mq.listener.file.imports.handle.AdminExcelFileStrategy;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.file.IExportExcelFileStrategy;
import com.dbj.classpal.framework.utils.param.ExportExcelParam;
import com.dbj.classpal.framework.utils.util.ImportExcelUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19 13:56:37
 */
@Service("sysDictExportExcelFileStrategy")
@Slf4j
public class SysDictExportExcelFileStrategy extends AdminExportExcelFileStrategy<SysDictExportBO> {


    @Resource
    private ISysDictBusiness sysDictBusiness;
    @Resource
    private ISysDictItemBusiness sysDictItemBusiness;

    @Override
    public void handlerExportExcelFile(SysFileExportExcelBO fileDomain) throws Exception {

        //字段数据不多，所以这里直接查询全部，后续如果数据较多，则需要分页处理
        try {
            //根据业务查询数据
            SysDictBO sysDictBO = new SysDictBO();
            if(StringUtils.isNotEmpty(fileDomain.getParamJson())){
                sysDictBO = BeanUtil.copyProperties(JSONUtil.parse(fileDomain.getParamJson()), SysDictBO.class);
            }
            List<SysDictDTO> sysDictDTOList = sysDictBusiness.listSysDictInfo(sysDictBO);
            List<SysDictExportBO> sysDictExportBOList =  new ArrayList<>();
            if(CollectionUtils.isNotEmpty(sysDictDTOList)){
                List<Integer> dictIds = sysDictDTOList.stream().map(SysDictDTO::getId).collect(Collectors.toList());
                List<SysDictItem> sysDictItemList = sysDictItemBusiness.lambdaQuery().in(SysDictItem::getDictId,dictIds).list();
                Map<Integer,List<SysDictItem>> sysDictMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(sysDictItemList)){
                    sysDictMap = sysDictItemList.stream().collect(Collectors.groupingBy(SysDictItem::getDictId));
                }

                for(SysDictDTO sysDictDTO : sysDictDTOList){
                    SysDictExportBO sysDictExportBO = BeanUtil.copyProperties(sysDictDTO, SysDictExportBO.class);
                    sysDictExportBO.setSort(null);
                    sysDictExportBOList.add(sysDictExportBO);
                    sysDictExportBO.setDictType(DictTypeEnum.DICT.getCode());
                    List<SysDictItem> sysItems = sysDictMap.get(sysDictDTO.getId());
                    if(CollectionUtils.isNotEmpty(sysItems)){
                        String dictName = sysDictDTO.getDictName();
                        String dictCode = sysDictDTO.getDictCode();
                        for(SysDictItem sysDictItem : sysItems){
                            sysDictExportBO = new SysDictExportBO();
                            sysDictExportBO.setItemName(sysDictItem.getItemName());
                            sysDictExportBO.setItemValue(sysDictItem.getItemValue());
                            sysDictExportBO.setSort(sysDictItem.getSort());
                            sysDictExportBO.setItemStatus(sysDictItem.getStatus());
                            sysDictExportBO.setRemark(sysDictItem.getRemark());

                            sysDictExportBO.setDictName(dictName);
                            sysDictExportBO.setDictCode(dictCode);
                            sysDictExportBO.setDictType(DictTypeEnum.DICT_ITEM.getCode());
                            sysDictExportBOList.add(sysDictExportBO);
                        }
                    }
                }
            }
            if(CollectionUtils.isEmpty(sysDictExportBOList)){
                fileDomain.setErrorMsg("导出数据不能为空");
                handleProcessingFailedSys(fileDomain);
                return;
            }
            updateFileProcessed(sysDictExportBOList, fileDomain);
        }catch (Exception e){
            fileDomain.setErrorMsg(e.getMessage());
            handleProcessingFailedSys(fileDomain);
        }

    }

}
