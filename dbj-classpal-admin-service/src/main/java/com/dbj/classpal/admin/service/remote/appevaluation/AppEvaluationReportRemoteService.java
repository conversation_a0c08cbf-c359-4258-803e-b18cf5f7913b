package com.dbj.classpal.admin.service.remote.appevaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.evaluation.AdminUserPaperEvaluationApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminUserPaperEvaluationQueryApiBO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationReportQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationQueryPageApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationRemoteService
 * Date:     2025-05-16 16:11:44
 * Description: 表名： ,描述： 表
 */
@Component
public class AppEvaluationReportRemoteService {
    @Resource
    private AdminUserPaperEvaluationApi appUserPaperEvaluationApi;

    /**
     * 查询该评测表关联的所有用户评测报告列表
     * @param pageRequest
     * @return
     * @throws BusinessException
     */
    public Page<AdminUserPaperEvaluationQueryPageApiDTO> pageReports(PageInfo<AdminUserPaperEvaluationQueryApiBO> pageRequest) throws BusinessException{
        RestResponse<Page<AdminUserPaperEvaluationQueryPageApiDTO>> result = appUserPaperEvaluationApi.pageInfo(pageRequest);
        return result.returnProcess(result);
    }

    /**
     * 查询某个评测报告详情
     * @param bo
     * @return
     * @throws BusinessException
     */
    public AdminEvaluationReportQueryApiDTO getReportDetail(CommonIdApiBO bo) throws BusinessException{
        RestResponse<AdminEvaluationReportQueryApiDTO> result = appUserPaperEvaluationApi.getEvaluationReport(bo);
        return result.returnProcess(result);
    }

}
