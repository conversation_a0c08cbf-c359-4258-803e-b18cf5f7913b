package com.dbj.classpal.admin.service.entity.app.config;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Classname AppConfigItem
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 13:51:43
 * @Created by xuezhi
 */
@Data
@TableName("app_config_item")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AppConfigItem extends BizEntity implements Serializable {

    @TableField("type_id")
    @Schema(description = "配置类型ID")
    private Integer typeId;

    @TableField("item_title")
    @Schema(description = "配置项标题")
    private String itemTitle;

    @TableField("item_content")
    @Schema(description = "配置项内容(JSON格式)")
    private String itemContent;

    @TableField("item_url")
    @Schema(description = "链接地址")
    private String itemUrl;

    @TableField("item_image")
    @Schema(description = "图片地址")
    private String itemImage;

    @TableField("item_status")
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer itemStatus;

    @TableField("sort_order")
    @Schema(description = "排序号")
    private Integer sortOrder;

    @TableField("remark")
    @Schema(description = "备注说明")
    private String remark;

    @TableField("version")
    @Schema(description = "版本号")
    private Integer version;
}
    