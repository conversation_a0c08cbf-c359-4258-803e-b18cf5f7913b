package com.dbj.classpal.admin.service.service.sys.dict;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemDetailBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemUpdBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemUpdStatusBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDictItemService
 * @description
 * @date 2025-03-14 15:58
 **/
public interface ISysDictItemService {


    /**
     * 获取单个SysDictItem数据
     * @Title: saveSysDictItem
     * @Description: 添加SysDictItem数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    SysDictItemDTO getSysDictItemInfo(SysDictItemDetailBO reqBo) throws BusinessException;

    /**
     * 添加SysDictItem数据
     * @Title: saveSysDictItem
     * @Description: 添加SysDictItem数据
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean saveSysDictItem(SysDictItemSaveBO bo) throws BusinessException;

    /**
     * 修改数据
     * @Title: UpdateSysDictItem
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    Boolean updateSysDictItem(SysDictItemUpdBO bo) throws BusinessException;


    /**
     * 删除部门数据
     * @Title: delSysDictItemInfo
     * @Description: 添加SysDictItem数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean batchDelSysDictItemInfo(SysDictItemUpdStatusBO reqBo) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description  批量修改状态
     * @Date 2025/3/17 10:57
     * @param
     * @return
     **/
    Boolean batchUpdStatus(SysDictItemUpdStatusBO reqBo) throws BusinessException;


    /**
     * <AUTHOR>
     * @Description  分页获取数据
     * @Date 2025/3/17 8:43
     * @param page
     * @return Page
     **/
    Page<SysDictItemDTO> pageSysDictItemInfo(PageInfo<SysDictItemBO> page) throws BusinessException;

}
