package com.dbj.classpal.admin.service.service.app.config;

import com.dbj.classpal.admin.common.dto.app.config.AppConfigTypeQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Classname AppConfigTypeService
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:45:34
 * @Created by xuezhi
 */
@Mapper
public interface IAppConfigTypeService{
    /**
     * 查询所有APP配置类型
     * @return
     */
    List<AppConfigTypeQueryDTO> getAllAppConfigTypeList();
}
