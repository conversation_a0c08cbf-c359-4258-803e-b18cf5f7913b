package com.dbj.classpal.admin.service.entity.sys.role;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色与菜单权限关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_role_menu")
@Tag(name="SysRoleMenu对象", description="角色与菜单权限关系表")
public class SysRoleMenu extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "菜单id")
    @TableField("menu_id")
    private Integer menuId;

    @Schema(description = "角色id")
    @TableField("role_id")
    private Integer roleId;

   @Schema(description = "版本号")
    @TableField("version")
    private Integer version;



}
