package com.dbj.classpal.admin.service.remote.appebooksconfig;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEbooksConfigWatermarkTemplateApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateEditApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateSaveApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AppEbooksConfigWatermarkTemplateRemoteService {
    @Resource
    private AppEbooksConfigWatermarkTemplateApi appEbooksConfigWatermarkTemplateApi;


    public Page<AppEbooksConfigWatermarkTemplateQueryApiDTO> pageInfo(PageInfo<AppEbooksConfigWatermarkTemplateQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<AppEbooksConfigWatermarkTemplateQueryApiDTO>> result = appEbooksConfigWatermarkTemplateApi.pageInfo(pageRequest);
        return result.returnProcess(result);
    }

    public List<AppEbooksConfigWatermarkTemplateQueryApiDTO> getAll() throws BusinessException {
        RestResponse<List<AppEbooksConfigWatermarkTemplateQueryApiDTO>> result = appEbooksConfigWatermarkTemplateApi.getAll();
        return result.returnProcess(result);
    }


    public Boolean saveEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appEbooksConfigWatermarkTemplateApi.saveEbooksConfigWatermarkTemplate(bo);
        return result.returnProcess(result);
    }

    public Boolean updateEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateEditApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = appEbooksConfigWatermarkTemplateApi.updateEbooksConfigWatermarkTemplate(bo);
        return result.returnProcess(result);
    }

    public Boolean deleteEbooksConfigWatermarkTemplate(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appEbooksConfigWatermarkTemplateApi.deleteEbooksConfigWatermarkTemplate(bo);
        return result.returnProcess(result);
    }
}
