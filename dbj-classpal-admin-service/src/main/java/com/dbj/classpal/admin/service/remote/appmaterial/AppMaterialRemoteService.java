package com.dbj.classpal.admin.service.remote.appmaterial;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.books.client.api.material.AppMaterialApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.material.*;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryDicTreeApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryRootApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialStatisticsSizeApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialRemoteService
 * Date:     2025-04-10 14:25:13
 * Description: 表名： ,描述： 表
 */
@Component
public class AppMaterialRemoteService {

    @Resource
    private AppMaterialApi appMaterialApi;

    /**
     * 分页查询素材中心列表
     * @param pageRequest
     * @return
     */
    public Page<AppMaterialQueryApiDTO> pageInfo(PageInfo<AppMaterialQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<AppMaterialQueryApiDTO>> result = appMaterialApi.pageInfo(pageRequest);
        return result.returnProcess(result);
    }


    public AppMaterialQueryRootApiDTO getRoot() throws BusinessException {
        RestResponse<AppMaterialQueryRootApiDTO> result = appMaterialApi.getRoot();
        return result.returnProcess(result);
    }

    /**
     * 查询素材中心资源是否上传过
     * @param bo
     * @return
     */
    public Boolean materialExist(AppMaterialExistQueryApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appMaterialApi.materialExist(bo);
        return result.returnProcess(result);
    }

    /**
     * 查询素材中心文件夹结构树
     * @return
     */
    public AppMaterialQueryDicTreeApiDTO getAllDirectsTree() throws BusinessException {
        RestResponse<AppMaterialQueryDicTreeApiDTO> result = appMaterialApi.getAllDirectsTree();
        return result.returnProcess(result);
    }


    /**
     * 新建文件夹
     * @param saveMkdirApiBO
     * @return
     */
    public Boolean materialMkdir(AppMaterialSaveMkdirApiBO saveMkdirApiBO) throws BusinessException {
        RestResponse<Boolean> result = appMaterialApi.materialMkdir(saveMkdirApiBO);
        return result.returnProcess(result);
    }

    /**
     * 新建文件夹
     * @param bo
     * @return
     */
    public Boolean renameMaterial(AppMaterialReNameApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appMaterialApi.renameMaterial(bo);
        return result.returnProcess(result);
    }

    /**
     * 移动文件资源
     * @param ioApiBO
     * @return
     */
    public Boolean moveMaterial(AppMaterialIOApiBO ioApiBO) throws BusinessException{
        RestResponse<Boolean> result = appMaterialApi.moveMaterial(ioApiBO);
        return result.returnProcess(result);
    }


    /**
     * 批量移动文件资源
     * @param ioApiBO
     * @return
     */
    public Boolean batchMoveMaterial(AppMaterialBatchIOApiBO ioApiBO) throws BusinessException{
        RestResponse<Boolean> result = appMaterialApi.batchMoveMaterial(ioApiBO);
        return result.returnProcess(result);
    }


    /**
     * 复制文件资源
     * @param ioApiBO
     * @return
     */
    public Boolean copyMaterial(AppMaterialIOApiBO ioApiBO) throws BusinessException{
        RestResponse<Boolean> result = appMaterialApi.copyMaterial(ioApiBO);
        return result.returnProcess(result);
    }

    /**
     * 批量复制文件资源
     * @param ioApiBO
     * @return
     */
    public Boolean batchCopyMaterial(AppMaterialBatchIOApiBO ioApiBO) throws BusinessException{
        RestResponse<Boolean> result = appMaterialApi.batchCopyMaterial(ioApiBO);
        return result.returnProcess(result);
    }

    /**
     * 查询该文件夹父节点列表
     * @param bo
     * @return
     */
    public List<AppMaterialQueryApiDTO> getMaterialParentsPath(BaseIdBO bo) throws BusinessException{
        AppMaterialQueryApiBO apiBO = new AppMaterialQueryApiBO();
        BeanUtil.copyProperties(bo, apiBO);
        RestResponse<List<AppMaterialQueryApiDTO>> result = appMaterialApi.getMaterialParentsPath(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 编辑字幕
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean editCaption(AppMaterialEditCaptionApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = appMaterialApi.editCaption(bo);
        return result.returnProcess(result);
    }


    /**
     * 删除素材
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean deleteMaterial(CommonIdApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appMaterialApi.deleteMaterial(bo);
        return result.returnProcess(result);
    }

    /**
     * 批量删除素材
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean batchDeleteMaterial(AppMaterialBatchCommonIdApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appMaterialApi.batchDeleteMaterial(bo);
        return result.returnProcess(result);
    }

    /**
     * 查询文件已使用大小（kb）
     * @return
     */
    public AppMaterialStatisticsSizeApiDTO usedSize()throws BusinessException {
        RestResponse<AppMaterialStatisticsSizeApiDTO> result = appMaterialApi.usedSize();
        return result.returnProcess(result);
    }

}
