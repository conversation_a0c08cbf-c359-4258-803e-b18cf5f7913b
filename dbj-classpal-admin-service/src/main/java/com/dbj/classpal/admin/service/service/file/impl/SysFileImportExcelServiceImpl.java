package com.dbj.classpal.admin.service.service.file.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.aliyun.core.utils.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.admin.client.enums.sys.file.FileBusinessTypeEnum;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.admin.common.bo.file.importfile.*;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.dbj.classpal.admin.service.mq.constant.ExchangeConstant;
import com.dbj.classpal.admin.service.mq.constant.RoutingKeyConstant;
import com.dbj.classpal.admin.service.remote.appmaterial.AppMaterialRemoteService;
import com.dbj.classpal.admin.service.service.file.ISysFileImportExcelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.mq.bo.MaterialTransParamsBO;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.oss.config.OssConfig;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 导入文件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Service
@Slf4j
public class SysFileImportExcelServiceImpl implements ISysFileImportExcelService {

    @Resource
    private ISysFileImportExcelBusiness systemExcelBusiness;
    @Resource
    private ISysUserBusiness sysUserBusiness;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Resource
    private OssConfig ossConfig;
    @Resource
    private AppMaterialRemoteService materialRemoteService;


    @Override
    public SysFileImportExcelDTO getImportFileInfo(Integer id) throws BusinessException {
        SysFileImportExcel sysFileImportExcel = systemExcelBusiness.getById(id);
        if(sysFileImportExcel == null){
            throw new BusinessException(AdminErrorCode.EXCEL_FILE_NOT_EXIST_CODE,AdminErrorCode.EXCEL_FILE_NOT_EXIST_MSG);
        }
        return BeanUtil.copyProperties(sysFileImportExcel, SysFileImportExcelDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveImportFile(SysFileImportExcelSaveBO saveBO) throws BusinessException {
        SysFileImportExcel domain = new SysFileImportExcel();
        BeanUtils.copyProperties(saveBO, domain);
        domain.setHandleStartTime(LocalDateTime.now());
        domain.setBusinessType(FileBusinessTypeEnum.BUSINESS_NORMAL_FILE.getCode());
        if (systemExcelBusiness.save(domain)) {
            ExcelFileEntity excelFileEntity = new ExcelFileEntity();
            excelFileEntity.setId(domain.getId());
            log.info("保存文件成功，开始发送mq消息：{}", JSON.toJSONString(excelFileEntity));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, ExchangeConstant.CLASSPAL_FILE_EXCHANGE, MessageFormat.format(RoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY, saveBO.getSign()));
                }
            });

        }
        return domain.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveImportMaterialFile(SysFileImportExcelMaterialSaveBO bo) throws Exception {
        SysFileImportExcel domain = new SysFileImportExcel();
        BeanUtils.copyProperties(bo, domain);
        domain.setHandleStartTime(LocalDateTime.now());
        String paramJson = bo.getParamJson();
        if (StringUtils.isEmpty(paramJson)){
            throw new BusinessException("额外参数不能为空");
        }
        Gson gson = new Gson();
        MaterialTransParamsBO transParamsBO = gson.fromJson(bo.getParamJson(), MaterialTransParamsBO.class);
        if (transParamsBO.getMaterialId() == null) {
            throw new BusinessException("所在目录Id不能为空");
        }
        if (transParamsBO.getFileName() == null) {
            throw new BusinessException("转码后素材中心文件名不能为空");
        }
        if (transParamsBO.getSize() == null) {
            throw new BusinessException("文件大小不能为空");
        }
        if (!bo.getFileUrl().contains(ossConfig.getUrlPrefix()) && !bo.getFileUrl().contains(ossConfig.getCdn()) ) {
            throw new BusinessException("源文件文件地址不正确");
        }
        if (systemExcelBusiness.save(domain)) {
            ExcelFileEntity excelFileEntity = new ExcelFileEntity();
            excelFileEntity.setId(domain.getId());
            excelFileEntity.setParamJson(domain.getParamJson());
            log.info("保存文件成功，开始发送mq消息：{}", JSON.toJSONString(excelFileEntity));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, ExchangeConstant.CLASSPAL_FILE_EXCHANGE, RoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_BOOKS_MATERIAL);
                }
            });
        }
        return domain.getId();
    }

    @Override
    public Boolean cancel(BaseIdBO bo) {
        return null;
    }

    @Override
    public Boolean deleteClear(SysFileImportExcelClearBO bo) {
        systemExcelBusiness.lambdaUpdate().in(SysFileImportExcel::getStatus, bo.getStatus())
                .eq(SysFileImportExcel::getCreateBy,ContextUtil.getUserIdInt()).remove();
        return true;
    }

    @Override
    public List<SysFileImportExcelCountDTO> sysFileImportExcelCount() {
        return systemExcelBusiness.sysFileImportExcelCount();
    }

    @Override
    public Page<SysFileImportExcelDTO> pageSysFileImportExcel(PageInfo<SysFileImportExcelBO> page) {
        page.getData().setCreateBy(ContextUtil.getUserIdInt());
        List<Integer> status = page.getData().getStatus();
        if(CollectionUtils.isNotEmpty(status)){
            systemExcelBusiness.lambdaUpdate().in( SysFileImportExcel::getStatus,status)
                    .eq( SysFileImportExcel::getCreateBy,ContextUtil.getUserIdInt())
                    .set(SysFileImportExcel::getIsRead, YesOrNoEnum.YES.getCode()).update();
        }
        return systemExcelBusiness.pageSysFileImportExcel(page);
    }

    @Override
    public List<SysFileImportExcelDTO> getFileInfoTop3(SysFileImportExcelTypeBO bo) {
        List<SysFileImportExcelDTO>  sysFileImportExcelDTOList = systemExcelBusiness.getFileInfoTop3(bo);
        if(CollectionUtils.isNotEmpty(sysFileImportExcelDTOList)){
            List<Integer> userIds = sysFileImportExcelDTOList.stream().map(SysFileImportExcelDTO::getCreateBy).collect(Collectors.toList());
            List<SysUser> sysUserList =  sysUserBusiness.listByIds(userIds);
            if(CollectionUtils.isNotEmpty(sysUserList)){
                Map<Integer,String> userNameMap = sysUserList.stream().collect(Collectors.toMap(SysUser::getId,SysUser::getNickName));
                sysFileImportExcelDTOList.forEach(sysFileImportExcelDTO -> {
                        sysFileImportExcelDTO.setCreateName(userNameMap.get(sysFileImportExcelDTO.getCreateBy()));
                });
            }
        }
        return sysFileImportExcelDTOList;
    }

    @Override
    public List<ExcelFileImportQueryApiDTO> getByAnalysisJobId(ExcelFileImportQueryApiBO bo) {
        return systemExcelBusiness.getByAnalysisJobId(bo);
    }

    @Override
    public List<ExcelFileImportQueryApiDTO> getByTransCodeJobId(ExcelFileImportQueryApiBO bo) {
        return systemExcelBusiness.getByTransCodeJobId(bo);
    }

}
