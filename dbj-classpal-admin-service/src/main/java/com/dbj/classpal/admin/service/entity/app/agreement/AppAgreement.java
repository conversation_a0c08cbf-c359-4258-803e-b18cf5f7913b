package com.dbj.classpal.admin.service.entity.app.agreement;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_agreement")
@Tag(name="APP协议管理对象", description="APP协议管理表")
@AllArgsConstructor
@NoArgsConstructor
public class AppAgreement extends BizEntity implements Serializable  {

    @TableField("agreement_type")
    @Schema(description = "协议类型 1-用户服务协议 2-用户隐私政策 3-儿童隐私政策 4-第三方共享信息清单 5-个人信息收集清单 6-注销账号申请")
    private Integer agreementType;

    @TableField("agreement_title")
    @Schema(description = "协议标题")
    private String agreementTitle;

    @TableField("agreement_content")
    @Schema(description = "协议内容")
    private String agreementContent;

    @TableField("agreement_status")
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer agreementStatus;

    @TableField("version_code")
    @Schema(description = "版本号(三段式)")
    private String versionCode;

    @TableField("publish_time")
    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @TableField("version")
    @Schema(description = "版本号")
    private Integer version;

}
