package com.dbj.classpal.admin.service.remote.appchannel;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.app.client.api.channel.AppChannelClientApi;
import com.dbj.classpal.app.client.bo.channel.*;
import com.dbj.classpal.app.client.dto.channel.ChannelDetailApiDTO;
import com.dbj.classpal.app.client.dto.version.VersionChannelApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 14:52:00
 */
@Component
public class AppChannelRemoteService {
    @Resource
    private AppChannelClientApi appChannelClientApi;

    public Page<ChannelDetailApiDTO> pageChannels(PageInfo<ChannelQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<ChannelDetailApiDTO>> result = appChannelClientApi.pageChannels(pageRequest);
        return result.returnProcess(result);
    }

    public Boolean saveChannel(ChannelSaveApiBO channel) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.saveChannel(channel);
        return result.returnProcess(result);
    }


    public Boolean updateChannel(ChannelUpdateApiBO channel) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.updateChannel(channel);
        return result.returnProcess(result);
    }

    public Boolean deleteChannel(ChannelIdApiBO channelId) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.deleteChannel(channelId);
        return result.returnProcess(result);
    }

    public Boolean enableChannel(ChannelIdApiBO channelId) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.enableChannel(channelId);
        return result.returnProcess(result);
    }

    public Boolean disableChannel(ChannelIdApiBO channelId) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.disableChannel(channelId);
        return result.returnProcess(result);
    }

    public VersionChannelApiDTO getQrCode(BaseIdBO bo) throws BusinessException {
        RestResponse<VersionChannelApiDTO> result = appChannelClientApi.getQrCode(bo.getId());
        return result.returnProcess(result);
    }


    public ChannelDetailApiDTO getChannelDetail(ChannelIdApiBO channelId) throws BusinessException {
        RestResponse<ChannelDetailApiDTO> result = appChannelClientApi.getChannelDetail(channelId);
        return result.returnProcess(result);
    }

    public List<ChannelDetailApiDTO> batchGetChannelDetails(ChannelIdsApiBO channelIds) throws BusinessException {
        RestResponse<List<ChannelDetailApiDTO>> result = appChannelClientApi.batchGetChannelDetails(channelIds);
        return result.returnProcess(result);
    }

    public Boolean batchEnable(ChannelIdsApiBO ids) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.batchEnable(ids);
        return result.returnProcess(result);
    }

    public Boolean batchDisable(ChannelIdsApiBO ids) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.batchDisable(ids);
        return result.returnProcess(result);
    }

    public Boolean batchDelete(ChannelIdsApiBO ids) throws BusinessException {
        RestResponse<Boolean> result = appChannelClientApi.batchDelete(ids);
        return result.returnProcess(result);
    }
}
