package com.dbj.classpal.admin.service.mapper.app.config;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.app.config.AppConfigSharePosterApiQueryBO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigSharePosterApiQueryDTO;
import com.dbj.classpal.admin.common.bo.app.config.AppConfigItemSharePosterQueryBO;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname AppConfigItemMapper
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:15:28
 * @Created by xuezhi
 */
@Mapper
public interface AppConfigItemMapper extends BaseMapper<AppConfigItem> {
    /**
     * 分页查询海报列表
     * @param page
     * @return
     */

    Page<AppConfigItemQueryDTO> pageSharePosterInfo(Page page, @Param("bo") AppConfigItemSharePosterQueryBO bo);

    /**
     * 分页查询海报列表-Api端
     * @param page
     * @return
     */
    Page<AppConfigSharePosterApiQueryDTO> apiPageSharePosterInfo(Page page, @Param("bo") AppConfigSharePosterApiQueryBO bo);
}
