package com.dbj.classpal.admin.service.biz.file.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelBO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.admin.service.mapper.file.SysFileExportExcelMapper;
import com.dbj.classpal.admin.service.biz.file.ISysFileExportExcelBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 导出文件记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Service
public class SysFileExportExcelBusinessImpl extends ServiceImpl<SysFileExportExcelMapper, SysFileExportExcel> implements ISysFileExportExcelBusiness {


    @Override
    public Page<SysFileExportExcelDTO> pageSysFileExportExcel(PageInfo<SysFileExportExcelBO> page) {
        return baseMapper.pageSysFileExportExcel(page.getPage(),page.getData());
    }

    @Override
    public List<SysFileExportExcelCountDTO> sysFileExportExcelCount() {
        return baseMapper.sysFileExportExcelCount(ContextUtil.getUserIdInt());
    }
}
