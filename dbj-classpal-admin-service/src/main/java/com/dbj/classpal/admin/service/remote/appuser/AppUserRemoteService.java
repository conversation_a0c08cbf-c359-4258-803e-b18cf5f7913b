package com.dbj.classpal.admin.service.remote.appuser;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.api.user.AppUserClientApi;
import com.dbj.classpal.app.client.bo.user.*;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.app.client.dto.user.UserDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:23:01
 */
@Slf4j
@Component
public class AppUserRemoteService {
    @Resource
    private AppUserClient<PERSON>pi appUserClientApi;

    public void updateUserGuide(UserGuideApiBO userGuide) throws BusinessException {
        RestResponse<Void> result = appUserClientApi.updateUserGuide(userGuide);
        result.returnProcess(result);
    }

    public CurrentUserApiDTO getCurrentUser(UserIdApiBO userIdBO) throws BusinessException {
        RestResponse<CurrentUserApiDTO> result =  appUserClientApi.getCurrentUser(userIdBO);
        return result.returnProcess(result);
    }

    public Page<UserDetailApiDTO> pageUsers(PageInfo<UserQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<UserDetailApiDTO>> result = appUserClientApi.pageUsers(pageRequest);
        return result.returnProcess(result);
    }

    public UserDetailApiDTO getUserDetail(UserIdApiBO userIdBO) throws BusinessException {
        RestResponse<UserDetailApiDTO> result = appUserClientApi.getUserDetail(userIdBO);
        return result.returnProcess(result);
    }
    public void enableAccount(DisableAccountApiBO disableAccount) throws BusinessException {
        RestResponse<Void> result = appUserClientApi.enableAccount(disableAccount);
        result.returnProcess(result);
    }

    public void disableAccount(DisableAccountApiBO disableAccount) throws BusinessException {
        RestResponse<Void> result =appUserClientApi.disableAccount(disableAccount);
        result.returnProcess(result);
    }

    public void cancelAccount(CancelAccountApiBO cancelAccount) throws BusinessException {
        RestResponse<Void> result =appUserClientApi.cancelAccount(cancelAccount);
        result.returnProcess(result);
    }


    public UserDetailApiDTO exactMatch(UserExactMatchApiBo request) throws BusinessException {
        RestResponse<UserDetailApiDTO> result = appUserClientApi.exactMatch(request);
        return result.returnProcess(result);
    }
}