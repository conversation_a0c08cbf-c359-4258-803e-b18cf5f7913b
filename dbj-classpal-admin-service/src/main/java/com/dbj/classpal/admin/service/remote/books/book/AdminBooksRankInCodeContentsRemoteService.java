package com.dbj.classpal.admin.service.remote.books.book;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.books.AdminBooksRankInCodeContentsApi;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodeContentsUpdForceApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsMoveBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsSaveBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsTreeBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsUpdBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsUpdTypeBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsTreeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@Component
public class AdminBooksRankInCodeContentsRemoteService {

    @Resource
    private AdminBooksRankInCodeContentsApi adminBooksRankInCodeContentsApi;

   public List<BooksRankInCodesContentsTreeDTO> list(BooksRankInCodesContentsTreeBO boardBooksRankClassifyBO) throws BusinessException{
        RestResponse<List<BooksRankInCodesContentsTreeDTO>> result = adminBooksRankInCodeContentsApi.list(boardBooksRankClassifyBO);
        return result.returnProcess(result);
    }
    public Page<BooksRankInCodesContentsPageDTO> page(@RequestBody PageInfo<BooksRankInCodesContentsPageBO> pageInfo) throws BusinessException{
        RestResponse<Page<BooksRankInCodesContentsPageDTO>>  result = adminBooksRankInCodeContentsApi.page(pageInfo);
        return result.returnProcess(result);
    }
    public Integer save(BooksRankInCodesContentsSaveBO booksRankInCodesContentsSaveBO) throws BusinessException{
        RestResponse<Integer>  result = adminBooksRankInCodeContentsApi.save(booksRankInCodesContentsSaveBO);
        return result.returnProcess(result);
    }
    public Boolean update(BooksRankInCodesContentsUpdBO booksRankInCodesContentsUpdBO) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsApi.update(booksRankInCodesContentsUpdBO);
        return result.returnProcess(result);
    }
    public Boolean delete(Integer id) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsApi.delete(id);
        return result.returnProcess(result);
    }
    public Boolean updateType(BooksRankInCodesContentsUpdTypeBO bo) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsApi.updateType(bo);
        return result.returnProcess(result);
    }


    public BooksRankInCodesContentsDetailDTO detail(@RequestParam Integer id) throws BusinessException{
        RestResponse<BooksRankInCodesContentsDetailDTO>  result = adminBooksRankInCodeContentsApi.detail(id);
        return result.returnProcess(result);
    }

    public Boolean forcePromotionUrl(BooksRankInCodeContentsUpdForceApiBO bo) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsApi.forcePromotionUrl(bo);
        return result.returnProcess(result);
    }

    public Boolean move(BooksRankInCodesContentsMoveBO bo) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsApi.move(bo);
        return result.returnProcess(result);
    }



}
