package com.dbj.classpal.admin.service.service.sys.menu;


import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuSaveBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuTypeBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdIsRefreshBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdVisibleBO;
import com.dbj.classpal.admin.common.dto.sys.menu.SysMenuDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 用户菜单相关接口
 * @Date 2025/3/17 14:36 
 **/
public interface SysMenuService {

	SysMenuDTO getSysMenuInfo(Integer id) throws BusinessException;
	
	Boolean saveSysMenu(SysMenuSaveBO sysMenuSaveBO) throws BusinessException;

	/**
	 * <AUTHOR>
	 * @Description  修改SysMenu
	 * @Date 2025/3/17 14:34 
	 * @param sysMenuUpdBO 修改的信息
	 * @return Boolean 是否成功
	 **/
	Boolean updateSysMenu(SysMenuUpdBO sysMenuUpdBO) throws BusinessException;

	/**
	 * <AUTHOR>
	 * @Description  修改显示状态
	 * @Date 2025/3/17 14:32 
	 * @param bo 修改的状态参数
	 * @return Boolean 是否成功
	 **/
	Boolean updateSysMenuVisible(SysMenuUpdVisibleBO bo) throws BusinessException;

	/**
	 * <AUTHOR>
	 * @Description  修改刷新状态
	 * @Date 2025/3/17 14:31
	 * @param bo 刷新状态入参
	 * @return Boolean 是否成功
	 **/
	Boolean updateSysMenuIsRefresh(SysMenuUpdIsRefreshBO bo) throws BusinessException;

	/**
	 * <AUTHOR>
	 * @Description  修改刷新状态
	 * @Date 2025/3/17 14:31
	 * @param bo 刷新状态入参
	 * @return Boolean 是否成功
	 **/
	Boolean delSysMenu(BaseIdBO bo) throws BusinessException;

	/**
     * 查找SysMenu
     * 
     * @return SysMenuDTO
     */
	List<SysMenuDTO> getSysMenuAll();


	/**
	 * 根据菜单类型获取菜单
	 * @Title: getSysMenuByMenuType
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @return
	 * @date: 2022年10月20日
	 * @throws
	 */
	List<SysMenuDTO> getSysMenuByMenuType();

	List<SysMenuDTO> getSysUserMenuAuth(Integer userId) throws BusinessException;
}