package com.dbj.classpal.admin.service.biz.sys.help.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsBO;
import com.dbj.classpal.admin.common.dto.help.SysHelpItemsDTO;
import com.dbj.classpal.admin.service.entity.sys.help.SysHelpItems;
import com.dbj.classpal.admin.service.mapper.sys.help.SysHelpItemsMapper;
import com.dbj.classpal.admin.service.biz.sys.help.ISysHelpItemsBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Service
public class SysHelpItemsBusinessImpl extends ServiceImpl<SysHelpItemsMapper, SysHelpItems> implements ISysHelpItemsBusiness {

    @Resource
    private SysHelpItemsMapper sysHelpItemsMapper;

    @Override
    public Page<SysHelpItemsDTO> pageSysHelpItems(PageInfo<SysHelpItemsBO> bo) {
        return sysHelpItemsMapper.pageSysHelpItems(bo.getPage(), bo.getData());
    }
}
