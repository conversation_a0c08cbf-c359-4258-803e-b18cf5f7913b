package com.dbj.classpal.admin.service.remote.appebooksconfig;

import com.dbj.classpal.books.client.api.ebooks.AppEbooksConfigCategoryApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategorySaveApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryUpdateApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigCategoryQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AppEbooksConfigCategoryRemoteService {
    @Resource
    private AppEbooksConfigCategoryApi appEbooksConfigCategoryApi;


    public List<AppEbooksConfigCategoryQueryApiDTO> getAllCategory(AppEbooksConfigCategoryQueryApiBO bo) throws BusinessException {
        RestResponse<List<AppEbooksConfigCategoryQueryApiDTO>> result = appEbooksConfigCategoryApi.getAllCategory(bo);
        return result.returnProcess(result);
    }


    public Boolean saveCategory(AppEbooksConfigCategorySaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appEbooksConfigCategoryApi.saveCategory(bo);
        return result.returnProcess(result);
    }

    public Boolean updateCategory(AppEbooksConfigCategoryUpdateApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = appEbooksConfigCategoryApi.updateCategory(bo);
        return result.returnProcess(result);
    }

    public Boolean deleteCategory(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = appEbooksConfigCategoryApi.deleteCategory(bo);
        return result.returnProcess(result);
    }
}
