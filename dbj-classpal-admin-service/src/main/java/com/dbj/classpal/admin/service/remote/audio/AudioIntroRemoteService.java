package com.dbj.classpal.admin.service.remote.audio;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.audio.AudioIntroApi;
import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioIntroDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2025/6/30 14:28
 */
@Component
@RequiredArgsConstructor
public class AudioIntroRemoteService {

    private final AudioIntroApi audioIntroApi;

    public Page<AudioIntroDTO> page(PageInfo<AudioIntroQueryBO> pageInfo) throws BusinessException {
        RestResponse<Page<AudioIntroDTO>> response = audioIntroApi.page(pageInfo);
        return response.returnProcess(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean save(AudioIntroSaveBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioIntroApi.save(bo);
        return response.returnProcess(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean move(AudioIntroMoveBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioIntroApi.move(bo);
        return response.returnProcess(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(AudioIntroDelBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioIntroApi.remove(bo);
        return response.returnProcess(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean copy(AudioIntroCopyBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioIntroApi.copy(bo);
        return response.returnProcess(response);
    }

    public AudioIntroDTO getDetails(AudioIntroIdBO bo) throws BusinessException {
        RestResponse<AudioIntroDTO> response = audioIntroApi.getDetails(bo);
        return response.returnProcess(response);
    }
}
