package com.dbj.classpal.admin.service.entity.sys.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user")
@Tag(name = "SysUser对象", description="用户表")
public class SysUser extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "手机号码")
    @TableField("phone")
    private String phone;

    @Schema(description = "用户昵称")
    @TableField("nick_name")
    private String nickName;

    @Schema(description = "用户邮箱")
    @TableField("email")
    private String email;

    @Schema(description = "用户性别 0-男 1-女")
    @TableField("sex")
    private Boolean sex;

    @Schema(description = "头像路径")
    @TableField("avatar")
    private String avatar;

    @Schema(description = "帐号")
    @TableField("accounts")
    private String accounts;
    @Schema(description = "帐号状态 0禁用 1启用")
    @TableField("accounts_status")
    private Integer accountsStatus;

    @Schema(description = "密码")
    @TableField("password")
    private String password;

    @Schema(description = "盐加密")
    @TableField("salt")
    private String salt;

    @Schema(description = "最后登录时间")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    @Schema(description = "是否强制修改密码 0否 1是")
    @TableField("is_force")
    private Integer isForce;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    @Schema(description = "工作流签名")
    @TableField("signature")
    private String signature;


    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;



}
