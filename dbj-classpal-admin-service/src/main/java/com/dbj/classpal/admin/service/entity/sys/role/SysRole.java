package com.dbj.classpal.admin.service.entity.sys.role;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_role")
@Tag(name ="SysRole对象", description="角色信息表")
public class SysRole extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "角色名称")
    @TableField("name")
    private String name;

    @Schema(description = "角色描述")
    @TableField("describes")
    private String describes;

    @Schema(description = "角色权限字符串")
    @TableField("role_code")
    private String roleCode;

    @Schema(description = "显示顺序")
    @TableField("role_sort")
    private Integer roleSort;

    @Schema(description = "备注")
    @TableField("remark")
    private String remark;


    @Schema(description = "角色状态 0 禁用 1 启用")
    @TableField("role_status")
    private Integer roleStatus;

    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;



}
