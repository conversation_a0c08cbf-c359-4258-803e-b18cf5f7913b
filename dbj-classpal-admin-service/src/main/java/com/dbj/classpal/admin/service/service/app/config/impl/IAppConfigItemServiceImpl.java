package com.dbj.classpal.admin.service.service.app.config.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.enums.app.config.AppConfigTypeEnum;
import com.dbj.classpal.admin.common.bo.app.config.*;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.admin.common.enums.StatusEnum;
import com.dbj.classpal.admin.service.biz.app.config.IAppConfigItemBiz;
import com.dbj.classpal.admin.service.biz.app.config.IAppConfigTypeBiz;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigItem;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigType;
import com.dbj.classpal.admin.service.service.app.config.IAppConfigItemService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname IAppConfigItemServiceImpl
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:50:51
 * @Created by xuezhi
 */
@Service
public class IAppConfigItemServiceImpl implements IAppConfigItemService {
    @Autowired
    private IAppConfigItemBiz business;
    @Autowired
    private IAppConfigTypeBiz typeBusiness;

    @Override
    public AppConfigItemQueryDTO getAppConfigItemById(Integer id) throws BusinessException {
        AppConfigItemQueryDTO dto = new AppConfigItemQueryDTO();
        if (id == null || StringUtils.isBlank(id.toString())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem appConfigItem = business.getById(id);
        if (ObjectUtils.isEmpty(appConfigItem)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        BeanUtil.copyProperties(appConfigItem, dto);
        StatusEnum statusEnum = StatusEnum.getByCode(dto.getItemStatus());
        if (statusEnum != null) {
            dto.setItemStatusStr(statusEnum.getName());
        }
        return dto;
    }

    @Override
    public AppConfigItemQueryDTO getAppConfigItemByTypeCode(String typeCode) throws BusinessException {
        AppConfigType one = typeBusiness.lambdaQuery().eq(AppConfigType::getTypeCode, typeCode).one();
        if (ObjectUtils.isEmpty(one)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_TYPE_EXIST_CODE, AdminErrorCode.APP_CONFIG_TYPE_EXIST_MSG);
        }
        AppConfigItemQueryDTO dto = new AppConfigItemQueryDTO();
        List<AppConfigItem> list = business.lambdaQuery().eq(AppConfigItem::getTypeId, one.getId()).list();
        if (list.size() > 1){
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_ACCEPT_RESULT_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_ACCEPT_RESULT_ERROR_MSG);
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        BeanUtil.copyProperties(list.get(0), dto);
        StatusEnum statusEnum = StatusEnum.getByCode(dto.getItemStatus());
        if (statusEnum != null) {
            dto.setItemStatusStr(statusEnum.getName());
        }
        return dto;
    }

    @Override
    public List<AppConfigItemQueryDTO> getAppConfigItemListByTypeCode(String typeCode) throws BusinessException {
        AppConfigType one = typeBusiness.lambdaQuery().eq(AppConfigType::getTypeCode, typeCode).one();
        if (ObjectUtils.isEmpty(one)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_TYPE_EXIST_CODE, AdminErrorCode.APP_CONFIG_TYPE_EXIST_MSG);
        }
        List<AppConfigItem> list = business.lambdaQuery().eq(AppConfigItem::getTypeId, one.getId()).list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().sorted(Comparator.comparing(AppConfigItem::getSortOrder).reversed()).map(dto -> {
            AppConfigItemQueryDTO appConfigItemQueryDTO = new AppConfigItemQueryDTO();
            BeanUtil.copyProperties(dto, appConfigItemQueryDTO);
            StatusEnum statusEnum = StatusEnum.getByCode(dto.getItemStatus());
            if (statusEnum != null) {
                appConfigItemQueryDTO.setItemStatusStr(statusEnum.getName());
            }
            return appConfigItemQueryDTO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveTogetherStudyGroup(AppConfigItemTogetherStudyGroupSaveBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || bo.getTypeId() == null) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItemQueryDTO dto = this.getAppConfigItemByTypeCode(AppConfigTypeEnum.TOGETHER_STUDY_GROUP.getCode());
        if (ObjectUtils.isNotEmpty(dto)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_EXIST_MSG);
        }
        AppConfigItem saveBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, saveBO);
        saveBO.setItemStatus(StatusEnum.AGREEMENT_STATUS_YES.getCode());
        if (!business.save(saveBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_SAVE_FAIL_CODE, AdminErrorCode.APP_COMMON_SAVE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTogetherStudyGroup(AppConfigItemTogetherStudyGroupUpdateBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || ObjectUtils.isEmpty(bo.getId())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem byId = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        AppConfigItem updateBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, updateBO);
        if (!business.updateById(updateBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveFeedBack(AppConfigItemFeedBackSaveBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo)|| bo.getTypeId() == null) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItemQueryDTO dto = this.getAppConfigItemByTypeCode(AppConfigTypeEnum.FEEDBACK_SUGGESTIONS.getCode());
        if (ObjectUtils.isNotEmpty(dto)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_EXIST_MSG);
        }
        AppConfigItem saveBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, saveBO);
        saveBO.setItemStatus(StatusEnum.AGREEMENT_STATUS_YES.getCode());
        if (!business.save(saveBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_SAVE_FAIL_CODE, AdminErrorCode.APP_COMMON_SAVE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFeedBack(AppConfigItemFeedBackUpdateBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || ObjectUtils.isEmpty(bo.getId())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem byId = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        AppConfigItem updateBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, updateBO);
        if (!business.updateById(updateBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCustomer(AppConfigItemCustomerSaveBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || bo.getTypeId() == null) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItemQueryDTO dto = this.getAppConfigItemByTypeCode(AppConfigTypeEnum.CUSTOMER.getCode());
        if (ObjectUtils.isNotEmpty(dto)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_EXIST_MSG);
        }
        AppConfigItem saveBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, saveBO);
        saveBO.setItemStatus(StatusEnum.AGREEMENT_STATUS_YES.getCode());
        if (!business.save(saveBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_SAVE_FAIL_CODE, AdminErrorCode.APP_COMMON_SAVE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCustomer(AppConfigItemCustomerUpdateBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || ObjectUtils.isEmpty(bo.getId())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem byId = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        AppConfigItem updateBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, updateBO);
        if (!business.updateById(updateBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSharePoster(AppConfigItemSharePosterSaveBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || bo.getTypeId() == null) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem saveBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, saveBO);
        if (!business.save(saveBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_SAVE_FAIL_CODE, AdminErrorCode.APP_COMMON_SAVE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSharePoster(AppConfigItemSharePosterUpdateBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo) || ObjectUtils.isEmpty(bo.getId())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem byId = business.getById(bo.getId());
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        if (byId.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_NO.getCode())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_SHARE_POSTER_UPDATE_CODE, AdminErrorCode.APP_CONFIG_ITEM_SHARE_POSTER_UPDATE_MSG);
        }
        AppConfigItem updateBO = new AppConfigItem();
        BeanUtil.copyProperties(bo, updateBO);
        if (!business.updateById(updateBO)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSharePosterStatus(Integer id) throws BusinessException {
        if (ObjectUtils.isEmpty(id)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        AppConfigItem byId = business.getById(id);
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        if (byId.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_YES.getCode())) {
            byId.setItemStatus(StatusEnum.AGREEMENT_STATUS_NO.getCode());
        }else{
            byId.setItemStatus(StatusEnum.AGREEMENT_STATUS_YES.getCode());
        }
        if (!business.updateById(byId)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    public Page<AppConfigItemQueryDTO> PageSharePoster(PageInfo<AppConfigItemSharePosterQueryBO> page) {
        return business.pageSharePosterInfo(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delSharePoster(Integer id) throws BusinessException {
        AppConfigItem byId = business.getById(id);
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_CODE, AdminErrorCode.APP_CONFIG_ITEM_NOT_EXIST_MSG);
        }
        if (byId.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_YES.getCode())) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_SHARE_POSTER_DEL_CODE, AdminErrorCode.APP_CONFIG_ITEM_SHARE_POSTER_DEL_MSG);
        }
        if (!business.removeById(byId)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_DELETE_FAIL_CODE, AdminErrorCode.APP_COMMON_DELETE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSharePosterEnableStatusList(AppConfigItemCommonQueryBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        boolean update = business.lambdaUpdate().in(AppConfigItem::getId, bo.getIds()).set(AppConfigItem::getItemStatus, StatusEnum.AGREEMENT_STATUS_YES.getCode()).update();
        if (!update){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSharePosterDisableStatusList(AppConfigItemCommonQueryBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        boolean update = business.lambdaUpdate().in(AppConfigItem::getId, bo.getIds()).set(AppConfigItem::getItemStatus, StatusEnum.AGREEMENT_STATUS_NO.getCode()).update();
        if (!update){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delSharePosterList(AppConfigItemCommonQueryBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo)) {
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_CODE, AdminErrorCode.APP_CONFIG_ITEM_PARAM_ERROR_MSG);
        }
        int count = business.lambdaQuery().in(AppConfigItem::getId, bo.getIds()).eq(AppConfigItem::getItemStatus, StatusEnum.AGREEMENT_STATUS_YES.getCode()).count().intValue();
        if (count>0){
            throw new BusinessException(AdminErrorCode.APP_CONFIG_ITEM_SHARE_POSTER_LIST_DEL_CODE, AdminErrorCode.APP_CONFIG_ITEM_SHARE_POSTER_LIST_DEL_MSG);
        }
        if (!business.removeByIds(bo.getIds())){
            throw new BusinessException(AdminErrorCode.APP_COMMON_DELETE_FAIL_CODE, AdminErrorCode.APP_COMMON_DELETE_FAIL_MSG);
        }
        return true;
    }

}
    