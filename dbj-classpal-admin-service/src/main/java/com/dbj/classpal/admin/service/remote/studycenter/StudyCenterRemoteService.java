package com.dbj.classpal.admin.service.remote.studycenter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.studycenter.StudyCenterApi;
import com.dbj.classpal.books.client.bo.studycenter.*;
import com.dbj.classpal.books.client.dto.studycenter.AppStudyModuleDetailApiDTO;
import com.dbj.classpal.books.client.dto.studycenter.AppStudyModuleListApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
@Component
public class StudyCenterRemoteService {
    @Resource
    StudyCenterApi studyCenterApi;

    public Boolean createModule(AppStudyModuleCreateApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.createModule(bo);
        return result.returnProcess(result);
    }

    public Boolean updateModule(AppStudyModuleUpdateApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.updateModule(bo);
        return result.returnProcess(result);
    }

    public Boolean deleteModule(AppStudyModuleIdsApiBO idsApiBO) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.deleteModule(idsApiBO);
        return result.returnProcess(result);
    }

    public Page<AppStudyModuleListApiDTO> pageModule(PageInfo<AppStudyModuleQueryPageApiBO> pageInfo) throws BusinessException {
        RestResponse<Page<AppStudyModuleListApiDTO>> result = studyCenterApi.pageModule(pageInfo);
        return result.returnProcess(result);
    }

    public AppStudyModuleDetailApiDTO getModuleDetail(AppStudyModuleIdApiBO idApiBO) throws BusinessException{
        RestResponse<AppStudyModuleDetailApiDTO> result = studyCenterApi.getModuleDetail(idApiBO);
        return result.returnProcess(result);
    }


    public Boolean batchPublish(AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.batchPublish(idsApiBo);
        return result.returnProcess(result);
    }

    public Boolean batchUnpublish(AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.batchUnpublish(idsApiBo);
        return result.returnProcess(result);
    }

    public Boolean batchShow(AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.batchShow(idsApiBo);
        return result.returnProcess(result);
    }

    public Boolean batchHide(AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        RestResponse<Boolean> result = studyCenterApi.batchHide(idsApiBo);
        return result.returnProcess(result);
    }
}