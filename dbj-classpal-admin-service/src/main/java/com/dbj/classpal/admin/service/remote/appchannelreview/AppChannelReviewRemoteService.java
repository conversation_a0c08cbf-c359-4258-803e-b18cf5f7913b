package com.dbj.classpal.admin.service.remote.appchannelreview;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.api.channel.AppChannelReviewClientApi;
import com.dbj.classpal.app.client.bo.channel.*;
import com.dbj.classpal.app.client.dto.channel.ChannelReviewApiDTO;
import com.dbj.classpal.app.client.dto.channel.ReviewDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:21:18
 */
@Component
public class AppChannelReviewRemoteService {
    @Resource
    private AppChannelReviewClientApi channelReviewClientApi;

    public Page<ReviewDetailApiDTO> pageReviews(PageInfo<ReviewQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<ReviewDetailApiDTO>> result = channelReviewClientApi.pageReviews(pageRequest);
        return result.returnProcess(result);
    }

    public List<ChannelReviewApiDTO> list() throws BusinessException {
        RestResponse<List<ChannelReviewApiDTO>> result = channelReviewClientApi.list();
        return result.returnProcess(result);
    }


    public Boolean saveReview(ReviewSaveApiBO review) throws BusinessException {
        RestResponse<Boolean> result =  channelReviewClientApi.saveReview(review);
        return result.returnProcess(result);
    }

    public Boolean updateReview(ReviewUpdateApiBO review) throws BusinessException {
        RestResponse<Boolean> result = channelReviewClientApi.updateReview(review);
        return result.returnProcess(result);

    }

    public ReviewDetailApiDTO getReviewDetail(ReviewIdApiBO reviewId) throws BusinessException {
        RestResponse<ReviewDetailApiDTO> result = channelReviewClientApi.getReviewDetail(reviewId);
        return result.returnProcess(result);
    }

    public List<ReviewDetailApiDTO> batchGetReviewDetails(ReviewIdsApiBO reviewIds) throws BusinessException {
        RestResponse<List<ReviewDetailApiDTO>> result =  channelReviewClientApi.batchGetReviewDetails(reviewIds);
        return result.returnProcess(result);
    }

}
