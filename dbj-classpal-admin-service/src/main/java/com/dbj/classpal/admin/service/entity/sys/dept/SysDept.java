package com.dbj.classpal.admin.service.entity.sys.dept;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dept")
@Tag(name="SysDept对象", description="部门表")
public class SysDept extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "父部门id")
    @TableField("father_id")
    private Integer fatherId;

    @Schema(description = "部门名称")
    @TableField("dept_name")
    private String deptName;

    @Schema(description = "显示顺序")
    @TableField("order_num")
    private Integer orderNum;


    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;


}
