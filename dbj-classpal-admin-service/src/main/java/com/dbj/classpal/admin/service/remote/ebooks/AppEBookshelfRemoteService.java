package com.dbj.classpal.admin.service.remote.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookshelfApi;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookshelfApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class AppEBookshelfRemoteService {
    @Resource
    private AppEBookshelfApi bookshelfApi;
    
    public Page<AppEBookshelfApiDTO> page(PageInfo<AppEBookshelfQueryApiBO> pageRequest) throws BusinessException{
        RestResponse<Page<AppEBookshelfApiDTO>> result = bookshelfApi.page(pageRequest);
        return result.returnProcess(result);
    }

    public AppEBookshelfApiDTO detail(AppEBookshelfIdApiBO idBO) throws BusinessException{
        RestResponse<AppEBookshelfApiDTO> result = bookshelfApi.detail(idBO);
        return result.returnProcess(result);
    }

    public Integer save(AppEBookshelfSaveApiBO saveBO) throws BusinessException{
        RestResponse<Integer> result = bookshelfApi.save(saveBO);
        return result.returnProcess(result);
    }

    public Boolean update(AppEBookshelfUpdateApiBO saveBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.update(saveBO);
        return result.returnProcess(result);
    }

    public Boolean delete(AppEBookshelfIdApiBO idBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.delete(idBO);
        return result.returnProcess(result);
    }


    public Boolean deleteBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.deleteBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean enableBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.enableBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean disableBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.disableBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean allowDownloadBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.allowDownloadBatch(idsBO);
        return result.returnProcess(result);
    }


    public Boolean disableDownloadBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookshelfApi.disableDownloadBatch(idsBO);
        return result.returnProcess(result);
    }
}