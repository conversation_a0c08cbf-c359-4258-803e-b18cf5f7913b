package com.dbj.classpal.admin.service.mq.listener.file.exports;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dbj.classpal.admin.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.admin.common.enums.file.ExportExcelFileStrategyEnum;
import com.dbj.classpal.admin.service.biz.file.ISysFileExportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.admin.service.mq.constant.QueueConstant;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.file.ExcelFileStrategyFactory;
import com.dbj.classpal.framework.utils.file.IExportExcelFileStrategy;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileListener
 * @date 2023-10-27 10:42
 **/
@Component
@Slf4j
public class ExcelFileExportListener {

    @Resource
    private ISysFileExportExcelBusiness sysFileExportBusiness;

    @RabbitListener(queues = {QueueConstant.CLASSPAL_EXPORT_FILE_SERVICE_QUEUE_ADMIN})
    @ExtractHeader
    public void handleOrder(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException {
        SysFileExportExcel fileDomain = null;
        try {
            log.info("文件上传接收到消息:{}", excelFileEntity);
            if(excelFileEntity == null || excelFileEntity .getId() == null){
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            fileDomain = sysFileExportBusiness.getById(excelFileEntity.getId());
            log.info("查询文件信息{}", JSON.toJSONString(fileDomain));
            if(fileDomain == null || !Objects.equals(fileDomain.getStatus(), FileStatusEnum.PENDING_PROCESSING.getCode())){
                return;
            }
            SysFileExportExcel subFileDomain = new SysFileExportExcel();
            subFileDomain.setId(fileDomain.getId());
            subFileDomain.setHandleStartTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING.getCode());
            log.info("更改的文件信息:{}", JSONObject.toJSONString(subFileDomain));
            sysFileExportBusiness.updateById(subFileDomain);

            String type = fileDomain.getType();
            SysFileExportExcel errFileDomain = new SysFileExportExcel();
            errFileDomain.setId(fileDomain.getId());
            if(type == null){
                errFileDomain.setErrorMsg("找不到导出类型,请处理");
                errFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
                sysFileExportBusiness.updateById(errFileDomain);
                return;

            }
            ExportExcelFileStrategyEnum strategyEnum = ExportExcelFileStrategyEnum.getEnum(type);
            if(strategyEnum == null){
                errFileDomain.setErrorMsg("根据类型找不到对应枚举,请检查ExcelFileStrategyEnum枚举信息");
                errFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
                sysFileExportBusiness.updateById(errFileDomain);
                return;
            }
            IExportExcelFileStrategy fileStrategy = ExcelFileStrategyFactory.getExportStrategy(strategyEnum.getHandler());
            fileStrategy.handlerExportExcelFile(BeanUtil.copyProperties(fileDomain, SysFileExportExcelBO.class));
        } catch (Exception e) {
            log.error(e.getMessage());

        }finally {
            channel.basicAck(tag, false);  //
        }
    }

}
