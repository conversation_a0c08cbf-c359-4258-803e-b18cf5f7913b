package com.dbj.classpal.admin.service.mq.listener.file.imports.handle;

import com.dbj.classpal.admin.common.bo.sys.dict.SysDictImportBO;
import com.dbj.classpal.admin.common.enums.dict.DictTypeEnum;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.file.ExcelFileStrategy;
import com.dbj.classpal.framework.utils.param.ExportExcelParam;
import com.dbj.classpal.framework.utils.util.ImportExcelUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19 13:56:37
 */
@Service("sysDictExcelFileStrategy")
@Slf4j
public class SysDictExcelFileStrategy extends AdminExcelFileStrategy<SysDictImportBO> {


    @Resource
    private ISysDictBusiness sysDictBusiness;
    @Resource
    private ISysDictItemBusiness sysDictItemBusiness;

    /**
     * 转换读取出来的文件类
     * @param file
     * @return
     */
    @Override
    public List<SysDictImportBO> convert(File file) {
        try {
            return  ImportExcelUtil.readFile(file, SysDictImportBO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据实际业务处理数据
     * @param importBOList 导入的数据
     * @param fileDomain 文件状态数据
     * @param tmpFileName 临时文件名称
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void business(List<SysDictImportBO> importBOList, SysFileImportExcelBO fileDomain, String tmpFileName) throws Exception {
        Map<Integer,List<SysDictImportBO>> sysDictImportBOMap =  importBOList.stream().collect(Collectors.groupingBy(SysDictImportBO::getDictType));
        List<SysDictImportBO> sysDictImportBOList = sysDictImportBOMap.get(DictTypeEnum.DICT.getCode());
        Map<String ,Integer> dictCodeMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(sysDictImportBOList)){
            for(SysDictImportBO sysDictImportBO : sysDictImportBOList){
                SysDict sysDict = sysDictBusiness.lambdaQuery().eq(SysDict::getDictCode,sysDictImportBO.getDictCode()).one();
                if(sysDict != null){
                    sysDictBusiness.lambdaUpdate().set(SysDict::getDictName,sysDictImportBO.getDictName())
                            .set(SysDict::getDescription,sysDictImportBO.getDescription())
                            .set(SysDict::getStatus,sysDictImportBO.getStatus())
                            .eq(SysDict::getDictCode,sysDictImportBO.getDictCode())
                            .update();
                }else{
                    sysDict = new SysDict();
                    sysDict.setDictCode(sysDictImportBO.getDictCode());
                    sysDict.setDictName(sysDictImportBO.getDictName());
                    sysDict.setStatus(sysDictImportBO.getStatus());
                    sysDict.setDescription(sysDictImportBO.getDescription());
                    sysDictBusiness.save(sysDict);
                }
                dictCodeMap.put(sysDictImportBO.getDictCode(),sysDict.getId());
            }
        }

        List<SysDictImportBO> sysDictItemImportBOList = sysDictImportBOMap.get(DictTypeEnum.DICT_ITEM.getCode());
        for(SysDictImportBO sysDictImportBo :sysDictItemImportBOList){
            String dictCode = sysDictImportBo.getDictCode();
            Integer dictId = dictCodeMap.get(dictCode);
            String itemName = sysDictImportBo.getItemName();
            SysDictItem sysDictItem = sysDictItemBusiness.lambdaQuery().eq(SysDictItem::getDictId,dictId).eq(SysDictItem::getItemName,itemName).one();
            if(sysDictItem != null){
                sysDictItemBusiness.lambdaUpdate().eq(SysDictItem::getDictId,dictId).eq(SysDictItem::getItemName,itemName)
                        .set(SysDictItem::getItemValue,sysDictImportBo.getItemValue())
                        .set(SysDictItem::getRemark,sysDictImportBo.getRemark())
                        .set(SysDictItem::getSort,sysDictImportBo.getSort())
                        .set(SysDictItem::getStatus,sysDictImportBo.getItemStatus())
                        .update();
            }else {
                sysDictItem = new SysDictItem();
                sysDictItem.setDictId(dictId);
                sysDictItem.setItemName(sysDictImportBo.getItemName());
                sysDictItem.setItemValue(sysDictImportBo.getItemValue());
                sysDictItem.setSort(sysDictImportBo.getSort());
                sysDictItem.setStatus(sysDictImportBo.getItemStatus());
                sysDictItem.setRemark(sysDictImportBo.getRemark());
                sysDictItemBusiness.save(sysDictItem);
            }
        }

    }


    /**
     * 根据实际业务做数据校验,无需校验则直接返回true
     * @param importBOList 数据
     * @param fileDomain 上传文件对象
     * @param errFileName 错误文件名称
     * @return 是否通过校验
     */
    @Override
    public boolean dataCheck(List<SysDictImportBO> importBOList, SysFileImportExcelBO fileDomain, String errFileName) {
        if(CollectionUtils.isNotEmpty(importBOList)){
            Map<String,Integer> dictCodeMap = new HashMap<>();
            Map<String,Integer> dictItemMap = new HashMap<>();
            Integer errNum = 0;
            List<String> dictCodeList = importBOList.stream().filter(sysDictImportBO ->Objects.equals(sysDictImportBO.getDictType(),DictTypeEnum.DICT.getCode())).map(sysDictImportBO -> sysDictImportBO.getDictCode()).collect(Collectors.toList());
            for(SysDictImportBO sysDictImportBO : importBOList){
                boolean flag = false;
                String dictCode = sysDictImportBO.getDictCode();
                String itemName = sysDictImportBO.getItemName();
                StringBuilder rowTips = new StringBuilder();
                if(Objects.equals(sysDictImportBO.getDictType(),DictTypeEnum.DICT.getCode())){
                    if(dictCodeMap.containsKey(dictCode)){
                        rowTips.append("字典与第").append(dictCodeMap.get(dictCode)).append("行字典标识重复;");
                        flag = true;
                    }
                    if(sysDictImportBO.getStatus() == null){
                        rowTips.append("字典状态不能为空;");
                        flag = true;
                    }
                    dictCodeMap.put(dictCode,sysDictImportBO.getRowNum());
                }else if(Objects.equals(sysDictImportBO.getDictType(),DictTypeEnum.DICT_ITEM.getCode())){
                    if(StringUtils.isEmpty(itemName)){
                        rowTips.append("标签名称不能为空;");
                        flag = true;
                        String dictItemkey = dictCode + "-" + itemName;
                        if(dictItemMap.containsKey(dictItemkey)){
                            rowTips.append("标签名称与第").append(dictItemMap.get(dictItemkey)).append("行标名称识重复;");
                            flag = true;
                        }
                        dictItemMap.put(dictItemkey,sysDictImportBO.getRowNum());
                    }
                    if(sysDictImportBO.getItemValue() == null){
                        rowTips.append("数据键值不能为空;");
                        flag = true;
                    }
                    if(sysDictImportBO.getItemStatus() == null){
                        rowTips.append("标签状态不能为空;");
                        flag = true;
                    }
//                    if(sysDictImportBO.getSort() == null){
//                        rowTips.append("标签排序权重不能为空;");
//                        flag = true;
//                    }
                    if(!dictCodeList.contains(dictCode)){
                        SysDict sysDict = sysDictBusiness.lambdaQuery().eq(SysDict::getDictCode,sysDictImportBO.getDictCode()).one();
                        if(sysDict == null){
                            rowTips.append("标签对应的字典标识不存在");
                            flag = true;
                        }
                    }
                }else{
                    rowTips.append("字典类型错误");
                    flag = true;
                }
                sysDictImportBO.setRowTips(rowTips.toString());
                if(flag){
                    errNum++;
                }
            }

            if(errNum > 0){
                handleProcessingFailedBusiness(importBOList,fileDomain,errFileName,errNum);
                return false;
            }
        }
        return true;
    }



}
