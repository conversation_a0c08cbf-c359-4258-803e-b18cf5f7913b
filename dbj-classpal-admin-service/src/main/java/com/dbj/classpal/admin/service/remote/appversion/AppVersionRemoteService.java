package com.dbj.classpal.admin.service.remote.appversion;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.api.version.AppVersionClientApi;
import com.dbj.classpal.app.client.bo.version.CheckVersionApiBO;
import com.dbj.classpal.app.client.bo.version.VersionEditApiBO;
import com.dbj.classpal.app.client.bo.version.VersionIdApiBO;
import com.dbj.classpal.app.client.bo.version.VersionIdsApiBO;
import com.dbj.classpal.app.client.bo.version.VersionQueryApiBO;
import com.dbj.classpal.app.client.bo.version.VersionSaveApiBO;
import com.dbj.classpal.app.client.dto.version.AppVersionApiDTO;
import com.dbj.classpal.app.client.dto.version.VersionDetailApiDTO;
import com.dbj.classpal.app.client.dto.version.VersionShareApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:25:22
 */
@Component
public class AppVersionRemoteService {
    @Resource
    private AppVersionClientApi appVersionClientApi;

    public Page<VersionDetailApiDTO> pageVersions(PageInfo<VersionQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<VersionDetailApiDTO>> result =  appVersionClientApi.pageVersions(pageRequest);
        return result.returnProcess(result);
    }

    public void saveVersion(VersionSaveApiBO version) throws BusinessException {
        RestResponse<Void> result = appVersionClientApi.saveVersion(version);
        result.returnProcess(result);
    }

    public void editVersion(VersionEditApiBO version) throws BusinessException {
        RestResponse<Void> result = appVersionClientApi.editVersion(version);
        result.returnProcess(result);
    }

    public VersionDetailApiDTO getVersionDetail(VersionIdApiBO versionId) throws BusinessException {
        RestResponse<VersionDetailApiDTO> result = appVersionClientApi.getVersionDetail(versionId);
        return result.returnProcess(result);
    }


    public void batchDelete(VersionIdsApiBO versionIds) throws BusinessException {
        RestResponse<Void> result = appVersionClientApi.batchDelete(versionIds);
        result.returnProcess(result);
    }

    public void batchPublish(VersionIdsApiBO versionIds) throws BusinessException {
        RestResponse<Void> result = appVersionClientApi.batchPublish(versionIds);
        result.returnProcess(result);
    }

    public void batchCancel(VersionIdsApiBO versionIds) throws BusinessException {
        RestResponse<Void> result = appVersionClientApi.batchCancel(versionIds);
        result.returnProcess(result);
    }

    public VersionShareApiDTO getShareInfo(VersionIdApiBO versionId) throws BusinessException {
        RestResponse<VersionShareApiDTO> result = appVersionClientApi.getShareInfo(versionId);
        return result.returnProcess(result);
    }

    public AppVersionApiDTO checkVersion(CheckVersionApiBO versionBO) throws BusinessException {
        RestResponse<AppVersionApiDTO> result= appVersionClientApi.checkVersion(versionBO);
        return result.returnProcess(result);
    }
}
