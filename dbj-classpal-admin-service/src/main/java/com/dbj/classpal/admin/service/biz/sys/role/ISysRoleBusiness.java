package com.dbj.classpal.admin.service.biz.sys.role;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.role.SysRolePageBO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <p>
 * 角色信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface ISysRoleBusiness extends IService<SysRole> {


    Page<SysRolePageDTO> pageSysRole(PageInfo<SysRolePageBO> bo);
}
