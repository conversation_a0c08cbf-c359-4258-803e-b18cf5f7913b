package com.dbj.classpal.admin.service.biz.sys.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserBO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className SysUserBiz
 * @description
 * @date 2025-03-10 08:56
 **/
public interface ISysUserBusiness extends IService<SysUser> {

    SysUser getSysUserById(Integer id);

    Page<SysUserDTO> pageSysUser(PageInfo<SysUserBO> page);
}
