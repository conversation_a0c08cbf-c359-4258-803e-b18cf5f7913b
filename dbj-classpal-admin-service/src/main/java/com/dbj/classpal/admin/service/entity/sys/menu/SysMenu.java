package com.dbj.classpal.admin.service.entity.sys.menu;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 菜单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_menu")
@Tag(name ="SysMenu对象", description="菜单信息表")
public class SysMenu extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "菜单名称")
    @TableField("name")
    private String name;

    @Schema(description = "菜单简称")
    @TableField("short_name")
    private String shortName;

    @Schema(description = "父菜单ID")
    @TableField("parent_id")
    private Integer parentId;

    @Schema(description = "显示顺序")
    @TableField("order_num")
    private Integer orderNum;

    @Schema(description = "请求地址")
    @TableField("url")
    private String url;

    @Schema(description = "打开方式（menuItem页签 menuBlank新窗口）")
    @TableField("target")
    private String target;

    @Schema(description = "资源标识符")
    @TableField("identifier")
    private String identifier;

    @Schema(description = "菜单类型（1目录 2菜单 3按钮）")
    @TableField("menu_type")
    private Integer menuType;

    @Schema(description = "菜单状态（0显示 1隐藏）")
    @TableField("visible")
    private Integer visible;

    @Schema(description = "是否刷新（0刷新 1不刷新）")
    @TableField("is_refresh")
    private Integer isRefresh;

    @Schema(description = "权限标识")
    @TableField("perms")
    private String perms;

    @Schema(description = "菜单图标")
    @TableField("icon")
    private String icon;


    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;


}
