package com.dbj.classpal.admin.service.remote.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookApi;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.bo.share.GetShareInfoApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookAsyncProcessApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.PdfTaskStatusApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.ShareUrlResultApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class AppEBookRemoteService {
    @Resource
    private AppEBookApi appEBookApi;
    
    public Page<AppEBookApiDTO> page(PageInfo<AppEBookQueryApiBO> pageRequest) throws BusinessException{
        RestResponse<Page<AppEBookApiDTO>> result = appEBookApi.page(pageRequest);
        return result.returnProcess(result);
    }
    
    public AppEBookApiDTO detail(AppEBookIdApiBO idBO) throws BusinessException{
        RestResponse<AppEBookApiDTO> result = appEBookApi.detail(idBO);
        return result.returnProcess(result);
    }
    
    public Integer save(AppEBookSaveApiBO saveBO) throws BusinessException{
        RestResponse<Integer> result = appEBookApi.save(saveBO);
        return result.returnProcess(result);
    }
    
    public Boolean update(AppEBookUpdateApiBO saveBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.update(saveBO);
        return result.returnProcess(result);
    }

    public Boolean delete(AppEBookIdApiBO idBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.delete(idBO);
        return result.returnProcess(result);
    }


    public Boolean deleteBatch(AppEBookIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.deleteBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean enableBatch(AppEBookIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.enableBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean disableBatch(AppEBookIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.disableBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean allowDownloadBatch(AppEBookIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.allowDownloadBatch(idsBO);
        return result.returnProcess(result);
    }


    public Boolean disableDownloadBatch(AppEBookIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.disableDownloadBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean updateFile(AppEBookUpdateFileApiBO updateFileBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.updateFile(updateFileBO);
        return result.returnProcess(result);
    }

    public Boolean updateWatermark(AppEBookUpdateWatermarkApiBO updateWatermarkBO) throws BusinessException{
        RestResponse<Boolean> result = appEBookApi.updateWatermark(updateWatermarkBO);
        return result.returnProcess(result);
    }


    public String coverUrl(AppEBookFileApiBO fileApiBO) throws BusinessException{
        RestResponse<String> result = appEBookApi.coverUrl(fileApiBO);
        return result.returnProcess(result);
    }
    public AppEBookAsyncProcessApiDTO asyncCoverUrl(AppEBookAsyncProcessApiBO request) throws BusinessException{
        RestResponse<AppEBookAsyncProcessApiDTO> result = appEBookApi.asyncCoverUrl(request);
        return result.returnProcess(result);
    }


    public PdfTaskStatusApiDTO getTaskStatus(AppEBookAsyncProcessTaskApiBO taskApiBO) throws BusinessException{
        RestResponse<PdfTaskStatusApiDTO> result = appEBookApi.getTaskStatus(taskApiBO);
        return result.returnProcess(result);
    }


    public AppEBookAsyncProcessApiDTO reprocess(AppEBookReprocessApiBO request) throws BusinessException{
        RestResponse<AppEBookAsyncProcessApiDTO> result = appEBookApi.reprocess(request);
        return result.returnProcess(result);
    }

    public ShareUrlResultApiDTO getShareInfo(GetShareInfoApiBO request) throws BusinessException{
        RestResponse<ShareUrlResultApiDTO> result = appEBookApi.getShareInfo(request);
        return result.returnProcess(result);
    }

}