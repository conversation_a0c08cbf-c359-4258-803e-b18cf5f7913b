package com.dbj.classpal.admin.service.remote.audio;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.audio.AudioHintMusicApi;

import com.dbj.classpal.books.client.bo.audio.AudioHintMusicAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicPageBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicUpdBO;
import com.dbj.classpal.books.client.dto.audio.AudioHintMusicPageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 音频-制作配置远程服务
 */
@Component
public class AudioHintMusicRemoteService {

    @Autowired
    private AudioHintMusicApi audioHintMusicApi;

    public Boolean save(AudioHintMusicAddBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Boolean> result = audioHintMusicApi.save(bo);
        return result.returnProcess(result);
    }

    public Boolean update(AudioHintMusicUpdBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Boolean> result = audioHintMusicApi.update(bo);
        return result.returnProcess(result);
    }

    public Page<AudioHintMusicPageDTO> pageInfo(PageInfo<AudioHintMusicPageBO> bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Page<AudioHintMusicPageDTO>> result = audioHintMusicApi.pageInfo(bo);
        return result.returnProcess(result);
    }

    public Boolean delete(AudioHintMusicDelBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Boolean> result = audioHintMusicApi.delete(bo);
        return result.returnProcess(result);
    }
}
