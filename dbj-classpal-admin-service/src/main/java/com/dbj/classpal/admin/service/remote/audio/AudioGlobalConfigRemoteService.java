package com.dbj.classpal.admin.service.remote.audio;

import com.dbj.classpal.books.client.api.audio.AudioGlobalConfigApi;
import com.dbj.classpal.books.client.bo.audio.AudioGlobalConfigAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioIntroIdBO;
import com.dbj.classpal.books.client.dto.audio.AudioGlobalConfigDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 全局配置
 * <AUTHOR>
 */
@Component
public class AudioGlobalConfigRemoteService {

    @Autowired
    private AudioGlobalConfigApi audioGlobalConfigApi;

    public List<AudioGlobalConfigDTO> getGlobalConfig(AudioIntroIdBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<List<AudioGlobalConfigDTO>> result = audioGlobalConfigApi.getGlobalConfig(bo);
        return result.returnProcess(result);
    }

    public Integer saveGlobalConfig(List<AudioGlobalConfigAddBO> bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Integer> result = audioGlobalConfigApi.saveGlobalConfig(bo);
        return result.returnProcess(result);
    }
}
