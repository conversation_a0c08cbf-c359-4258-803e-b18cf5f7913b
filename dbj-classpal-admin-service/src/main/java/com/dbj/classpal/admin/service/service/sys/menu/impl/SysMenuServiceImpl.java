package com.dbj.classpal.admin.service.service.sys.menu.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuTypeBO;
import com.dbj.classpal.admin.common.enums.MenuTypeEnum;
import com.dbj.classpal.admin.service.biz.sys.help.ISysHelpItemsBusiness;
import com.dbj.classpal.admin.service.biz.sys.menu.ISysMenuBusiness;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleBusiness;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleMenuBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserRoleBusiness;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuSaveBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdIsRefreshBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdVisibleBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.sys.menu.SysMenuDTO;
import com.dbj.classpal.admin.service.entity.sys.help.SysHelpItems;
import com.dbj.classpal.admin.service.entity.sys.menu.SysMenu;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import com.dbj.classpal.admin.service.entity.sys.role.SysRoleMenu;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.dbj.classpal.admin.service.service.sys.menu.SysMenuService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.constant.Constants;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, cn.zlinks
 * FileName: SysMenuService
 * Date:     2024-2-2 13:36:06
 * Description:SysMenuService接口
 * <AUTHOR> Liu
 */
@Service
@Slf4j
public class SysMenuServiceImpl implements SysMenuService {

    @Resource
    private ISysMenuBusiness sysMenuBusiness;

    @Resource
    private ISysUserBusiness sysUserBusiness;
    @Resource
    private ISysUserRoleBusiness sysUserRoleBusiness;
    @Resource
    private ISysRoleBusiness sysRoleBusiness;
    @Resource
    private ISysRoleMenuBusiness sysRoleMenuBusiness;
    @Resource
    private ISysHelpItemsBusiness sysHelpItemsBusiness;

    @Override
    public SysMenuDTO getSysMenuInfo(Integer id) throws BusinessException {
        SysMenu sysMenuDomain = sysMenuBusiness.getById(id);
        if(sysMenuDomain == null){
            throw new BusinessException(AdminErrorCode.MENU_NOT_EXIST_CODE,AdminErrorCode.MENU_NOT_EXIST_MSG);
        }
        SysMenuDTO sysMenuDTO = new SysMenuDTO();
        BeanUtils.copyProperties(sysMenuDomain,sysMenuDTO);
        return sysMenuDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSysMenu(SysMenuSaveBO sysMenuSaveBO) throws BusinessException {
        //同一级的菜单名称不能重复
        List<SysMenu> sysMenuList = sysMenuBusiness.lambdaQuery().eq(SysMenu::getName,sysMenuSaveBO.getName()).eq(SysMenu::getParentId,sysMenuSaveBO.getParentId()).list();
        if(CollectionUtils.isNotEmpty(sysMenuList)){
            throw new BusinessException(AdminErrorCode.MENU_NAME_REPEAT_CODE,AdminErrorCode.MENU_NAME_REPEAT_MSG);
        }
        SysMenu sysMenu = new SysMenu();
        BeanUtils.copyProperties(sysMenuSaveBO,sysMenu);
        sysMenuBusiness.save(sysMenu);
        String identifier = generateIdentifier(sysMenu.getId(),sysMenu.getMenuType());
        sysMenu.setIdentifier(identifier);
        sysMenuBusiness.updateById(sysMenu);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSysMenu(SysMenuUpdBO sysMenuUpdBO) throws BusinessException {
        //同一级的菜单名称不能重复
        String name = sysMenuUpdBO.getName();
        SysMenu sysMenu = sysMenuBusiness.getById(sysMenuUpdBO.getId());
        if(sysMenu == null){
            throw new BusinessException(AdminErrorCode.MENU_NOT_EXIST_CODE,AdminErrorCode.MENU_NOT_EXIST_MSG);
        }
        if(!StringUtils.equals(name,sysMenu.getName())){
            List<SysMenu> sysMenuList = sysMenuBusiness.lambdaQuery().eq(SysMenu::getName,sysMenuUpdBO.getName()).eq(SysMenu::getParentId,sysMenuUpdBO.getParentId()).list();
            if(CollectionUtils.isNotEmpty(sysMenuList)){
                throw new BusinessException(AdminErrorCode.MENU_NAME_REPEAT_CODE,AdminErrorCode.MENU_NAME_REPEAT_MSG);
            }
        }

        //修改菜单时判断父节点是否为自己的子节点
        List<SysMenu> allMenus = sysMenuBusiness.list();
        List<Integer> childIds = new ArrayList<>();
        SysMenuDTO currentMenu = new SysMenuDTO();
        BeanUtils.copyProperties(sysMenu,currentMenu);
        List<Integer> idList = getMenuChildIds(BeanUtil.copyToList(allMenus,SysMenuDTO.class), currentMenu, childIds,new HashSet<>());
        if(idList.contains(sysMenuUpdBO.getParentId()) || Objects.equals(sysMenuUpdBO.getParentId(), sysMenu.getId())) {
            throw new BusinessException(AdminErrorCode.MENU_NODE_LOOP_CODE,AdminErrorCode.MENU_NODE_LOOP_MSG);
        }
        sysMenu = new SysMenu();
        BeanUtils.copyProperties(sysMenuUpdBO,sysMenu);
        sysMenuBusiness.updateById(sysMenu);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSysMenuVisible(SysMenuUpdVisibleBO sysMenuUpdVisibleBO) throws BusinessException {
        SysMenu sysMenu = sysMenuBusiness.getById(sysMenuUpdVisibleBO.getId());
        if(sysMenu == null){
            throw new BusinessException(AdminErrorCode.MENU_NOT_EXIST_CODE,AdminErrorCode.MENU_NOT_EXIST_MSG);
        }
        sysMenu = new SysMenu();
        BeanUtils.copyProperties(sysMenuUpdVisibleBO,sysMenu);
        sysMenuBusiness.updateById(sysMenu);
        return true;
    }

    @Override
    public Boolean updateSysMenuIsRefresh(SysMenuUpdIsRefreshBO bo) throws BusinessException {
        SysMenu sysMenu = sysMenuBusiness.getById(bo.getId());
        if(sysMenu == null){
            throw new BusinessException(AdminErrorCode.MENU_NOT_EXIST_CODE,AdminErrorCode.MENU_NOT_EXIST_MSG);
        }
        sysMenu = new SysMenu();
        BeanUtils.copyProperties(bo,sysMenu);
        sysMenuBusiness.updateById(sysMenu);
        return true;
    }

    @Override
    public Boolean delSysMenu(BaseIdBO bo) throws BusinessException {
        Integer id = bo.getId();
        SysMenu sysMenu = sysMenuBusiness.getById(id);
        if(sysMenu == null){
            throw new BusinessException(AdminErrorCode.MENU_NOT_EXIST_CODE,AdminErrorCode.MENU_NOT_EXIST_MSG);
        }
        List<SysMenu> sysMenuList = sysMenuBusiness.lambdaQuery().eq(SysMenu::getParentId,id).list();
        if(CollectionUtils.isNotEmpty(sysMenuList)){
            throw new BusinessException(AdminErrorCode.MENU_CHILDREN_EXIST_CODE,AdminErrorCode.MENU_CHILDREN_EXIST_MSG);
        }
        sysMenuBusiness.removeById(id);
        return true;
    }


    @Override
    public List<SysMenuDTO> getSysMenuAll() {
        //查询所有菜单
        List<SysMenu> sysMenuList = sysMenuBusiness.lambdaQuery().orderByDesc(SysMenu::getOrderNum).list();
        List<SysMenuDTO> sysMenuDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(sysMenuList)){
            sysMenuDTOList = BeanUtil.copyToList(sysMenuList,SysMenuDTO.class);
        }
        return sysMenuDTOList;
    }

    @Override
    public List<SysMenuDTO> getSysMenuByMenuType() {
        //查询所有菜单
        List<SysMenu> sysMenuList = sysMenuBusiness.lambdaQuery().list();
        List<SysMenuDTO> sysMenuDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(sysMenuList)){
            List<SysHelpItems> sysHelpItemsList = sysHelpItemsBusiness.lambdaQuery().in(SysHelpItems::getMenuPageId,sysMenuList.stream().filter(a -> !Objects.equals(a.getMenuType() , MenuTypeEnum.DIRECTORY.getCode())).map(SysMenu::getId).toList()).list();
            List<Integer> menuPageIds =  new ArrayList<>();
            if(CollectionUtils.isNotEmpty(sysHelpItemsList)){
                menuPageIds =  sysHelpItemsList.stream().map(SysHelpItems::getMenuPageId).collect(Collectors.toList());
            }
            sysMenuDTOList = BeanUtil.copyToList(sysMenuList,SysMenuDTO.class);
            for(SysMenuDTO sysMenuDTO : sysMenuDTOList){
                if(menuPageIds.contains(sysMenuDTO.getId())){
                    sysMenuDTO.setIsHaveHelpPage(YesOrNoEnum.YES.getCode());
                }else {
                    sysMenuDTO.setIsHaveHelpPage(YesOrNoEnum.NO.getCode());
                }

            }
        }
        return sysMenuDTOList;
    }

    @Override
    public List<SysMenuDTO> getSysUserMenuAuth(Integer userId) throws BusinessException {
        SysUser sysUser = sysUserBusiness.getById(userId);
        List<SysMenuDTO> appMenus = new ArrayList<>();
        boolean isAdmin = StrUtil.equals(Constants.ADMINISTRATORS, sysUser.getAccounts());
        if(isAdmin){
            appMenus = BeanUtil.copyToList(sysMenuBusiness.list(),SysMenuDTO.class);
        }else {
            // 查询公共角色
            List<SysUserRole> sysUserRoleList =  sysUserRoleBusiness.lambdaQuery().eq(SysUserRole::getUserId, sysUser.getId()).list();
            SysRole commonRole = sysRoleBusiness.lambdaQuery().eq(SysRole::getRoleCode, Constants.COMMON).one();
            if (CollectionUtils.isEmpty(sysUserRoleList) && commonRole == null) {
                throw new BusinessException(AdminErrorCode.NOT_LOGIN_CODE, AdminErrorCode.NOT_LOGIN_MSG);
            }
            List<Integer> roles = sysUserRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
            if(commonRole != null){
                roles.add(commonRole.getId());
            }
            //查询用户角色信息
            List<SysRole> sysRoleDomainList = sysRoleBusiness.listByIds(roles);
            List<SysRole> adminDates = sysRoleDomainList.stream()
                    .filter(a -> StringUtils.equals(a.getRoleCode(), Constants.ADMINISTRATORS))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(adminDates)){
                appMenus = BeanUtil.copyToList(sysMenuBusiness.list(),SysMenuDTO.class);
            }else{
                //通过角色查询菜单即可
                List<Integer> appRoleIds = sysRoleDomainList.stream().map(SysRole::getId).collect(Collectors.toList());
                List<SysRoleMenu> SysRoleMenuList =  sysRoleMenuBusiness.lambdaQuery().in(SysRoleMenu::getRoleId,appRoleIds).list();
                if(CollectionUtils.isNotEmpty(SysRoleMenuList)){
                    List<Integer> menuIds = SysRoleMenuList.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
                    appMenus = BeanUtil.copyToList(sysMenuBusiness.listByIds(menuIds),SysMenuDTO.class);
                }
            }
        }
        appMenus = buildTree2(appMenus);
        return appMenus;
    }


    /**
     * 递归获取菜单树
     * @param allMenus 菜单信息
     * @param currentMenu 当前菜单信息
     * @return SysDeptDTO
     */
    public List<Integer> getMenuChildIds(List<SysMenuDTO> allMenus, SysMenuDTO currentMenu, List<Integer> ids,Set<Integer> visitedIds) {
        if(CollectionUtils.isNotEmpty(allMenus)){
            List<SysMenuDTO> children = new ArrayList<>();
            if (visitedIds.contains(currentMenu.getId())) {
                log.error("菜单存在循环引用: {}", currentMenu.getId());
                return ids;
            }
            visitedIds.add(currentMenu.getId());
            for (SysMenuDTO child : allMenus) {
                if (Objects.equals(child.getParentId(), currentMenu.getId())) {
                    ids.add(child.getId());
                    children.add(child);
                    traverseTree(child, allMenus, ids);
                }
            }
            allMenus.removeAll(children);
            currentMenu.setChildren(children);
        }
        return ids;
    }

    /**
     * 递归获取菜单树
     * @param currentMenu 父节点id
     * @param allMenus 菜单树
     */
    private void traverseTree(SysMenuDTO currentMenu,List<SysMenuDTO> allMenus, List<Integer> ids) {
        if (currentMenu != null) {
            List<SysMenuDTO> children = new ArrayList<>();
            for (SysMenuDTO child : allMenus) {
                if (Objects.equals(child.getParentId(), currentMenu.getId())) {
                    ids.add(child.getId());
                    children.add(child);
                }
            }
            currentMenu.setChildren(children);
        }
    }

    public static String generateIdentifier(Integer id, int menuType) {
        long seedVal;
        StringBuilder resultStr = new StringBuilder();
        String charSet = "abcdefghijklmnopqrstuvwxyz0123456789";
        long positiveMenuType = (long) menuType & 0xFFFFFFFFL;
        seedVal = (id) * 1234567L + positiveMenuType * 7654321L;
        seedVal = seedVal % 1000000000;
        for (int i = 0; i < 6; i++) {
            resultStr.append(charSet.charAt((int) (seedVal % 36)));
            seedVal = seedVal / 36;
        }
        return resultStr.toString();
    }


    /**
     * 封装树节点
     * @param menuList
     * @return
     */
    public static List<SysMenuDTO> buildTree2(List<SysMenuDTO> menuList) {
        if (menuList == null || menuList.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Integer, SysMenuDTO> nodeMap = menuList.stream().collect(Collectors.toMap(SysMenuDTO::getId, v -> v));
        List<SysMenuDTO> rootNodes = new ArrayList<>();
        for (SysMenuDTO node : menuList) {
            // 根节点
            if (node.getParentId() == null || node.getParentId() == 0) {
                rootNodes.add(node);
                continue;
            }

            SysMenuDTO parentNode = nodeMap.get(node.getParentId());
            if (parentNode != null) {
                parentNode.addChild(node);
            }
        }
        return rootNodes;
    }
}