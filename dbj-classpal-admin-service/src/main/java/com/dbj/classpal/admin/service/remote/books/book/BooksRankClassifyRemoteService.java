package com.dbj.classpal.admin.service.remote.books.book;


import com.dbj.classpal.books.client.api.books.AdminBooksRankApi;
import com.dbj.classpal.books.client.api.books.AdminBooksRankClassifyApi;
import com.dbj.classpal.books.client.bo.books.BooksRankClassifyBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoUpdForceApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankClassifyDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 图书表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Component
public class BooksRankClassifyRemoteService {

    @Resource
    private AdminBooksRankClassifyApi adminBooksRankClassifyApi;

    public List<BooksRankClassifyDTO> list(BooksRankClassifyBO boardBooksRankClassifyBO) throws BusinessException{
        RestResponse<List<BooksRankClassifyDTO>> result = adminBooksRankClassifyApi.list(boardBooksRankClassifyBO);
        return result.returnProcess(result);
    }

}
