package com.dbj.classpal.admin.service.service.file;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelBO;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelClearBO;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelSaveBO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 导出文件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISysFileExportExcelService{


    /**
     * 查询一条数据的明细
     * @param id
     * @return
     */
    SysFileExportExcelDTO getSysFileExportExcel(Integer id) throws BusinessException;

    /**
     * 保存导入数据
     * @param saveBO
     * @return
     * @throws Exception
     */
    Integer saveExportFile(SysFileExportExcelSaveBO saveBO) throws Exception;

     /**
     * 获取文件上传记录统计
     * @return
     */
    List<SysFileExportExcelCountDTO> sysFileExportExcelCount();

    /**
     * 分页查询
     * @param page
     * @return
     */
    Page<SysFileExportExcelDTO> pageSysFileExportExcel(PageInfo<SysFileExportExcelBO> page);

    /**
     * 清空失败和清空全部
     * @param bo
     * @return
     */
    Boolean deleteClear(SysFileExportExcelClearBO bo);



}
