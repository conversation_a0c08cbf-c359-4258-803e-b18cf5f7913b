package com.dbj.classpal.admin.service.mapper.sys.dept;

import com.dbj.classpal.admin.service.entity.sys.dept.SysDept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Mapper
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 根据id查询id下所有人部门
     *
     * @param deptId
     * @return SysDeptDomain
     */
    List<Integer> getSysDeptInfos(@Param("deptId")Integer deptId);
}
