package com.dbj.classpal.admin.service.remote.appevaluation;

import com.dbj.classpal.books.client.api.evaluation.AdminEvaluationNodeApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeEditApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSortApiBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationRemoteService
 * Date:     2025-05-16 16:11:44
 * Description: 表名： ,描述： 表
 */
@Component
public class AppEvaluationNodeRemoteService {
    @Resource
    private AdminEvaluationNodeApi evaluationNodeApi;

    /**
     * 新增评测项
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean saveEvaluationNode(AdminEvaluationNodeSaveApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = evaluationNodeApi.saveEvaluationNode(bo);
        return result.returnProcess(result);
    }

    /**
     * 修改评测项
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean editEvaluationNode(AdminEvaluationNodeEditApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = evaluationNodeApi.editEvaluationNode(bo);
        return result.returnProcess(result);
    }


    /**
     * 评测项排序
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean reSort(AdminEvaluationNodeSortApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = evaluationNodeApi.reSort(bo);
        return result.returnProcess(result);
    }

    /**
     * 删除评测项
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean deleteNode(CommonIdsApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = evaluationNodeApi.delete(bo);
        return result.returnProcess(result);
    }

}
