package com.dbj.classpal.admin.service.remote.appnotice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.api.notice.AppNoticeClientApi;
import com.dbj.classpal.app.client.bo.notice.*;
import com.dbj.classpal.app.client.dto.channel.ChannelDetailApiDTO;
import com.dbj.classpal.app.client.dto.notice.NoticeDetailApiDTO;
import com.dbj.classpal.app.client.dto.notice.NoticeListApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:18:43
 */
@Component
public class AppNoticeRemoteService {
    @Resource
    private AppNoticeClientApi appNoticeClientApi;

    public Page<NoticeDetailApiDTO> pageNotices(PageInfo<NoticeQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<NoticeDetailApiDTO>> result = appNoticeClientApi.pageNotices(pageRequest);
        return result.returnProcess(result);
    }

    public Boolean saveNotice(NoticeSaveApiBO notice) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.saveNotice(notice);
        return result.returnProcess(result);
    }

    public Boolean updateNotice(NoticeUpdateApiBO notice) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.updateNotice(notice);
        return result.returnProcess(result);
    }

    public Boolean deleteNotice(NoticeIdApiBO notice) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.delete(notice);
        return result.returnProcess(result);
    }

    public Boolean enableNotice(NoticeIdApiBO notice) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.enable(notice);
        return result.returnProcess(result);
    }

    public Boolean disableNotice(NoticeIdApiBO notice) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.disable(notice);
        return result.returnProcess(result);
    }

    public NoticeDetailApiDTO getNoticeDetail(NoticeIdApiBO noticeId) throws BusinessException {
        RestResponse<NoticeDetailApiDTO> result = appNoticeClientApi.getNoticeDetail(noticeId);
        return result.returnProcess(result);
    }

    public List<NoticeDetailApiDTO> batchGetNoticeDetails(NoticeIdsApiBO noticeIds) throws BusinessException {
        RestResponse<List<NoticeDetailApiDTO>> result = appNoticeClientApi.batchGetNoticeDetails(noticeIds);
        return result.returnProcess(result);

    }
    

    public Boolean batchEnable(NoticeIdsApiBO noticeIds) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.batchEnable(noticeIds);
        return result.returnProcess(result);
    }

    public Boolean batchDisable(NoticeIdsApiBO noticeIds) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.batchDisable(noticeIds);
        return result.returnProcess(result);
    }

    public Boolean batchDelete(NoticeIdsApiBO noticeIds) throws BusinessException {
        RestResponse<Boolean> result = appNoticeClientApi.batchDelete(noticeIds);
        return result.returnProcess(result);
    }

    public List<NoticeListApiDTO> listValidNotices(NoticeQueryParamApiBO queryParam) throws BusinessException {
        RestResponse<List<NoticeListApiDTO>> result = appNoticeClientApi.listValidNotices(queryParam);
        return result.returnProcess(result);
    }
}