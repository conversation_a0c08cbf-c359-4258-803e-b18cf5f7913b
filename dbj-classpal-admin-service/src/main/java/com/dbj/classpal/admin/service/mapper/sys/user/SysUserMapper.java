package com.dbj.classpal.admin.service.mapper.sys.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserBO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {


    Page<SysUserDTO> pageSysUser(Page page, @Param("bo") SysUserBO sysUserBO);
}
