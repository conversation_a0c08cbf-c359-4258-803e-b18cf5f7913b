package com.dbj.classpal.admin.service.remote.books.book;

import com.dbj.classpal.books.client.api.books.AdminBooksCompatibilityApi;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilitySaveBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityUpdBO;
import com.dbj.classpal.books.client.dto.books.BooksCompatibilityApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@Component
public class AdminBooksCompatibilityRemoteService {

    @Resource
    private AdminBooksCompatibilityApi adminBooksCompatibilityApi;

    public Boolean save(@RequestBody BooksCompatibilitySaveBO booksCompatibilitySaveBO) throws BusinessException{
        RestResponse<Boolean> result = adminBooksCompatibilityApi.save(booksCompatibilitySaveBO);
        return result.returnProcess(result);
    }

    public List<BooksCompatibilityApiDTO> list(@RequestBody BooksCompatibilityBO booksCompatibilityBO) throws BusinessException{
        RestResponse<List<BooksCompatibilityApiDTO>> result = adminBooksCompatibilityApi.list(booksCompatibilityBO);
        return result.returnProcess(result);

    }

    public Boolean update(@RequestBody BooksCompatibilityUpdBO booksCompatibilityUpdBO) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksCompatibilityApi.update(booksCompatibilityUpdBO);
        return result.returnProcess(result);
    }


    public Boolean delete(@RequestParam Integer id) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksCompatibilityApi.delete(id);
        return result.returnProcess(result);
    }


}
