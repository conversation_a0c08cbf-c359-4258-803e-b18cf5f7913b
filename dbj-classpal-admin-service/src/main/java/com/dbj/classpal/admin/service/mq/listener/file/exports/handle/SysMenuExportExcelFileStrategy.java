package com.dbj.classpal.admin.service.mq.listener.file.exports.handle;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictExportBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuExportBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.common.enums.dict.DictTypeEnum;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.dbj.classpal.admin.service.biz.sys.menu.ISysMenuBusiness;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.admin.service.entity.sys.menu.SysMenu;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19 13:56:37
 */
@Service("sysMenuExportExcelFileStrategy")
@Slf4j
public class SysMenuExportExcelFileStrategy extends AdminExportExcelFileStrategy<SysMenuExportBO> {


    @Resource
    private ISysMenuBusiness sysMenuBusiness;



    @Override
    public void handlerExportExcelFile(SysFileExportExcelBO fileDomain) throws Exception {
        //字段数据不多，所以这里直接查询全部，后续如果数据较多，则需要分页处理
        try {
            //这里需要定义查询数据
            List<SysMenu> dataList = sysMenuBusiness.list();
            if(CollectionUtils.isEmpty(dataList)){
                fileDomain.setErrorMsg("导出数据不能为空");
                handleProcessingFailedSys(fileDomain);
                return;
            }
            List<SysMenuExportBO> sysMenuExportBOList = BeanUtil.copyToList(dataList,SysMenuExportBO.class);
            //查询数据对应的上级id
            List<Integer> parentIds = dataList.stream().map(SysMenu::getParentId).collect(Collectors.toList());
            List<SysMenu> parentList = sysMenuBusiness.listByIds(parentIds);
            Map<Integer, SysMenu> parentMap = parentList.stream().collect(Collectors.toMap(SysMenu::getId, sysMenu -> sysMenu));
            for (SysMenuExportBO sysMenuExportBO : sysMenuExportBOList) {
                if(Objects.nonNull(sysMenuExportBO.getParentId())){
                    SysMenu parent = parentMap.get(sysMenuExportBO.getParentId());
                    if(Objects.nonNull(parent)){
                        sysMenuExportBO.setParentName(parent.getName());
                        sysMenuExportBO.setParentIdentifier(parent.getIdentifier());
                    }
                }
            }

            updateFileProcessed(sysMenuExportBOList, fileDomain);
        }catch (Exception e){
            fileDomain.setErrorMsg(e.getMessage());
            handleProcessingFailedSys(fileDomain);
        }

    }



}
