package com.dbj.classpal.admin.service.remote.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookstoreApi;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class AppEBookstoreRemoteService {
    @Resource
    private AppEBookstoreApi bookstoreApi;

    public Page<AppEBookstoreApiDTO> page(PageInfo<AppEBookstoreQueryApiBO> pageRequest) throws BusinessException{
        RestResponse<Page<AppEBookstoreApiDTO>> result = bookstoreApi.page(pageRequest);
        return result.returnProcess(result);
    }

    public AppEBookstoreApiDTO detail(AppEBookstoreIdApiBO idBO) throws BusinessException{
        RestResponse<AppEBookstoreApiDTO> result = bookstoreApi.detail(idBO);
        return result.returnProcess(result);
    }

    public Integer save(AppEBookstoreSaveApiBO saveBO) throws BusinessException{
        RestResponse<Integer> result = bookstoreApi.save(saveBO);
        return result.returnProcess(result);
    }

    public Boolean update(AppEBookstoreUpdateApiBO saveBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.update(saveBO);
        return result.returnProcess(result);
    }

    public Boolean delete(AppEBookstoreIdApiBO idBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.delete(idBO);
        return result.returnProcess(result);
    }


    public Boolean deleteBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.deleteBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean enableBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.enableBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean disableBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.disableBatch(idsBO);
        return result.returnProcess(result);
    }

    public Boolean allowDownloadBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.allowDownloadBatch(idsBO);
        return result.returnProcess(result);
    }


    public Boolean disableDownloadBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        RestResponse<Boolean> result = bookstoreApi.disableDownloadBatch(idsBO);
        return result.returnProcess(result);
    }
}