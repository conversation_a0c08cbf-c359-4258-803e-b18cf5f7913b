package com.dbj.classpal.admin.service.service.sys.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserFirstUpdPasswordBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserSaveBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdAccountsStatusBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdNameBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdPasswordBO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public interface ISysUserService {


    SysUserDetailDTO getSysUserById(Integer id) throws BusinessException;

    Page<SysUserDTO> pageSysUser(PageInfo<SysUserBO> page);

    Boolean saveSysUser(SysUserSaveBO sysUserSaveBO) throws BusinessException;


    Boolean updateSysUser(SysUserUpdBO sysUserUpdateBO) throws BusinessException;

    Boolean updateSysUserName(SysUserUpdNameBO sysUserUpdateBO) throws BusinessException;

    Boolean updateAccountsStatus(SysUserUpdAccountsStatusBO sysUser);

    Boolean resetPassword(BaseIdsBO baseIds);


    Boolean updatePassword(SysUserUpdPasswordBO sysUserUpdPasswordBO) throws BusinessException;

    Boolean firstUpdatePassword(SysUserFirstUpdPasswordBO sysUserFirstUpdPasswordBO) throws BusinessException;

    Boolean delSysUser(BaseIdsBO baseIds);
}
