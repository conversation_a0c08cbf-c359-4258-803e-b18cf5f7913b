package com.dbj.classpal.admin.service.remote.appalbum;

import com.dbj.classpal.books.client.api.album.AppAlbumElementsApi;
import com.dbj.classpal.books.client.api.album.AppAlbumElementsBusinessRefApi;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefQueryApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefQueryCommonApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefRemoteService
 * Date:     2025-04-15 14:27:26
 * Description: 表名： ,描述： 表
 */
@Component
public class AppAlbumElementsBusinessRefRemoteService {

    @Resource
    private AppAlbumElementsBusinessRefApi api;

    public List<AppAlbumElementsBusinessRefQueryApiDTO> getElementsBusinessRef(AppAlbumElementsBusinessRefQueryCommonApiBO bo) throws BusinessException {
        RestResponse<List<AppAlbumElementsBusinessRefQueryApiDTO>> result = api.getElementsBusinessRef(bo);
        return result.returnProcess(result);
    }

    public AppAlbumElementsBusinessRefMaterialQueryApiDTO getElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefQueryApiBO bo) throws BusinessException {
        RestResponse<AppAlbumElementsBusinessRefMaterialQueryApiDTO> result = api.getElementsBusinessRefMaterialRef(bo);
        return result.returnProcess(result);
    }

    public Boolean saveOrUpdateElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.saveOrUpdateElementsBusinessRefMaterialRef(bo);
        return result.returnProcess(result);
    }


    public BooksRefDirectApiDTO getAlbumElementsBusinessRefBooks(CommonIdApiBO bo) throws BusinessException {
        RestResponse<BooksRefDirectApiDTO>result = api.getAlbumElementsBusinessRefBooks(bo);
        return result.returnProcess(result);
    }
}
