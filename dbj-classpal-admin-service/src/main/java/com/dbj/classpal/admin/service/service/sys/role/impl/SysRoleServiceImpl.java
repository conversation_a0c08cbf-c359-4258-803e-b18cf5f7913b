package com.dbj.classpal.admin.service.service.sys.role.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleBusiness;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleMenuBusiness;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRolePageBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleSaveBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleUpdBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleUpdStatusBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.sys.role.SysRoleDTO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserRoleBusiness;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import com.dbj.classpal.admin.service.entity.sys.role.SysRoleMenu;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.dbj.classpal.admin.service.service.sys.role.ISysRoleService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.constant.Constants;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className SysRoleService
 * @description
 * @date 2025-03-17 16:32
 **/
@Service
public class SysRoleServiceImpl implements ISysRoleService {

    @Resource
    private ISysRoleBusiness sysRoleBusiness;
    @Resource
    private ISysRoleMenuBusiness sysRoleMenuBusiness;
    @Resource
    private ISysUserRoleBusiness sysUserRoleBusiness;
    @Override
    public SysRoleDTO getSysRoleInfo(Integer id) throws BusinessException {
        SysRole sysRoleDomain = sysRoleBusiness.getById(id);
        if(sysRoleDomain == null){
            throw new BusinessException(AdminErrorCode.ROLE_NOT_EXIST_CODE,AdminErrorCode.ROLE_NOT_EXIST_MSG);
        }
        //根据id查询对应的菜单权限
        String roleCode = sysRoleDomain.getRoleCode();
        SysRoleDTO sysRoleDTO = new SysRoleDTO();
        BeanUtils.copyProperties(sysRoleDomain, sysRoleDTO);
        if(!StringUtils.equals(Constants.ADMINISTRATORS, roleCode)){
            //查询角色与菜单的关系数据
            List<SysRoleMenu> sysRoleMenuList = sysRoleMenuBusiness.lambdaQuery().eq(SysRoleMenu::getRoleId,sysRoleDomain.getId()).list();
            if(CollectionUtils.isNotEmpty(sysRoleMenuList)){
                sysRoleDTO.setMenuIds( sysRoleMenuList.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList()));
            }
        }
        return sysRoleDTO;
    }

    @Override
    public Boolean saveSysRole(SysRoleSaveBO sysRoleSaveBO) throws BusinessException {
        // 新增角色 判断名称是否重复，新增判断是否有数据权限是哪一种，然后对应添加数据权限
        String name = sysRoleSaveBO.getName();
        List<SysRole> sysRoleList =  sysRoleBusiness.lambdaQuery().eq(SysRole::getName,name).list();
        if(CollectionUtils.isNotEmpty(sysRoleList)){
            throw new BusinessException(AdminErrorCode.ROLE_NAME_REPEAT_CODE,AdminErrorCode.ROLE_NAME_REPEAT_MSG);
        }
        SysRole sysRole = new SysRole();
        BeanUtils.copyProperties(sysRoleSaveBO, sysRole);
        sysRole.setRoleCode("basic");
        sysRoleBusiness.save(sysRole);
        Integer id = sysRole.getId();
        List<Integer> menuIds = sysRoleSaveBO.getMenuIds();
        //新增角色与菜单的关系表
        List<SysRoleMenu> sysRoleMenuDomainList = new ArrayList<>();
        for(Integer menuId : menuIds){
            SysRoleMenu sysRoleMenuDomain = new SysRoleMenu();
            sysRoleMenuDomain.setRoleId(id);
            sysRoleMenuDomain.setMenuId(menuId);
            sysRoleMenuDomainList.add(sysRoleMenuDomain);
        }
        sysRoleMenuBusiness.saveBatch(sysRoleMenuDomainList);
        return true;
    }

    @Override
    public Boolean updateSysRole(SysRoleUpdBO sysRoleUpdBO) throws BusinessException {
        SysRole sysRoleDomain = sysRoleBusiness.getById(sysRoleUpdBO.getId());
        if(sysRoleDomain == null){
            throw new BusinessException(AdminErrorCode.ROLE_NOT_EXIST_CODE,AdminErrorCode.ROLE_NOT_EXIST_MSG);
        }
        if(StringUtils.equals(Constants.ADMINISTRATORS, sysRoleDomain.getRoleCode())){
            throw new BusinessException(AdminErrorCode.ROLE_ADMIN_NOT_UPDATE_CODE,AdminErrorCode.ROLE_ADMIN_NOT_UPDATE_MSG);
        }
        String name = sysRoleUpdBO.getName();
        if(!StringUtils.equals(sysRoleDomain.getName(),name)){
            List<SysRole> sysRoleList =  sysRoleBusiness.lambdaQuery().eq(SysRole::getName,name).list();
            if(CollectionUtils.isNotEmpty(sysRoleList)){
                throw new BusinessException(AdminErrorCode.ROLE_NAME_REPEAT_CODE,AdminErrorCode.ROLE_NAME_REPEAT_MSG);
            }
        }

        sysRoleDomain = new SysRole();
        //修改角色信息
        BeanUtils.copyProperties(sysRoleUpdBO, sysRoleDomain);
        sysRoleBusiness.updateById(sysRoleDomain);

        //删除角色与菜单之间的关系
        sysRoleMenuBusiness.lambdaUpdate().eq(SysRoleMenu::getRoleId,sysRoleDomain.getId()).remove();
        //保存角色与菜单之间的关系
        List<Integer> menuIds = sysRoleUpdBO.getMenuIds();
        //新增角色与菜单的关系表
        List<SysRoleMenu> sysRoleMenuDomainList = new ArrayList<>();
        for(Integer menuId : menuIds){
            SysRoleMenu sysRoleMenuSaveDomain = new SysRoleMenu();
            sysRoleMenuSaveDomain.setRoleId(sysRoleDomain.getId());
            sysRoleMenuSaveDomain.setMenuId(menuId);
            sysRoleMenuDomainList.add(sysRoleMenuSaveDomain);
        }
        sysRoleMenuBusiness.saveBatch(sysRoleMenuDomainList);

        return true;
    }

    @Override
    public Boolean updateSysRoleStatus(SysRoleUpdStatusBO sysRoleUpdStatusBO) throws BusinessException {
        List<SysRole> sysRoleList = sysRoleBusiness.listByIds(sysRoleUpdStatusBO.getIds());
        if(CollectionUtils.isEmpty(sysRoleList)){
            Optional<SysRole> opention = sysRoleList.stream().filter(a -> StringUtils.equals(a.getRoleCode(), Constants.ADMINISTRATORS)).findFirst();
            if(opention.isPresent()){
                throw new BusinessException(AdminErrorCode.ROLE_ADMIN_NOT_UPDATE_CODE,AdminErrorCode.ROLE_ADMIN_NOT_UPDATE_MSG);
            }
        }
        sysRoleBusiness.lambdaUpdate().set(SysRole::getRoleStatus,sysRoleUpdStatusBO.getRoleStatus())
                .in(SysRole::getId,sysRoleUpdStatusBO.getIds()).update();
        return true;
    }

    @Override
    public Boolean delSysRoleInfo(BaseIdsBO baseIdsBO) throws BusinessException {
        List<Integer> ids = baseIdsBO.getIds();
        List<SysRole> sysRoleList = sysRoleBusiness.listByIds(ids);
        if(CollectionUtils.isEmpty(sysRoleList)){
            Optional<SysRole> opention = sysRoleList.stream().filter(a -> StringUtils.equals(a.getRoleCode(), Constants.ADMINISTRATORS)).findFirst();
            if(opention.isPresent()){
                throw new BusinessException(AdminErrorCode.ROLE_ADMIN_NOT_UPDATE_CODE,AdminErrorCode.ROLE_ADMIN_NOT_UPDATE_MSG);
            }
        }
        List<SysUserRole> sysUserRoleList = sysUserRoleBusiness.lambdaQuery().eq(SysUserRole::getRoleId,ids).list();
        if(CollectionUtils.isEmpty(sysUserRoleList)){
            throw new BusinessException(AdminErrorCode.USER_ROLE_EXIST_CODE,AdminErrorCode.USER_ROLE_EXIST_MSG);
        }
        sysRoleBusiness.lambdaUpdate().in(SysRole::getId,ids).remove();
        sysRoleMenuBusiness.lambdaUpdate().in(SysRoleMenu::getRoleId,ids).remove();
        return true;
    }

    @Override
    public Page<SysRolePageDTO> pageSysRole(PageInfo<SysRolePageBO> bo) {

        return sysRoleBusiness.pageSysRole(bo);
    }

    @Override
    public List<SysRolePageDTO> getSysRoleAll(SysRolePageBO bo) {
        List<SysRole> roleList = sysRoleBusiness.lambdaQuery()
                .like(StringUtils.isNotEmpty(bo.getName()),SysRole::getName, bo.getName())
                .like(StringUtils.isNotEmpty(bo.getDescribes()),SysRole::getDescribes, bo.getDescribes())
                .eq(SysRole::getRoleStatus, YesOrNoEnum.YES.getCode())
                .ne(SysRole::getRoleCode, Constants.COMMON)
                .list();
        List<SysRolePageDTO> sysRolePageDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(roleList)){
            sysRolePageDTOList = BeanUtil.copyToList(roleList,SysRolePageDTO.class);
        }
        return sysRolePageDTOList;
    }
}
