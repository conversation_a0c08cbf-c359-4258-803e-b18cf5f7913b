package com.dbj.classpal.admin.service.remote.appalbum;

import com.dbj.classpal.books.client.api.album.AppAlbumMenusApi;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.dto.album.AppAlbumMenusTreeApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenusRemoteService
 * Date:     2025-04-18 15:16:59
 * Description: 表名： ,描述： 表
 */
@Component
public class AppAlbumMenusRemoteService {
    @Resource
    private AppAlbumMenusApi api;

    public AppAlbumMenusTreeApiDTO getAllAlbumMenusTree(AppAlbumMenusQueryApiBO bo) throws BusinessException {
        RestResponse<AppAlbumMenusTreeApiDTO> result = api.getAllAlbumMenusTree(bo);
        return result.returnProcess(result);
    }

    public Boolean reNameAlbumMenus(AppAlbumMenusReNameApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.reNameAlbumMenus(bo);
        return result.returnProcess(result);
    }

    public Boolean saveAlbumMenus(AppAlbumMenusSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.saveAlbumMenus(bo);
        return result.returnProcess(result);
    }

    public Boolean deleteAlbumMenus(AppAlbumMenusDeleteApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.deleteAlbumMenus(bo);
        return result.returnProcess(result);
    }

    public Boolean resetAlbumMenusOrderNum(AppAlbumMenusBatchMoveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.resetAlbumMenusOrderNum(bo);
        return result.returnProcess(result);
    }
}
