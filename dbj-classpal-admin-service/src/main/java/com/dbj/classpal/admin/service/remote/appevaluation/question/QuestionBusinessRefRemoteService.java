package com.dbj.classpal.admin.service.remote.appevaluation.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.question.QuestionBusinessRefApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefPageApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSortApiBO;
import com.dbj.classpal.books.client.dto.question.QuestionBusinessRefApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: QuestionBusinessRefRemoteService
 * Date:     2025-05-21 17:36:30
 * Description: 表名： ,描述： 表
 */
@Component
public class QuestionBusinessRefRemoteService {
    @Resource
    private QuestionBusinessRefApi questionBusinessRefApi;


    /**
     * 分页查询题目列表
     */
    public Page<QuestionBusinessRefApiDTO> pageList(PageInfo<QuestionBusinessRefPageApiBO> pageApiBO) throws BusinessException {
        RestResponse<Page<QuestionBusinessRefApiDTO>> result = questionBusinessRefApi.pageList(pageApiBO);
        return result.returnProcess(result);
    }

    /**
     * 新增题目业务关联关系
     */
    public Boolean saveQuestionBusinessRef(QuestionBusinessRefSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = questionBusinessRefApi.saveQuestionBusinessRef(bo);
        return result.returnProcess(result);
    }


    /**
     * 删除题目业务关联关系
     */
    public Boolean deleteQuestionBusinessRef(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = questionBusinessRefApi.deleteQuestionBusinessRef(bo);
        return result.returnProcess(result);
    }


    /**
     * 题目业务关联关系排序
     */
    public Boolean sortQuestionBusinessRef(QuestionBusinessRefSortApiBO bo) throws BusinessException{
        RestResponse<Boolean> result = questionBusinessRefApi.sortQuestionBusinessRef(bo);
        return result.returnProcess(result);
    }
}
