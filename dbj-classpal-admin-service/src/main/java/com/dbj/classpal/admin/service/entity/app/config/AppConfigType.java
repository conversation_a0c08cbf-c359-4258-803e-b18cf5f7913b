package com.dbj.classpal.admin.service.entity.app.config;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Classname AppConfigType
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 13:46:41
 * @Created by xuezhi
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("app_config_type")
public class AppConfigType extends BizEntity implements Serializable {

    @TableField("parent_id")
    @Schema(description = "父级ID")
    private Integer parentId;

    @TableField("type_code")
    @Schema(description = "类型编码")
    private String typeCode;

    @TableField("type_name")
    @Schema(description = "类型名称")
    private String typeName;

    @TableField("type_level")
    @Schema(description = "层级 1-一级 2-二级")
    private Integer typeLevel;

    @TableField("sort_order")
    @Schema(description = "排序号")
    private Integer sortOrder;

    @TableField("type_status")
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer typeStatus;

    @TableField("version")
    @Schema(description = "版本号")
    private Integer version;
}
    