package com.dbj.classpal.admin.service.service.sys.user;

import com.dbj.classpal.admin.common.bo.sys.user.AllocationDeptBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserHelpSaveBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public interface ISysUserHelpService {



    Boolean saveSysUserHelp(@Validated @RequestBody SysUserHelpSaveBO sysUser) throws BusinessException;

}
