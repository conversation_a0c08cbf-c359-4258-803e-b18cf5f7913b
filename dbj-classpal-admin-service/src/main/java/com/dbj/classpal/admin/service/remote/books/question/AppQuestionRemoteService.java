package com.dbj.classpal.admin.service.remote.books.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.question.QuestionApi;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionBlankContentApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:23:01
 */
@Component
public class AppQuestionRemoteService {
    @Resource
    private QuestionApi questionApi;

    /**
     * 分页查询题目列表
     */
    public Page<QuestionApiDTO> pageList(PageInfo<QuestionPageApiBO> pageApiBO) throws BusinessException {
        RestResponse< Page<QuestionApiDTO>> result = questionApi.pageList(pageApiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取题目详情
     */
    public QuestionApiDTO getQuestion(QuestionIdApiBO idApiBO) throws BusinessException {
        RestResponse<QuestionApiDTO> result = questionApi.getQuestion(idApiBO);
        return result.returnProcess(result);
    }

    /**
     * 根据分类ID获取题目列表
     */
    public List<QuestionApiDTO> getQuestionList(QuestionCategoryIdQueryApiBO idApiBO) throws BusinessException {
        RestResponse<List<QuestionApiDTO>> result = questionApi.getQuestionList(idApiBO);
        return result.returnProcess(result);
    }

    /**
     * 创建题目
     */
    public Integer createQuestion(QuestionSaveApiBO question) throws BusinessException {
        RestResponse<Integer> result = questionApi.createQuestion(question);
        return result.returnProcess(result);
    }

    /**
     * 更新题目
     *
     * @return
     */
    public Void updateQuestion(QuestionEditApiBO question) throws BusinessException {
        RestResponse<Void> result = questionApi.updateQuestion(question);
        return result.returnProcess(result);
    }

    /**
     * 删除题目
     */
    public Void batchDeleteQuestion(QuestionIdsApiBO idsApiBO) throws BusinessException {
        RestResponse<Void> result = questionApi.batchDeleteQuestion(idsApiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取题目正确答案
     */
    public List<String> getQuestionAnswer(QuestionIdApiBO idApiBO) throws BusinessException {
        RestResponse<List<String>> result = questionApi.getQuestionAnswer(idApiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取完形填空题内容
     */
    public QuestionBlankContentApiDTO getQuestionBlankContent(QuestionIdApiBO idApiBO) throws BusinessException {
        RestResponse<QuestionBlankContentApiDTO> result = questionApi.getQuestionBlankContent(idApiBO);
        return result.returnProcess(result);
    }

    /**
     * 批量复制题目
     */
    public Void batchCopyQuestion(QuestionCopyApiBO copyApiBO) throws BusinessException {
        RestResponse<Void> result = questionApi.batchCopyQuestion(copyApiBO);
        return result.returnProcess(result);
    }

    /**
     * 批量移动题目
     */
    public Void batchMoveQuestion(QuestionMoveApiBO moveApiBO) throws BusinessException {
        RestResponse<Void> result = questionApi.batchMoveQuestion(moveApiBO);
        return result.returnProcess(result);
    }

}