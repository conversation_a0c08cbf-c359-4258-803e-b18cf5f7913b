package com.dbj.classpal.admin.service.remote.audio;

import cn.hutool.core.collection.CollectionUtil;
import com.dbj.classpal.books.client.api.audio.AudioContextHintMusicApi;
import com.dbj.classpal.books.client.bo.audio.AudioContextHintAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.books.client.dto.audio.AudioContextHintDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 音频文本提示音
 * <AUTHOR>
 */
@Component
public class AudioContextHintMusicRemoteService {

    @Autowired
    private AudioContextHintMusicApi audioContextHintMusicApi;

    public Integer save(List<AudioContextHintAddBO> bo) throws BusinessException {
        if (bo == null || CollectionUtil.isEmpty(bo)) {
            throw new BusinessException("参数不能为空");
        }
        if (bo.size() > 20) {
            throw new BusinessException("不可超过20条");
        }
        RestResponse<Integer> result = audioContextHintMusicApi.save(bo);
        return result.returnProcess(result);
    }

    public Integer reorder(List<Integer> bo) throws BusinessException {
        if (bo == null || CollectionUtil.isEmpty(bo)) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Integer> result = audioContextHintMusicApi.reorder(bo);
        return result.returnProcess(result);
    }

    public List<AudioContextHintDTO> getDefinitionHint(AudioTypeBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<List<AudioContextHintDTO>> result = audioContextHintMusicApi.getDefinitionHint(bo);
        return result.returnProcess(result);
    }
}
