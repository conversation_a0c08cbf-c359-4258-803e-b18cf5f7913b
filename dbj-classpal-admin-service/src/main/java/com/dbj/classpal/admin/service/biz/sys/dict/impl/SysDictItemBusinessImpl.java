package com.dbj.classpal.admin.service.biz.sys.dict.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.admin.service.mapper.sys.dict.SysDictItemMapper;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据字典项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
public class SysDictItemBusinessImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements ISysDictItemBusiness {

    @Resource
    private SysDictItemMapper sysDictItemMapper;

    @Override
    public Page<SysDictItemDTO> pageSysDictItemInfo(PageInfo<SysDictItemBO> page) {
        return sysDictItemMapper.pageSysDictItemInfo(page.getPage(), page.getData());
    }
}
