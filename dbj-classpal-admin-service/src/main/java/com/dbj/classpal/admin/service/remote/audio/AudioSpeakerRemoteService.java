package com.dbj.classpal.admin.service.remote.audio;


import com.dbj.classpal.books.client.api.audio.AudioSpeakerApi;
import com.dbj.classpal.books.client.bo.audio.AudioSpeakerBO;
import com.dbj.classpal.books.client.dto.audio.AudioSpeakerDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 发音人模板
 * <AUTHOR>
 */
@Component
public class AudioSpeakerRemoteService {

    @Autowired
    private AudioSpeakerApi audioSpeakerApi;

    public List<AudioSpeakerDTO> list(AudioSpeakerBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<List<AudioSpeakerDTO>> result = audioSpeakerApi.list(bo);
        return result.returnProcess(result);
    }
}
