package com.dbj.classpal.admin.service.api.client.books.material;

import com.dbj.classpal.admin.client.api.books.material.AppMaterialUploadApi;
import com.dbj.classpal.admin.client.bo.books.material.MaterialFileConversionApiBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelMaterialSaveBO;
import com.dbj.classpal.admin.service.service.file.ISysFileImportExcelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.mq.bo.MaterialTransParamsBO;
import com.google.gson.Gson;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AppMaterialUploadApiImpl implements AppMaterialUploadApi {
    @Resource
    private ISysFileImportExcelService fileImportExcelService;

    @Override
    public RestResponse<Boolean> saveApiImportMaterialFile(MaterialFileConversionApiBO bo) throws Exception {
        SysFileImportExcelMaterialSaveBO saveBO = new SysFileImportExcelMaterialSaveBO();
        saveBO.setFileName(bo.getOriginalFileName());
        saveBO.setBusinessType(bo.getBusinessType());
        saveBO.setFileUrl(bo.getFileUrl());
        saveBO.setParamJson(bo.getParamJson());
        fileImportExcelService.saveImportMaterialFile(saveBO);
        return RestResponse.success(true);
    }
}
