package com.dbj.classpal.admin.service.biz.sys.role.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.role.SysRolePageBO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import com.dbj.classpal.admin.service.mapper.sys.role.SysRoleMapper;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
public class SysRoleBusinessImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleBusiness {

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Override
    public Page<SysRolePageDTO> pageSysRole(PageInfo<SysRolePageBO> bo) {
        return sysRoleMapper.pageSysRole(bo.getPage(),bo.getData());
    }
}
