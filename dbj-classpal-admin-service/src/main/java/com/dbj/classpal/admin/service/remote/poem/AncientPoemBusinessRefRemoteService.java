package com.dbj.classpal.admin.service.remote.poem;

import com.dbj.classpal.books.client.api.poem.AncientPoemBusinessRefApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSortBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className AncientPoemBusinessRefRemoteService
 * @description
 * @date 2025-05-27 14:17
 **/
@Component
public class AncientPoemBusinessRefRemoteService {

    @Resource
    private AncientPoemBusinessRefApi ancientPoemBusinessRefApi;


    public List<AncientPoemBusinessRefListDTO> listAncientPoemBusinessRef(AncientPoemBusinessRefListBO anAncientPoemBusinessRefList) throws BusinessException {
        RestResponse<List<AncientPoemBusinessRefListDTO>> results = ancientPoemBusinessRefApi.listAncientPoemBusinessRef(anAncientPoemBusinessRefList);
        return results.returnProcess(results);
    }

    /**
     * 保存
     */
    @PostMapping("/batchSave")
    public Boolean batchSave(@RequestBody AncientPoemBusinessRefSaveBO anotherPoemBusinessRefSaveBO) throws BusinessException {
        RestResponse<Boolean> results = ancientPoemBusinessRefApi.batchSave(anotherPoemBusinessRefSaveBO);
        return results.returnProcess(results);
    }
    /**
     * 删除
     */
    @PostMapping("/batchDelete")
    public Boolean batchDelete(@RequestParam List<Integer> ids) throws BusinessException {
        RestResponse<Boolean> results = ancientPoemBusinessRefApi.batchDelete(ids);
        return results.returnProcess(results);

    }
    /**
     * 排序
     */
    @PostMapping("/batchSort")
    public Boolean batchSort(List<AncientPoemBusinessRefSortBO> anotherPoemBusinessRefSortList) throws BusinessException {
        RestResponse<Boolean> results = ancientPoemBusinessRefApi.batchSort(anotherPoemBusinessRefSortList);
        return results.returnProcess(results);
    }
}
