package com.dbj.classpal.admin.service.remote.appmaterial;

import com.dbj.classpal.books.client.api.material.AppMaterialBusinessRefApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefQueryCommonApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefReNameApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.books.client.dto.material.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefRemoteService
 * Date:     2025-04-18 15:05:55
 * Description: 表名： ,描述： 表
 */
@Component
public class AppMaterialBusinessRefRemoteService {

    @Resource
    private AppMaterialBusinessRefApi businessRefApi;


    /**
     * 查询被引用素材列表
     *
     * @param bo
     * @return
     * @throws BusinessException
     */
    public List<AppMaterialBusinessRefMaterialQueryApiDTO> beRefBusinessList(AppMaterialBusinessRefQueryCommonApiBO bo) throws BusinessException {
        RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> list = businessRefApi.beRefBusinessList(bo);
        return list.returnProcess(list);
    }

    /**
     * 查询引用列表
     *
     * @param bo
     * @return
     * @throws BusinessException
     */
    public List<AppMaterialBusinessRefMaterialQueryApiDTO> refBusinessList(AppMaterialBusinessRefQueryCommonApiBO bo) throws BusinessException {
        RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> list = businessRefApi.refBusinessList(bo);
        return list.returnProcess(list);
    }



    /**
     * 查询引用列表下各素材类型数量统计
     * @param bo
     * @return
     * @throws BusinessException
     */
    public List<AppMaterialBusinessRefTypeCountApiDTO> getMaterialBusinessRefTypeCount(CommonIdApiBO bo) throws BusinessException {
        RestResponse<List<AppMaterialBusinessRefTypeCountApiDTO>> result = businessRefApi.getMaterialBusinessRefTypeCount(bo);
        return result.returnProcess(result);
    }


    /**
     * 保存资源关联关系
     * @param bo
     * @return
     */
    public Boolean saveAppMaterialBusinessRef(AppMaterialBusinessRefSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = businessRefApi.saveAppMaterialBusinessRef(bo);
        return result.returnProcess(result);
    }

    /**
     * 资源关联关系重命名
     * @param bo
     * @return
     */
    public Boolean reNameAppMaterialBusinessRef(AppMaterialBusinessRefReNameApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = businessRefApi.reNameAppMaterialBusinessRef(bo);
        return result.returnProcess(result);
    }

    /**
     * 移除资源关联关系
     * @param bo
     * @return
     */
    public Boolean removeAppMaterialBusinessRef(CommonIdApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = businessRefApi.removeAppMaterialBusinessRef(bo);
        return result.returnProcess(result);
    }


    /**
     * 批量移除资源关联关系
     * @param bo
     * @return
     */
    public Boolean removeBatchAppMaterialBusinessRef(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = businessRefApi.removeBatchAppMaterialBusinessRef(bo);
        return result.returnProcess(result);
    }

    /**
     * 资源关联关系排序
     * @param bo
     * @return
     */
    public Boolean changeAppMaterialBusinessRefOrderNum(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = businessRefApi.changeAppMaterialBusinessRefOrderNum(bo);
        return result.returnProcess(result);
    }


    /**
     * 资源关联-查看图书管理-图书资源引用
     * @param bo
     * @return
     */
    public BooksRefDirectApiDTO getAppMaterialBusinessRefBooks(CommonIdApiBO bo) throws BusinessException {
        RestResponse<BooksRefDirectApiDTO> result = businessRefApi.getAppMaterialBusinessRefBooks(bo);
        return result.returnProcess(result);
    }

    /**
     * 资源关联-查看内容管理-专辑引用
     * @param bo
     * @return
     */
    public AppMaterialBusinessRefAlbumDirectApiDTO getAppMaterialBusinessRefAlbum(CommonIdApiBO bo) throws BusinessException {
        RestResponse<AppMaterialBusinessRefAlbumDirectApiDTO> result = businessRefApi.getAppMaterialBusinessRefAlbum(bo);
        return result.returnProcess(result);
    }

    /**
     * 资源关联-查看图书管理-题库引用
     * @param bo
     * @return
     */
    public AppMaterialBusinessRefQuestionDirectApiDTO getAppMaterialBusinessRefQuestion(CommonIdApiBO bo) throws BusinessException {
        RestResponse<AppMaterialBusinessRefQuestionDirectApiDTO> result = businessRefApi.getAppMaterialBusinessRefQuestion(bo);
        return result.returnProcess(result);
    }
}
