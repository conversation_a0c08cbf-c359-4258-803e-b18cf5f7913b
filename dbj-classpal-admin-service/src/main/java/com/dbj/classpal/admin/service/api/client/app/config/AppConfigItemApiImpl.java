package com.dbj.classpal.admin.service.api.client.app.config;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.app.config.AppConfigSharePosterApiQueryBO;
import com.dbj.classpal.admin.client.api.app.config.AppConfigItemApi;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigCustomerApiQueryDTO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigFeedBackApiQueryDTO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigSharePosterApiQueryDTO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigTogetherApiQueryDTO;
import com.dbj.classpal.admin.client.enums.app.config.AppConfigTypeEnum;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.admin.common.enums.StatusEnum;
import com.dbj.classpal.admin.service.biz.app.config.IAppConfigItemBiz;
import com.dbj.classpal.admin.service.service.app.config.IAppConfigItemService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname AppConfigItemApiImpl
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-20 17:02:17
 * @Created by xuezhi
 */
@RestController
public class AppConfigItemApiImpl implements AppConfigItemApi {

    @Resource
    private IAppConfigItemService service;
    @Resource
    private IAppConfigItemBiz business;

    @Override
    public RestResponse<AppConfigTogetherApiQueryDTO> getTogether() throws BusinessException {
        AppConfigItemQueryDTO dto = service.getAppConfigItemByTypeCode(AppConfigTypeEnum.TOGETHER_STUDY_GROUP.getCode());
        if (ObjectUtils.isEmpty(dto) || dto.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_NO)) {
            return null;
        }
        AppConfigTogetherApiQueryDTO together = new AppConfigTogetherApiQueryDTO();
        BeanUtil.copyProperties(dto, together);
        return RestResponse.success(together);
    }

    @Override
    public RestResponse<AppConfigFeedBackApiQueryDTO> getFeedBack() throws BusinessException {
        AppConfigItemQueryDTO dto = service.getAppConfigItemByTypeCode(AppConfigTypeEnum.FEEDBACK_SUGGESTIONS.getCode());
        if (ObjectUtil.isNull(dto) || dto.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_NO)) {
            return null;
        }
        AppConfigFeedBackApiQueryDTO feedBack = new AppConfigFeedBackApiQueryDTO();
        BeanUtil.copyProperties(dto, feedBack);
        return RestResponse.success(feedBack);
    }

    @Override
    public RestResponse<List<AppConfigCustomerApiQueryDTO>> getCustomers() throws BusinessException {
        List<AppConfigItemQueryDTO> appConfigItemListByTypeCode = service.getAppConfigItemListByTypeCode(AppConfigTypeEnum.CUSTOMER.getCode());
        if(CollectionUtils.isEmpty(appConfigItemListByTypeCode)){
            return null;
        }
       return RestResponse.success(appConfigItemListByTypeCode.stream().filter(d -> d.getItemStatus() != null && d.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_YES.getCode())).map(d -> {
            AppConfigCustomerApiQueryDTO customer = new AppConfigCustomerApiQueryDTO();
            BeanUtil.copyProperties(d, customer);
            return customer;
        }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<List<AppConfigSharePosterApiQueryDTO>> getAllSharePoster() throws BusinessException {
        List<AppConfigItemQueryDTO> appConfigItemListByTypeCode = service.getAppConfigItemListByTypeCode(AppConfigTypeEnum.SHARE_POSTER.getCode());
        if(CollectionUtils.isEmpty(appConfigItemListByTypeCode)){
            return null;
        }
        return RestResponse.success(appConfigItemListByTypeCode.stream().filter(d -> d.getItemStatus() != null && d.getItemStatus().equals(StatusEnum.AGREEMENT_STATUS_YES.getCode())).map(d -> {
            AppConfigSharePosterApiQueryDTO customer = new AppConfigSharePosterApiQueryDTO();
            BeanUtil.copyProperties(d, customer);
            return customer;
        }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<Page<AppConfigSharePosterApiQueryDTO>> getPageSharePoster(PageInfo<AppConfigSharePosterApiQueryBO> pageable) throws BusinessException {
        return RestResponse.success(business.apiPageSharePosterInfo(pageable));
    }

}
    