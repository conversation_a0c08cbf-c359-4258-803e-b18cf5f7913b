package com.dbj.classpal.admin.service.biz.app.agreement.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.admin.client.bo.app.agreement.AppAgreementApiQueryBO;
import com.dbj.classpal.admin.client.dto.app.agreement.AppAgreementApiQueryDTO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.service.biz.app.agreement.IAppAgreementBiz;
import com.dbj.classpal.admin.service.entity.app.agreement.AppAgreement;
import com.dbj.classpal.admin.service.mapper.app.agreemennt.AppAgreementMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IAppAgreementBizImpl extends ServiceImpl<AppAgreementMapper,AppAgreement> implements IAppAgreementBiz {

    @Override
    public List<AppAgreementApiQueryDTO> getAllAppAgreement() {
        return List.of();
    }

    @Override
    public AppAgreementApiQueryDTO getAppAgreementById(Integer id) throws BusinessException {
        AppAgreementApiQueryDTO dto = new AppAgreementApiQueryDTO();
        if (id == null || StringUtils.isBlank(id.toString())) {
            throw new BusinessException(AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_CODE, AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_MSG);
        }
        return null;
    }

    @Override
    public AppAgreementApiQueryDTO getAppAgreementByTypeId(AppAgreementApiQueryBO bo) {
        return null;
    }
}
