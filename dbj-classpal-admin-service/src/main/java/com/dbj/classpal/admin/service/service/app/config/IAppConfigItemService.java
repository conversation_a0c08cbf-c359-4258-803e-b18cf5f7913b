package com.dbj.classpal.admin.service.service.app.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.app.config.*;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Classname AppConfigItemService
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:46:06
 * @Created by xuezhi
 */
@Mapper
public interface IAppConfigItemService {

    /**
     * 根据id查询配置项信息
     * @param id
     * @return
     */
    AppConfigItemQueryDTO getAppConfigItemById(Integer id) throws BusinessException;


    /**
     * 根据typeCode查询配置项信息
     * @param typeCode
     * @return
     */
    AppConfigItemQueryDTO getAppConfigItemByTypeCode(String typeCode) throws BusinessException;

    /**
     * 根据typeCode查询配置项信息列表
     * @param typeCode
     * @return
     */
    List<AppConfigItemQueryDTO> getAppConfigItemListByTypeCode(String typeCode) throws BusinessException;


    /**
     * 新增共学群信息
     * @param bo
     * @return
     */
    Boolean saveTogetherStudyGroup(AppConfigItemTogetherStudyGroupSaveBO bo) throws BusinessException;

    /**
     * 修改共学群信息
     * @param bo
     * @return
     */
    Boolean updateTogetherStudyGroup(AppConfigItemTogetherStudyGroupUpdateBO bo) throws BusinessException;

    /**
     * 新增反馈建议信息
     * @param bo
     * @return
     */
    Boolean saveFeedBack(AppConfigItemFeedBackSaveBO bo) throws BusinessException;

    /**
     * 修改反馈建议信息
     * @param bo
     * @return
     */
    Boolean updateFeedBack(AppConfigItemFeedBackUpdateBO bo) throws BusinessException;


    /**
     * 新增客服信息
     * @param bo
     * @return
     */
    Boolean saveCustomer(AppConfigItemCustomerSaveBO bo) throws BusinessException;

    /**
     * 修改客服信息
     * @param bo
     * @return
     */
    Boolean updateCustomer(AppConfigItemCustomerUpdateBO bo) throws BusinessException;


    /**
     * 新增分享海报信息
     * @param bo
     * @return
     */
    Boolean saveSharePoster(AppConfigItemSharePosterSaveBO bo) throws BusinessException;

    /**
     * 修改分享海报信息
     * @param bo
     * @return
     */
    Boolean updateSharePoster(AppConfigItemSharePosterUpdateBO bo) throws BusinessException;


    /**
     * 启用|禁用分享海报
     * @param id
     * @return
     */
    Boolean updateSharePosterStatus(Integer id) throws BusinessException;


    /**
     * 分页查询分享海报
     * @param page
     * @return
     */
    Page<AppConfigItemQueryDTO>PageSharePoster(PageInfo<AppConfigItemSharePosterQueryBO> page);


    /**
     * 删除分享海报
     * @param id
     * @return
     */
    Boolean delSharePoster(Integer id) throws BusinessException;

    /**
     * 批量启用分享海报
     * @param bo
     * @return
     */
    Boolean updateSharePosterEnableStatusList(AppConfigItemCommonQueryBO bo) throws BusinessException;


    /**
     * 批量禁用分享海报
     * @param bo
     * @return
     */
    Boolean updateSharePosterDisableStatusList(AppConfigItemCommonQueryBO bo) throws BusinessException;


    /**
     * 批量删除分享海报
     * @param bo
     * @return
     */
    Boolean delSharePosterList(AppConfigItemCommonQueryBO bo) throws BusinessException;
}
