package com.dbj.classpal.admin.service.service.app.agreement;

import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementQueryBO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementSaveBO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementUpdateBO;
import com.dbj.classpal.admin.common.dto.app.agreement.AppAgreementQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IAppAgreementService{

    /**
     * 根据id查询单个协议内容
     * @param id
     * @return
     */
    AppAgreementQueryDTO getAppAgreementById(Integer id) throws BusinessException;

    /**
     * 根据协议类型查询单个协议内容
     * @param bo
     * @return
     */
    AppAgreementQueryDTO getAppAgreementByTypeId(AppAgreementQueryBO bo);
    /**
     * 新增单个协议内容
     * @param bo
     * @return
     */
    Boolean saveAppAgreement(AppAgreementSaveBO bo) throws BusinessException;

    /**
     * 新增|修改单个协议内容
     * @param bo
     * @return
     */
    Boolean updateAppAgreement(AppAgreementUpdateBO bo) throws BusinessException;


    /**
     * 获取所有协议列表
     * @return
     */
    List<AppAgreementQueryDTO> getAllAppAgreements();
}
