package com.dbj.classpal.admin.service.service.sys.dept.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.client.enums.sys.dept.UserDeptTreeTypeEnum;
import com.dbj.classpal.admin.common.dto.sys.user.SysDeptUserNameDTO;
import com.dbj.classpal.admin.service.biz.sys.dept.ISysDeptBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserDeptBusiness;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptBO;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptUpdBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.sys.dept.SysDeptDTO;
import com.dbj.classpal.admin.common.dto.sys.dept.SysUserDeptGroupDTO;
import com.dbj.classpal.admin.service.entity.sys.dept.SysDept;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.dbj.classpal.admin.service.service.sys.dept.ISysDeptService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDeptService
 * @description
 * @date 2025-03-13 09:04
 **/
@Service
public class SysDeptServiceImpl implements ISysDeptService {

    @Resource
    private ISysDeptBusiness sysDeptBusiness;
    @Resource
    private ISysUserDeptBusiness sysUserDeptBusiness;

    @Override
    public SysDeptDTO getSysDeptInfo(SysDeptBO sysDeptBO) throws BusinessException {
        Integer id = sysDeptBO.getId();
        SysDept sysDeptDomain = sysDeptBusiness.getById(id);
        if(sysDeptDomain == null){
           throw new BusinessException(AdminErrorCode.DEPT_NOT_EXIST_CODE,AdminErrorCode.DEPT_NOT_EXIST_MSG);
        }
        SysDeptDTO sysPositionDTO = new SysDeptDTO();
        BeanUtil.copyProperties(sysDeptDomain,sysPositionDTO);
        return sysPositionDTO;
    }

    @Override
    public Boolean saveSysDept(SysDeptSaveBO bo) throws BusinessException {
        // 判断同级的名称是否重复
        Integer fatherId = bo.getFatherId();
        if(fatherId == null){
            throw new BusinessException(AdminErrorCode.DEPT_FATHER_ID_NOT_EXIST_CODE,AdminErrorCode.DEPT_FATHER_ID_NOT_EXIST_MSG);
        }
        SysDept parent = sysDeptBusiness.getById(fatherId);
        if(parent == null){
            throw new BusinessException(AdminErrorCode.DEPT_FATHER_NOT_EXIST_CODE,AdminErrorCode.DEPT_FATHER_NOT_EXIST_MSG);
        }
        List<SysDept> sysDeptDomainList =sysDeptBusiness.lambdaQuery().eq(SysDept::getFatherId,fatherId).eq(SysDept::getDeptName,bo.getDeptName()).list();

        if (CollectionUtils.isNotEmpty(sysDeptDomainList)) {
            throw new BusinessException(AdminErrorCode.DEPT_NAME_REPEAT_CODE,AdminErrorCode.DEPT_NAME_REPEAT_MSG);
        }
        SysDept sysDept = new SysDept();
        BeanUtil.copyProperties(bo, sysDept);
        sysDeptBusiness.save(sysDept);
        return true;
    }

    @Override
    public Boolean updateSysDept(SysDeptUpdBO bo) throws BusinessException {
        Integer id = bo.getId();
        SysDept sysDeptDomain = sysDeptBusiness.getById(id);
        if(sysDeptDomain == null){
            throw new BusinessException(AdminErrorCode.DEPT_NOT_EXIST_CODE,AdminErrorCode.DEPT_NOT_EXIST_MSG);
        }


        if (!StringUtils.equals(bo.getDeptName(), sysDeptDomain.getDeptName())) {
            List<SysDept> sysDeptDomainList =sysDeptBusiness.lambdaQuery().eq(SysDept::getFatherId,sysDeptDomain.getFatherId()).eq(SysDept::getDeptName,bo.getDeptName()).list();

            if (CollectionUtils.isNotEmpty(sysDeptDomainList)) {
                throw new BusinessException(AdminErrorCode.DEPT_NAME_REPEAT_CODE,AdminErrorCode.DEPT_NAME_REPEAT_MSG);
            }
        }
        sysDeptDomain = new SysDept();
        BeanUtil.copyProperties(bo,sysDeptDomain);
        sysDeptBusiness.updateById(sysDeptDomain);
        return true;
    }

    @Override
    public Boolean delSysDeptInfo(SysDeptBO reqBo) throws BusinessException {
        // 判断是否有子部门和部门里面是否有人
        long count = sysDeptBusiness.lambdaQuery().eq(SysDept::getFatherId,reqBo.getId()).count();
        if(count > 0){
            throw new BusinessException(AdminErrorCode.DEPT_CHILDREN_EXIST_CODE,AdminErrorCode.DEPT_CHILDREN_EXIST_MSG);
        }
        count = sysUserDeptBusiness.lambdaQuery().eq(SysUserDept::getDeptId,reqBo.getId()).count();
        if(count > 0){
            throw new BusinessException(AdminErrorCode.DEPT_CHILDREN_EXIST_CODE,AdminErrorCode.DEPT_CHILDREN_EXIST_MSG);
        }
        sysDeptBusiness.removeById(reqBo.getId());
        return true;
    }

    @Override
    public List<SysDeptDTO> listSysDept() {
        List<SysDept> sysDeptList = sysDeptBusiness.list();

        if(CollectionUtils.isEmpty(sysDeptList)){
            return null;
        }
        List<SysUserDeptGroupDTO> sysUserDeptGroupDTOList = sysUserDeptBusiness.getSysUserInfoByDeptIdGroup();
        Map<Integer,Integer> sysUserDeptGroupMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(sysUserDeptGroupDTOList)){
            sysUserDeptGroupMap = sysUserDeptGroupDTOList.stream().collect(Collectors.toMap(SysUserDeptGroupDTO::getDeptId,SysUserDeptGroupDTO::getNum));
        }

        List<SysDeptDTO> sysDeptDTOList = BeanUtil.copyToList(sysDeptList,SysDeptDTO.class);
        for(SysDeptDTO sysDeptDTO: sysDeptDTOList){
            sysDeptDTO.setUserNum(sysUserDeptGroupMap.getOrDefault(sysDeptDTO.getId(),0));
        }
        //统计人数
        return sysDeptDTOList;
    }

    @Override
    public List<SysDeptDTO> getSysDeptAll() {
        List<SysDept> sysDeptList = sysDeptBusiness.list();

        if(CollectionUtils.isEmpty(sysDeptList)){
            return null;
        }
        List<SysDeptDTO> allDepts = BeanUtil.copyToList(sysDeptList,SysDeptDTO.class);
        SysDeptDTO currentMenu = allDepts.stream().filter(a -> Objects.equals(a.getFatherId(),0)).findFirst().orElse(null);
        if(currentMenu == null){
            return null;
        }
        //顶级部门直接查总数
        List<Integer> deptIds = allDepts.stream().map(SysDeptDTO::getId).collect(Collectors.toList());
        sysUserDeptBusiness.lambdaQuery().in(SysUserDept::getDeptId,deptIds).list();

        List<SysUserDeptGroupDTO> sysUserDeptGroupDTOList = sysUserDeptBusiness.getSysUserInfoByDeptIdGroup();
        Map<Integer,Integer> sysUserDeptGroupMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(sysUserDeptGroupDTOList)){
            sysUserDeptGroupMap = sysUserDeptGroupDTOList.stream().collect(Collectors.toMap(SysUserDeptGroupDTO::getDeptId,SysUserDeptGroupDTO::getNum));
        }

        long userNum = sysUserDeptBusiness.count();
        currentMenu.setUserNum((int)userNum);
        SysDeptDTO rootMenuTree = getDeptTree(allDepts, currentMenu,sysUserDeptGroupMap,new HashMap<>());

        return Arrays.asList(rootMenuTree);
    }

    @Override
    public List<SysDeptDTO> getSysDeptUserTree() {



        List<SysDept> sysDeptList = sysDeptBusiness.list();

        if(CollectionUtils.isEmpty(sysDeptList)){
            return null;
        }
        List<SysDeptDTO> allDepts = BeanUtil.copyToList(sysDeptList,SysDeptDTO.class);
        SysDeptDTO currentMenu = allDepts.stream().filter(a -> Objects.equals(a.getFatherId(),0)).findFirst().orElse(null);
        if(currentMenu == null){
            return null;
        }
        //顶级部门直接查总数
        List<Integer> deptIds = allDepts.stream().map(SysDeptDTO::getId).collect(Collectors.toList());
        sysUserDeptBusiness.lambdaQuery().in(SysUserDept::getDeptId,deptIds).list();

        List<SysUserDeptGroupDTO> sysUserDeptGroupDTOList = sysUserDeptBusiness.getSysUserInfoByDeptIdGroup();
        Map<Integer,Integer> sysUserDeptGroupMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(sysUserDeptGroupDTOList)){
            sysUserDeptGroupMap = sysUserDeptGroupDTOList.stream().collect(Collectors.toMap(SysUserDeptGroupDTO::getDeptId,SysUserDeptGroupDTO::getNum));
        }
        List<SysDeptUserNameDTO>  sysDeptUserNameDTOList = sysUserDeptBusiness.getUserNameDeptList();
        Map<Integer,List<SysDeptUserNameDTO>> sysDeptUserNameMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(sysDeptUserNameDTOList)){
            sysDeptUserNameMap = sysDeptUserNameDTOList.stream().collect(Collectors.groupingBy(SysDeptUserNameDTO::getDeptId));
        }

        long userNum = sysUserDeptBusiness.count();
        currentMenu.setUserNum((int)userNum);
        currentMenu.setType(UserDeptTreeTypeEnum.DEPT.getCode());


        List<SysDeptUserNameDTO> sysDeptUserNameList = sysDeptUserNameMap.get(currentMenu.getId());
        if(CollectionUtils.isNotEmpty(sysDeptUserNameList)) {
            List<SysDeptDTO> childDTOList = currentMenu.getChildren();
            if (childDTOList == null) {
                childDTOList = new ArrayList<>();
            }
            for (SysDeptUserNameDTO sysDeptUserNameDTO : sysDeptUserNameList) {
                SysDeptDTO userChild = new SysDeptDTO();
                userChild.setType(UserDeptTreeTypeEnum.USER.getCode());
                userChild.setDeptName(sysDeptUserNameDTO.getNickName());
                userChild.setAvatar(sysDeptUserNameDTO.getAvatar());
                userChild.setId(sysDeptUserNameDTO.getUserId());
                userChild.setFatherId(currentMenu.getId());
                childDTOList.add(userChild);
            }

            currentMenu.setChildren(childDTOList);
        }


        SysDeptDTO rootMenuTree = getDeptTree(allDepts, currentMenu,sysUserDeptGroupMap,sysDeptUserNameMap);


        return Arrays.asList(rootMenuTree);
    }


    /**
     * 递归获取菜单树
     * @param allDepts
     * @param currentMenu
     * @return SysDeptDTO
     */
    public SysDeptDTO getDeptTree(List<SysDeptDTO> allDepts, SysDeptDTO currentMenu, Map<Integer,Integer> sysUserDeptGroupMap,Map<Integer,List<SysDeptUserNameDTO>> sysDeptUserNameMap) {
        if(CollectionUtils.isNotEmpty(allDepts)){
            List<SysDeptDTO> children = currentMenu.getChildren();
            if(children == null){
                children = new ArrayList<>();
            }
            for (SysDeptDTO child : allDepts) {

                child.setUserNum(sysUserDeptGroupMap.get(child.getId()));
                child.setType(UserDeptTreeTypeEnum.DEPT.getCode());
                if (Objects.equals(child.getFatherId(), currentMenu.getId())) {
                    List<SysDeptUserNameDTO> sysDeptUserNameDTOList = sysDeptUserNameMap.get(child.getId());
                    if(CollectionUtils.isNotEmpty(sysDeptUserNameDTOList)){
                        List<SysDeptDTO> childDTOList = child.getChildren();
                        if(childDTOList == null){
                            childDTOList = new ArrayList<>();
                        }
                        for(SysDeptUserNameDTO sysDeptUserNameDTO :sysDeptUserNameDTOList){
                            SysDeptDTO userChild = new SysDeptDTO();
                            userChild.setType(UserDeptTreeTypeEnum.USER.getCode());
                            userChild.setDeptName(sysDeptUserNameDTO.getNickName());
                            userChild.setAvatar(sysDeptUserNameDTO.getAvatar());
                            userChild.setId(sysDeptUserNameDTO.getUserId());
                            userChild.setFatherId(child.getId());
                            childDTOList.add(userChild);
                        }
                        child.setChildren(childDTOList);
                    }
                    children.add(child);
                    traverseTree(child,allDepts,sysUserDeptGroupMap,sysDeptUserNameMap);
                }
            }
            allDepts.removeAll(children);
            currentMenu.setChildren(children);
        }
        return currentMenu;
    }


    /**
     * 递归获取菜单树
     * @param currentDept 父节点id
     * @param allDepts 菜单树
     * @return
     */
    private void traverseTree(SysDeptDTO currentDept,List<SysDeptDTO> allDepts,Map<Integer,Integer> sysUserDeptGroupMap,Map<Integer,List<SysDeptUserNameDTO>> sysDeptUserNameMap) {
        if (currentDept != null) {
            List<SysDeptDTO> children = currentDept.getChildren();
            if(children == null){
                children = new ArrayList<>();
            }
            for (SysDeptDTO child : allDepts) {
                if (Objects.equals(child.getFatherId(), currentDept.getId())) {
                    child.setType(UserDeptTreeTypeEnum.DEPT.getCode());
                    child.setUserNum(sysUserDeptGroupMap.get(child.getId()));
                    List<SysDeptUserNameDTO> sysDeptUserNameDTOList = sysDeptUserNameMap.get(child.getId());

                    if(CollectionUtils.isNotEmpty(sysDeptUserNameDTOList)){
                        List<SysDeptDTO> childDTOList = child.getChildren();
                        if(childDTOList == null){
                            childDTOList = new ArrayList<>();
                        }
                        for(SysDeptUserNameDTO sysDeptUserNameDTO :sysDeptUserNameDTOList){
                            SysDeptDTO userChild = new SysDeptDTO();
                            userChild.setType(UserDeptTreeTypeEnum.USER.getCode());
                            userChild.setDeptName(sysDeptUserNameDTO.getNickName());
                            userChild.setId(sysDeptUserNameDTO.getUserId());
                            userChild.setFatherId(child.getId());
                            childDTOList.add(userChild);
                        }
                        child.setChildren(childDTOList);
                    }
                    children.add(child);

                    traverseTree(child,allDepts,sysUserDeptGroupMap,sysDeptUserNameMap);
                }
            }
            currentDept.setChildren(children);
        }
    }

}
