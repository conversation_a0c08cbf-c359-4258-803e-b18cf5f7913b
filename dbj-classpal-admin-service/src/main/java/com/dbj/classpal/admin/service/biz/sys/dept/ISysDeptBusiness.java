package com.dbj.classpal.admin.service.biz.sys.dept;

import com.dbj.classpal.admin.service.entity.sys.dept.SysDept;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface ISysDeptBusiness extends IService<SysDept> {


    /**
     * 根据id查询id下所有人部门
     *
     * @param deptId
     * @return SysDeptDomain
     */
    List<Integer> getSysDeptInfos(Integer deptId);



}
