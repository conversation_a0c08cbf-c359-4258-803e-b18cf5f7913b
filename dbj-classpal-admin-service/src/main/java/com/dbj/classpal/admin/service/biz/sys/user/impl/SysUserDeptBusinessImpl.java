package com.dbj.classpal.admin.service.biz.sys.user.impl;

import com.dbj.classpal.admin.common.dto.sys.dept.SysUserDeptGroupDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysDeptUserNameDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDeptDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.dbj.classpal.admin.service.mapper.sys.user.SysUserDeptMapper;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserDeptBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户与部门关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
public class SysUserDeptBusinessImpl extends ServiceImpl<SysUserDeptMapper, SysUserDept> implements ISysUserDeptBusiness {

    @Resource
    private SysUserDeptMapper sysUserDeptMapper;

    @Override
    public List<SysUserDeptGroupDTO> getSysUserInfoByDeptIdGroup() {
        return sysUserDeptMapper.getSysUserInfoByDeptIdGroup();
    }

    @Override
    public List<SysUserDeptDTO> getUserDeptList(List<Integer> userIds) {
        return sysUserDeptMapper.getUserDeptList(userIds);
    }

    @Override
    public List<SysDeptUserNameDTO> getUserNameDeptList() {
        return sysUserDeptMapper.getUserNameDeptList();
    }
}
