package com.dbj.classpal.admin.service.remote.books.book;


import com.dbj.classpal.books.client.api.books.AdminBooksCategoryApi;
import com.dbj.classpal.books.client.bo.books.BooksCategoryApiBO;
import com.dbj.classpal.books.client.dto.books.BooksCategoryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Component
public class BooksCategoryRemoteService {

    @Resource
    private AdminBooksCategoryApi adminBooksCategoryApi;


    public List<BooksCategoryApiDTO> list(BooksCategoryApiBO bookCategoryApiBO) throws BusinessException{
        RestResponse<List<BooksCategoryApiDTO>> result = adminBooksCategoryApi.list(bookCategoryApiBO);
        return result.returnProcess(result);
    }

    public Boolean sync() throws BusinessException{
        RestResponse<Boolean> result = adminBooksCategoryApi.sync();
        return result.returnProcess(result);
    }
}
