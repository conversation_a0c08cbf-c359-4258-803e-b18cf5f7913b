package com.dbj.classpal.admin.service.service.sys.dept;

import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptBO;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptUpdBO;
import com.dbj.classpal.admin.common.dto.sys.dept.SysDeptDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDeptService
 * @description
 * @date 2025-03-13 09:04
 **/
public interface ISysDeptService {


    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param
     * @return
     **/
    SysDeptDTO getSysDeptInfo(SysDeptBO sysDeptBO ) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param
     * @return
     **/
    Boolean saveSysDept(SysDeptSaveBO bo) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param
     * @return
     **/
    Boolean updateSysDept(SysDeptUpdBO bo) throws BusinessException;


    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @param reqBo
     * @return SysDeptDTO
     **/
    Boolean delSysDeptInfo(SysDeptBO reqBo) throws BusinessException;


    /**
     * <AUTHOR>
     * @Description 获取所有部门
     * @Date 2025/3/13 9:06
     * @return SysDeptDTO
     **/
    List<SysDeptDTO> listSysDept();

    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/3/13 9:06
     * @return SysDeptDTO
     **/
    List<SysDeptDTO> getSysDeptAll();
    /**
     * <AUTHOR>
     * @Description 获取部门热源树
     * @Date 2025/3/13 9:06
     * @return SysDeptDTO
     **/
    List<SysDeptDTO> getSysDeptUserTree();

}
