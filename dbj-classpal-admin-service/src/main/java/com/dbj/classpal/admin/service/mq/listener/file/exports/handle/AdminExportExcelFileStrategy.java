package com.dbj.classpal.admin.service.mq.listener.file.exports.handle;

import com.dbj.classpal.admin.service.biz.file.ISysFileExportExcelBusiness;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.dto.CommonExcelBO;
import com.dbj.classpal.framework.utils.dto.CommonExportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.file.ExcelFileStrategy;
import com.dbj.classpal.framework.utils.file.ExcelHelp;
import com.dbj.classpal.framework.utils.file.IExportExcelFileStrategy;
import com.dbj.classpal.framework.utils.param.ExportExcelParam;
import jakarta.annotation.Resource;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className AdminExcelFileStrategy
 * @description
 * @date 2025-03-24 15:58
 **/
public abstract class AdminExportExcelFileStrategy<T extends CommonExportExcelBO> extends ExcelHelp implements IExportExcelFileStrategy<T> {



    @Resource
    private ISysFileExportExcelBusiness sysFileExportBusiness;




    /**
     * 系统失败时调用 犹豫该处理是再各系统中进行，所有不同系统的调用方式不一样
     * @param fileDomain 需要修改的文件数据
     */
    public void handleProcessingFailedSys(SysFileExportExcelBO fileDomain) {
        sysFileExportBusiness.lambdaUpdate().eq(SysFileExportExcel::getId, fileDomain.getId())
                .set(SysFileExportExcel::getErrorMsg, fileDomain.getErrorMsg())
                .set(SysFileExportExcel::getHandleEndTime, new Date())
                .set(SysFileExportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_SYS.getCode()).update();
    }

    /**
     * 修改为处理完成
     * @param fileDomain
     */
    @Override
    public void updateFileProcessed(List<T> sysDictExportBOList, SysFileExportExcelBO fileDomain) {
        //修改状态 然后上传oss
        ExportExcelParam exportExcelParam = new ExportExcelParam();
        exportExcelParam.setData(sysDictExportBOList);
        String url = updateOss(exportExcelParam,fileDomain.getFileName());

        sysFileExportBusiness.lambdaUpdate().eq(SysFileExportExcel::getId, fileDomain.getId())
                .set(SysFileExportExcel::getFileUrl, url)
                .set(SysFileExportExcel::getStatus, FileStatusEnum.PROCESSED.getCode())
                .set(SysFileExportExcel::getHandleEndTime, new Date())
                .update();
    }
}