package com.dbj.classpal.admin.service.service.sys.help.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.biz.sys.help.ISysHelpItemsBusiness;
import com.dbj.classpal.admin.service.biz.sys.menu.ISysMenuBusiness;
import com.dbj.classpal.admin.common.bo.sys.help.BatchUpdHelpStatusBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsDetailBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsSaveBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsUpdBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.help.SysHelpItemsDTO;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserHelpBusiness;
import com.dbj.classpal.admin.service.entity.sys.help.SysHelpItems;
import com.dbj.classpal.admin.service.entity.sys.menu.SysMenu;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserHelp;
import com.dbj.classpal.admin.service.service.sys.help.ISysHelpItemsService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysHelpItemsService
 * @description
 * @date 2025-03-13 09:04
 **/
@Service
public class SysHelpItemsServiceImpl implements ISysHelpItemsService {

    @Resource
    private ISysHelpItemsBusiness sysHelpItemsBusiness;
    @Resource
    private ISysMenuBusiness SysMenuBusiness;

    @Resource
    private ISysUserHelpBusiness sysUserHelpBusiness;

    @Override
    public SysHelpItemsDTO getSysHelpItemsInfo(SysHelpItemsDetailBO sysHelpItemsBO) throws BusinessException {
        Integer id = sysHelpItemsBO.getId();
        SysHelpItems sysHelpItemsDomain = sysHelpItemsBusiness.getById(id);
        if(sysHelpItemsDomain == null){
           throw new BusinessException(AdminErrorCode.HELP_PAGE_NOT_EXIST_CODE,AdminErrorCode.HELP_PAGE_NOT_EXIST_MSG);
        }
        SysHelpItemsDTO sysHelpItemsDTO = new SysHelpItemsDTO();
        BeanUtil.copyProperties(sysHelpItemsDomain,sysHelpItemsDTO);
        return sysHelpItemsDTO;
    }

    @Override
    public SysHelpItemsDTO getSysHelpItemsInfoByPageId(Integer menuPageId) {
        List<SysHelpItems> sysHelpItemsDomain = sysHelpItemsBusiness.lambdaQuery().eq(SysHelpItems::getMenuPageId,menuPageId).list();
        SysHelpItemsDTO sysHelpItemsDTO = null;
        if(CollectionUtils.isNotEmpty(sysHelpItemsDomain)){
            sysHelpItemsDTO = BeanUtil.copyProperties(sysHelpItemsDomain.get(0),SysHelpItemsDTO.class);
            //获取当前用户的
            SysUserHelp sysUserHelp = sysUserHelpBusiness.lambdaQuery().eq(SysUserHelp::getUserId, ContextUtil.getUserIdInt())
                    .eq(SysUserHelp::getHelpId,sysHelpItemsDTO.getId()).one();
            if(sysUserHelp != null){
                sysHelpItemsDTO.setIsFold(sysUserHelp.getIsFold());
            }else {
                sysHelpItemsDTO.setIsFold(YesOrNoEnum.NO.getCode());
            }
        }
        return sysHelpItemsDTO;
    }

    @Override
    public Boolean saveSysHelpItems(SysHelpItemsSaveBO bo) throws BusinessException {
        // 判断同级的名称是否重复
        Integer menuPageId = bo.getMenuPageId();
        List<SysHelpItems> sysHelpItemsList =  sysHelpItemsBusiness.lambdaQuery().eq(SysHelpItems::getId,menuPageId).list();
        if(CollectionUtils.isNotEmpty(sysHelpItemsList)){
            throw new BusinessException(AdminErrorCode.HELP_PAGE_EXIST_CODE,AdminErrorCode.HELP_PAGE_EXIST_MSG);
        }

        SysHelpItems sysHelpItems = new SysHelpItems();
        BeanUtil.copyProperties(bo, sysHelpItems);
        sysHelpItemsBusiness.save(sysHelpItems);
        return true;
    }

    @Override
    public Boolean updateSysHelpItems(SysHelpItemsUpdBO bo) throws BusinessException {
        Integer id = bo.getId();
        SysHelpItems sysHelpItemsDomain = sysHelpItemsBusiness.getById(id);
        if(sysHelpItemsDomain == null){
            throw new BusinessException(AdminErrorCode.HELP_PAGE_NOT_EXIST_CODE,AdminErrorCode.HELP_PAGE_NOT_EXIST_MSG);
        }
        if (!Objects.equals(bo.getMenuPageId(), sysHelpItemsDomain.getMenuPageId())) {
            List<SysHelpItems> SysHelpItemsDomainList =sysHelpItemsBusiness.lambdaQuery().eq(SysHelpItems::getMenuPageId,bo.getMenuPageId()).list();
            if (CollectionUtils.isNotEmpty(SysHelpItemsDomainList)) {
                throw new BusinessException(AdminErrorCode.HELP_PAGE_EXIST_CODE,AdminErrorCode.HELP_PAGE_EXIST_MSG);
            }
        }
        sysHelpItemsDomain = new SysHelpItems();
        BeanUtil.copyProperties(bo,sysHelpItemsDomain);
        sysHelpItemsBusiness.updateById(sysHelpItemsDomain);
        return true;
    }

    @Override
    public Boolean batchUpdHelpStatus(BatchUpdHelpStatusBO bo) throws BusinessException {
        sysHelpItemsBusiness.lambdaUpdate().set(SysHelpItems::getStatus,bo.getStatus()).in(SysHelpItems::getId,bo.getIds()).update();
        return true;
    }

    @Override
    public Boolean batchDelHelp(BatchUpdHelpStatusBO bo)  {
        sysHelpItemsBusiness.removeBatchByIds(bo.getIds());
        return true;
    }

    @Override
    public Page<SysHelpItemsDTO> pageSysHelpItems(PageInfo<SysHelpItemsBO> bo){
        Page<SysHelpItemsDTO> sysHelpItemsDTOPage = sysHelpItemsBusiness.pageSysHelpItems(bo);
        List<SysHelpItemsDTO> sysHelpItemsDTOList = sysHelpItemsDTOPage.getRecords();
        if(CollectionUtils.isNotEmpty(sysHelpItemsDTOList)){
            List<SysMenu> sysMenuDTOList =  SysMenuBusiness.listByIds(sysHelpItemsDTOList.stream().map(SysHelpItemsDTO::getMenuPageId).collect(Collectors.toList()));
            Map<Integer, String> sysMenuMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(sysMenuDTOList)){
                sysMenuMap = sysMenuDTOList.stream().collect(Collectors.toMap(SysMenu::getId, SysMenu::getName));
            }
            for(SysHelpItemsDTO sysHelpItemDTO : sysHelpItemsDTOList){
                sysHelpItemDTO.setMenuShortName(sysMenuMap.get(sysHelpItemDTO.getMenuPageId()));
            }
        }
        return sysHelpItemsDTOPage;
    }


}
