package com.dbj.classpal.admin.service.api.client.file.importfile;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.api.file.importfile.ExcelFileImportApi;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportSaveApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelQueryBO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.admin.service.service.file.ISysFileImportExcelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.admin.common.constant.AdminErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileImportApiImpl
 * Date:     2025-04-08 11:36:42
 * Description: 表名： ,描述： 表
 */
@RestController
public class ExcelFileImportApiImpl implements ExcelFileImportApi {

    @Resource
    private ISysFileImportExcelBusiness business;
    @Resource
    private ISysFileImportExcelService importExcelService;

    @Override
    public RestResponse<ExcelFileImportQueryApiDTO> getExcelFileImportById(ExcelFileImportQueryApiBO bo) throws BusinessException {
        if (bo == null || bo.getId() == null) {
            throw new BusinessException(PARAM_ERROR_CODE,PARAM_ERROR_MSG);
        }
        ExcelFileImportQueryApiDTO dto = new ExcelFileImportQueryApiDTO();
        SysFileImportExcel byId = business.getById(bo.getId());
        if (!ObjectUtils.isEmpty(byId)) {
            BeanUtil.copyProperties(byId, dto);
        }
        return RestResponse.success(dto);
    }

    @Override
    public RestResponse<SysFileImportExcelBO> getById(Integer id) {
        SysFileImportExcel fileDomain = business.getById(id);
        return RestResponse.success(BeanUtil.copyProperties(fileDomain, SysFileImportExcelBO.class));
    }

    @Override
    public RestResponse<Boolean> handleProcessingFailedSys(SysFileImportExcelBO fileDomain){
        business.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getErrorMsg, fileDomain.getErrorMsg())
                .set(SysFileImportExcel::getHandleEndTime,LocalDateTime.now())
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_SYS.getCode()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> handleProcessing(SysFileImportExcelBO fileDomain) {
        business.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSING.getCode())
                .set(SysFileImportExcel::getHandleStartTime,LocalDateTime.now())
                .update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateFileProcessed(SysFileImportExcelBO fileDomain) {
        business.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getProcessedUrl, fileDomain.getProcessedUrl())
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_BUSINESS.getCode())
                .set(SysFileImportExcel::getErrorNum, fileDomain.getErrorNum())
                .set(SysFileImportExcel::getHandleEndTime,LocalDateTime.now())
                .set(SysFileImportExcel::getSubNum,fileDomain.getSubNum())
                .update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> handleProcessingFailedBusiness(SysFileImportExcelBO fileDomain) {
        business.lambdaUpdate().eq(SysFileImportExcel::getId, fileDomain.getId())
                .set(SysFileImportExcel::getSubNum, fileDomain.getSubNum())
                .set(SysFileImportExcel::getErrorNum, 0)
                .set(SysFileImportExcel::getHandleEndTime, LocalDateTime.now())
                .set(SysFileImportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_BUSINESS.getCode())
                .set(SysFileImportExcel::getProcessedUrl, fileDomain.getProcessedUrl()).update();

        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> saveExcelFileImportById(ExcelFileImportSaveApiBO bo) throws BusinessException {
        SysFileImportExcel sysFileImportExcel = new SysFileImportExcel();
        sysFileImportExcel.setFileName(bo.getFileName());
        sysFileImportExcel.setFileUrl(bo.getFileUrl());
        sysFileImportExcel.setProcessedUrl(bo.getProcessedUrl());
        sysFileImportExcel.setHandleStartTime(bo.getHandleStartTime());
        sysFileImportExcel.setHandleEndTime(bo.getHandleEndTime());
        sysFileImportExcel.setStatus(bo.getStatus());
        return RestResponse.success(business.save(sysFileImportExcel));
    }

    @Override
    public RestResponse<Boolean> checkProcessMd5File(ExcelFileImportQueryApiBO bo) throws BusinessException {
        SysFileImportExcelQueryBO sysFileImportExcelQueryBO = new SysFileImportExcelQueryBO();
        BeanUtil.copyProperties(bo, sysFileImportExcelQueryBO);
        List<SysFileImportExcelDTO> sysFileImportExcelDTOS = business.checkProcessMd5File(sysFileImportExcelQueryBO);
        return RestResponse.success(!sysFileImportExcelDTOS.isEmpty());
    }


    @Override
    public RestResponse<Boolean> updateExcelFileImportById(ExcelFileImportUpdateApiBO bo) throws BusinessException{
        if (bo == null || bo.getId() == null) {
            throw new BusinessException(PARAM_ERROR_CODE,PARAM_ERROR_MSG);
        }
        SysFileImportExcel sysFileImportExcel = new SysFileImportExcel();
        BeanUtil.copyProperties(bo, sysFileImportExcel);
        sysFileImportExcel.setUpdateTime(LocalDateTime.now());
        if (!business.updateById(sysFileImportExcel)) {
            throw new BusinessException(EXPORT_EXCEL_FILE_UPDATE_FILE_CODE,EXPORT_EXCEL_FILE_UPDATE_FILE_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<ExcelFileImportQueryApiDTO> getByAnalysisJobId(ExcelFileImportQueryApiBO bo) throws BusinessException {
        if (bo == null || bo.getAnalysisSubmitJobId() == null) {
            throw new BusinessException(PARAM_ERROR_CODE,PARAM_ERROR_MSG);
        }
        List<ExcelFileImportQueryApiDTO> list = importExcelService.getByAnalysisJobId(bo);
        ExcelFileImportQueryApiDTO dto = new ExcelFileImportQueryApiDTO();
        if (CollectionUtils.isNotEmpty(list)){
            BeanUtil.copyProperties(list.get(0), dto);
        }
        return RestResponse.success(dto);
    }

    @Override
    public RestResponse<ExcelFileImportQueryApiDTO> getByTransCodeJobId(ExcelFileImportQueryApiBO bo) throws BusinessException {
        if (bo == null || bo.getTransSubmitJobId() == null) {
            throw new BusinessException(PARAM_ERROR_CODE,PARAM_ERROR_MSG);
        }
        List<ExcelFileImportQueryApiDTO> list = importExcelService.getByTransCodeJobId(bo);
        ExcelFileImportQueryApiDTO dto = new ExcelFileImportQueryApiDTO();
        if (CollectionUtils.isNotEmpty(list)){
            BeanUtil.copyProperties(list.get(0), dto);
        }
        return RestResponse.success(dto);
    }

    @Override
    public RestResponse<List<ExcelFileImportQueryApiDTO>> getExcelFileImportListByStatus(ExcelFileImportQueryApiBO bo) throws BusinessException{
        if (bo == null || bo.getStatus() == null) {
            throw new BusinessException(PARAM_ERROR_CODE,PARAM_ERROR_MSG);
        }
        Page<SysFileImportExcel> page = new Page<>(1, 10);
        LambdaQueryWrapper<SysFileImportExcel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysFileImportExcel::getStatus, bo.getStatus());
        queryWrapper.isNotNull(SysFileImportExcel::getTransSubmitJobId);
        queryWrapper.orderByAsc(SysFileImportExcel::getHandleEndTime);
        IPage<SysFileImportExcel> resultPage = business.page(page, queryWrapper);
        List<SysFileImportExcel> list = resultPage.getRecords();
        return RestResponse.success(list.stream().map(d -> {
            ExcelFileImportQueryApiDTO dto = new ExcelFileImportQueryApiDTO();
            BeanUtil.copyProperties(d, dto);
            return dto;
        }).collect(Collectors.toList()));
    }
}
