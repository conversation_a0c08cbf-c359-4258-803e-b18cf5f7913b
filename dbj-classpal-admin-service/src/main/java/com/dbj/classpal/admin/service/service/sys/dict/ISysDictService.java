package com.dbj.classpal.admin.service.service.sys.dict;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.app.dict.DictItemApiQueryBo;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.dict.DictItemQueryBo;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictDetailBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictUpdBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictUpdStatusBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDictService
 * @description
 * @date 2025-03-14 15:58
 **/
public interface ISysDictService {


    /**
     * 获取单个SysDict数据
     * @Title: saveSysDict
     * @Description: 添加SysDict数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    SysDictDTO getSysDictInfo(SysDictDetailBO reqBo) throws BusinessException;

    /**
     * 添加SysDict数据
     * @Title: saveSysDict
     * @Description: 添加SysDict数据
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean saveSysDict(SysDictSaveBO bo) throws BusinessException;

    /**
     * 修改数据
     * @Title: UpdateSysDict
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    Boolean updateSysDict(SysDictUpdBO bo) throws BusinessException;

    /**
     * 修改数据
     * @Title: UpdateSysDict
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    Boolean batchUpdateStatus(SysDictUpdStatusBO bo) throws BusinessException;


    /**
     * 批量删除字典数据
     * @Title: delSysDictInfo
     * @Description: 添加SysDict数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean batchDelSysDictInfo(BaseIdsBO baseIdsBO) throws BusinessException;


    /**
     * <AUTHOR>
     * @Description  分页获取数据
     * @Date 2025/3/17 8:43
     * @param page
     * @return Page
     **/
    Page<SysDictDTO> pageSysDictInfo(PageInfo<SysDictBO> page) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description 获取所有字典数据
     * @Date 2025/3/17 9:23
     * @return SysDictDTO
     **/
    Map<String,List<SysDictItemDTO>> getSysDictInfoAll() throws BusinessException;

    /**
     * <AUTHOR>
     * @Description 刷新字典缓存
     * @Date 2025/3/17 9:23
     * @return SysDictDTO
     **/
    Boolean refreshCacheDict() throws BusinessException;
    SysDictItemDTO getAdjacentDictItem(DictItemQueryBo queryBo) throws BusinessException;

    SysDictItemApiDTO getDictItem(DictItemQueryBo queryBo) throws BusinessException;
}
