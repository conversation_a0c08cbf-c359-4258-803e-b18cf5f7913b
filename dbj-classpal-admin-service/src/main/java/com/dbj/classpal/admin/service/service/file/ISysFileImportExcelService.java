package com.dbj.classpal.admin.service.service.file;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.file.importfile.*;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 导入文件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISysFileImportExcelService {

    /**
     * 获取一个File
     *
     * @param id
     * @return RestResponse<FileDTO>
     */
    SysFileImportExcelDTO getImportFileInfo(Integer id) throws BusinessException;

    /**
     * 新增File
     *
     * @param bo
     * @return RestResponse<Integer>
     */
    Integer saveImportFile(SysFileImportExcelSaveBO bo) throws Exception;


    /**
     * 上传素材中心文件
     *
     * @param bo
     * @return RestResponse<Integer>
     */
    Integer saveImportMaterialFile(SysFileImportExcelMaterialSaveBO bo) throws Exception;
    /**
     * 取消上传
     * @Title: 取消上传
     * @Description: 取消上传
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean cancel(BaseIdBO bo);
    /**
     * 清空接口
     * @Title: 清空接口
     * @Description: 清空接口
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Boolean deleteClear(SysFileImportExcelClearBO bo);

    List<SysFileImportExcelCountDTO> sysFileImportExcelCount();

    /**
     * 获取用户最近上传的记录
     * @Title: 获取用户最近上传的记录
     * @Description: 获取用户最近上传的记录
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    Page<SysFileImportExcelDTO> pageSysFileImportExcel(PageInfo<SysFileImportExcelBO> page);

    /**
     * top3
     * @param bo
     * @return
     */
    List<SysFileImportExcelDTO> getFileInfoTop3(SysFileImportExcelTypeBO bo);

    /**
     * 根据分析模板任务id查询导入信息
     * @param bo
     * @return
     */
    List<ExcelFileImportQueryApiDTO> getByAnalysisJobId(ExcelFileImportQueryApiBO bo);


    /**
     * 根据转码任务id查询导入信息
     * @param bo
     * @return
     */
    List<ExcelFileImportQueryApiDTO> getByTransCodeJobId(ExcelFileImportQueryApiBO bo);

}
