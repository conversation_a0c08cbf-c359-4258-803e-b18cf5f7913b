package com.dbj.classpal.admin.service.entity.sys.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数据字典主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dict")
@Tag(name="SysDict对象", description="数据字典主表")
public class SysDict extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description ="字典名称")
    @TableField("dict_name")
    private String dictName;

    @Schema(description ="字典标识")
    @TableField("dict_code")
    private String dictCode;

    @Schema(description ="启用状态 1-启用 0-停用")
    @TableField("status")
    private Integer status;

    @Schema(description ="字典描述")
    @TableField("description")
    private String description;

    @Schema(description ="排序权重")
    @TableField("sort")
    private Integer sort;


    @Schema(description ="版本号")
    @TableField("version")
    private Integer version;


}
