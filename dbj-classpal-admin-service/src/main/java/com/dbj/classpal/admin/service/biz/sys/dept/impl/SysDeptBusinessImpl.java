package com.dbj.classpal.admin.service.biz.sys.dept.impl;

import com.dbj.classpal.admin.service.entity.sys.dept.SysDept;
import com.dbj.classpal.admin.service.mapper.sys.dept.SysDeptMapper;
import com.dbj.classpal.admin.service.biz.sys.dept.ISysDeptBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
public class SysDeptBusinessImpl extends ServiceImpl<SysDeptMapper, SysDept> implements ISysDeptBusiness {

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Override
    public List<Integer> getSysDeptInfos(Integer deptId) {
        return sysDeptMapper.getSysDeptInfos(deptId);
    }
}
