package com.dbj.classpal.admin.service.mapper.sys.dict;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据字典主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Mapper
public interface SysDictMapper extends BaseMapper<SysDict> {

    Page<SysDictDTO> pageSysDictInfo(Page page, @Param("bo") SysDictBO reqBo);

    /**
     * 不分页查询
     * @param sysDictBO
     * @return
     */
    List<SysDictDTO> listSysDictInfo( @Param("bo")SysDictBO sysDictBO);

}

