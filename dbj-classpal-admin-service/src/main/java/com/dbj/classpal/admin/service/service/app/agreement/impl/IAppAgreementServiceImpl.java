package com.dbj.classpal.admin.service.service.app.agreement.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dbj.classpal.admin.client.enums.app.agreement.AppAgreementEnum;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.service.biz.app.agreement.IAppAgreementBiz;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementQueryBO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementSaveBO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementUpdateBO;
import com.dbj.classpal.admin.common.dto.app.agreement.AppAgreementQueryDTO;
import com.dbj.classpal.admin.service.entity.app.agreement.AppAgreement;
import com.dbj.classpal.admin.common.enums.StatusEnum;
import com.dbj.classpal.admin.service.service.app.agreement.IAppAgreementService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class IAppAgreementServiceImpl implements IAppAgreementService {
    @Autowired
    private IAppAgreementBiz business;

    @Override
    public AppAgreementQueryDTO getAppAgreementById(Integer id) throws BusinessException {
        if (id == null || StringUtils.isBlank(id.toString())) {
            throw new BusinessException(AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_CODE, AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_MSG);
        }
        AppAgreement appAgreement = business.getById(id);
        if (ObjectUtil.isNull(appAgreement)){
            return null;
        }
        AppAgreementQueryDTO dto = new AppAgreementQueryDTO();
        BeanUtil.copyProperties(appAgreement, dto);
        StatusEnum statusEnum = StatusEnum.getByCode(dto.getAgreementStatus());
        if (statusEnum != null) {
            dto.setAgreementStatusStr(statusEnum.getName());
        }
        AppAgreementEnum agreementEnum = AppAgreementEnum.getByCode(dto.getAgreementType());
        if (agreementEnum != null) {
            dto.setAgreementTypeStr(agreementEnum.getName());
        }
        return dto;
    }

    @Override
    public AppAgreementQueryDTO getAppAgreementByTypeId(AppAgreementQueryBO bo) {
        AppAgreement appAgreement = business.lambdaQuery().eq(AppAgreement::getAgreementType,bo.getAgreementType()).one();
        if (ObjectUtil.isNull(appAgreement)){
            return null;
        }
        AppAgreementQueryDTO dto = new AppAgreementQueryDTO();
        BeanUtil.copyProperties(appAgreement, dto);
        StatusEnum statusEnum = StatusEnum.getByCode(dto.getAgreementStatus());
        if (statusEnum != null) {
            dto.setAgreementStatusStr(statusEnum.getName());
        }
        AppAgreementEnum agreementEnum = AppAgreementEnum.getByCode(dto.getAgreementType());
        if (agreementEnum != null) {
            dto.setAgreementTypeStr(agreementEnum.getName());
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAppAgreement(AppAgreementSaveBO bo) throws BusinessException {
        AppAgreement one = business.lambdaQuery().eq(AppAgreement::getAgreementType,bo.getAgreementType()).one();
        if (ObjectUtils.isNotEmpty(one)) {
            throw new BusinessException(AdminErrorCode.APP_AGREEMENT_EXIST_CODE, AdminErrorCode.APP_AGREEMENT_EXIST_MSG);
        }
        AppAgreement appAgreement = new AppAgreement();
        BeanUtil.copyProperties(bo, appAgreement);
        appAgreement.setAgreementStatus(StatusEnum.AGREEMENT_STATUS_YES.getCode());
        return business.save(appAgreement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAppAgreement(AppAgreementUpdateBO bo) throws BusinessException {
        if (bo.getId() == null || StringUtils.isBlank(bo.getId().toString())) {
            throw new BusinessException(AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_CODE, AdminErrorCode.APP_AGREEMENT_PARAM_ERROR_MSG);
        }
        AppAgreement appAgreement = new AppAgreement();
        BeanUtil.copyProperties(bo, appAgreement);
        if (!business.updateById(appAgreement)){
            throw new BusinessException(AdminErrorCode.APP_COMMON_UPDATE_FAIL_CODE, AdminErrorCode.APP_COMMON_UPDATE_FAIL_MSG);
        }
        return true;
    }

    @Override
    public List<AppAgreementQueryDTO> getAllAppAgreements() {
        List<AppAgreement> list = business.list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(dto -> {
            AppAgreementQueryDTO queryDTO = new AppAgreementQueryDTO();
            BeanUtil.copyProperties(dto, queryDTO);
            StatusEnum statusEnum = StatusEnum.getByCode(dto.getAgreementStatus());
            if (statusEnum != null) {
                queryDTO.setAgreementStatusStr(statusEnum.getName());
            }
            AppAgreementEnum agreementEnum = AppAgreementEnum.getByCode(dto.getAgreementType());
            if (agreementEnum != null) {
                queryDTO.setAgreementTypeStr(agreementEnum.getName());
            }
            return queryDTO;
        }).collect(Collectors.toList());
    }
}
