package com.dbj.classpal.admin.service.entity.file;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导入文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_file_import_excel")
@Tag(name= "SysFileImportExcel对象", description="导入文件记录")
public class SysFileImportExcel extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "所属模块名称")
    @TableField("module_name")
    private String moduleName;

    @Schema(description = "服务标识 system app books")
    @TableField("sign")
    private String sign;

    @Schema(description = "源文件名称")
    @TableField("file_name")
    private String fileName;

    @Schema(description = "源文件url")
    @TableField("file_url")
    private String fileUrl;

    @Schema(description = "新增条数")
    @TableField("add_num")
    private Integer addNum;

    @Schema(description = "更新条数")
    @TableField("upd_num")
    private Integer updNum;

    @Schema(description = "数据正确条数")
    @TableField("sub_num")
    private Integer subNum;

    @Schema(description = "错误条数")
    @TableField("error_num")
    private Integer errorNum;

    @Schema(description = "文件处理后的url")
    @TableField("processed_url")
    private String processedUrl;

    @Schema(description = "开始处理时间")
    @TableField("handle_start_time")
    private LocalDateTime handleStartTime;

    @Schema(description = "结束处理时间")
    @TableField("handle_end_time")
    private LocalDateTime handleEndTime;

    @Schema(description = "上传文件业务类型")
    @TableField("type")
    private String type;

    @Schema(description = "文件处理业务类型 1普通文件 2素材中心 3音频tts制作")
    @TableField("business_type")
    private Integer businessType;

    @Schema(description = "处理失败(还没有开始处理数据就失败的原因)")
    @TableField("error_msg")
    private String errorMsg;

    @Schema(description = "额外参数")
    @TableField("param_json")
    private String paramJson;

    @Schema(description = "阿里云分析模板任务jobId")
    @TableField("analysis_submit_job_id")
    private String analysisSubmitJobId;

    @Schema(description = "阿里云查询分析模板结果任务jobId")
    @TableField("analysis_query_job_id")
    private String analysisQueryJobId;

    @Schema(description = "阿里云转码jobId")
    @TableField("trans_submit_job_id")
    private String transSubmitJobId;

    @Schema(description = "阿里云查询转码任务jobId")
    @TableField("trans_query_job_id")
    private String transQueryJobId;

    @Schema(description = "是否已读 0-否 1-是")
    @TableField("is_read")
    private Integer isRead;

    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
    @TableField("status")
    private Integer status;


}
