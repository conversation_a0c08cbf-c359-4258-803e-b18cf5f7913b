package com.dbj.classpal.admin.service.remote.books.book;


import com.dbj.classpal.books.client.api.books.AdminBooksRankApi;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoUpdForceApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksTreeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 图书表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Component
public class BooksRankRemoteService {

    @Resource
    private AdminBooksRankApi adminBooksRankApi;
    public List<BooksRankInfoApiDTO> list(BooksRankInfoApiBO bookRankInfoApiBO) throws BusinessException{
        RestResponse<List<BooksRankInfoApiDTO>> result = adminBooksRankApi.list(bookRankInfoApiBO);
        return result.returnProcess(result);
    }

    public Boolean updateForcePromotionUrl(BooksRankInfoUpdForceApiBO booksRankInfoUpdForceApiBO) throws BusinessException{
        RestResponse<Boolean> result = adminBooksRankApi.updateForcePromotionUrl(booksRankInfoUpdForceApiBO);
        return result.returnProcess(result);
    }

    public BooksRankInfoDetailDTO detail(Integer id) throws BusinessException{
        RestResponse<BooksRankInfoDetailDTO> result = adminBooksRankApi.detail(id);
        return result.returnProcess(result);
    }

    public BooksTreeDTO tree(Integer booksId) throws BusinessException{
        RestResponse<BooksTreeDTO> result = adminBooksRankApi.tree(booksId);
        return result.returnProcess(result);
    }
}
