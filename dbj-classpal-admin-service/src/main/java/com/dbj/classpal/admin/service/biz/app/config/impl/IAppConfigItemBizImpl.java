package com.dbj.classpal.admin.service.biz.app.config.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.admin.client.bo.app.config.AppConfigSharePosterApiQueryBO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigSharePosterApiQueryDTO;
import com.dbj.classpal.admin.client.enums.app.config.AppConfigTypeEnum;
import com.dbj.classpal.admin.common.bo.app.config.AppConfigItemSharePosterQueryBO;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.admin.common.enums.StatusEnum;
import com.dbj.classpal.admin.service.biz.app.config.IAppConfigItemBiz;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigItem;
import com.dbj.classpal.admin.service.mapper.app.config.AppConfigItemMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Classname IAppConfigItemBusinessImpl
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:52:09
 * @Created by xuezhi
 */
@Service
public class IAppConfigItemBizImpl extends ServiceImpl<AppConfigItemMapper, AppConfigItem> implements IAppConfigItemBiz {
    @Resource
    private AppConfigItemMapper mapper;

    @Override
    public Page<AppConfigItemQueryDTO> pageSharePosterInfo(PageInfo<AppConfigItemSharePosterQueryBO> page) {
        AppConfigItemSharePosterQueryBO data = page.getData();
        data.setTypeCode(AppConfigTypeEnum.SHARE_POSTER.getCode());
        return mapper.pageSharePosterInfo(page.getPage(),data);
    }

    @Override
    public Page<AppConfigSharePosterApiQueryDTO> apiPageSharePosterInfo(PageInfo<AppConfigSharePosterApiQueryBO> page) {
        AppConfigSharePosterApiQueryBO data = page.getData();
        if (ObjectUtil.isNull(data)) {
            data = new AppConfigSharePosterApiQueryBO();
        }
        data.setTypeCode(AppConfigTypeEnum.SHARE_POSTER.getCode());
        data.setItemStatus(StatusEnum.AGREEMENT_STATUS_YES.getCode());
        return mapper.apiPageSharePosterInfo(page.getPage(),data);
    }
}
    