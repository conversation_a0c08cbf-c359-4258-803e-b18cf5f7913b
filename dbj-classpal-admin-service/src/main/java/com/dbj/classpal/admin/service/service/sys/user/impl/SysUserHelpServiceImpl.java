package com.dbj.classpal.admin.service.service.sys.user.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.common.bo.sys.user.AllocationDeptBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserHelpSaveBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.service.biz.sys.help.ISysHelpItemsBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserDeptBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserHelpBusiness;
import com.dbj.classpal.admin.service.entity.sys.help.SysHelpItems;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserHelp;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserDeptService;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserHelpService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class SysUserHelpServiceImpl implements ISysUserHelpService {


    @Resource
    private ISysUserHelpBusiness sysUserHelpBusiness;
    @Resource
    private ISysHelpItemsBusiness sysHelpItemsBusiness;


    @Override
    public Boolean saveSysUserHelp(SysUserHelpSaveBO sysUserHelpSaveBO) throws BusinessException {
        SysHelpItems sysHelpItems = sysHelpItemsBusiness.getById(sysUserHelpSaveBO.getHelpId());
        if(sysHelpItems == null){
            throw new BusinessException(AdminErrorCode.HELP_PAGE_NOT_EXIST_CODE,AdminErrorCode.HELP_PAGE_NOT_EXIST_MSG);
        }
        Integer userId = ContextUtil.getUserIdInt();
        SysUserHelp sysUserHelp = sysUserHelpBusiness.lambdaQuery().eq(SysUserHelp::getUserId, userId)
                .eq(SysUserHelp::getHelpId,sysUserHelpSaveBO.getHelpId()).one();
        if(sysUserHelp == null){
            sysUserHelp = BeanUtil.copyProperties(sysUserHelpSaveBO, SysUserHelp.class);
            sysUserHelp.setUserId(userId);
            sysUserHelpBusiness.save(sysUserHelp);
        }else{
            SysUserHelp userHelp = BeanUtil.copyProperties(sysUserHelpSaveBO, SysUserHelp.class);
            userHelp.setId(sysUserHelp.getId());
            sysUserHelpBusiness.updateById(userHelp);
        }
        return true;
    }
}
