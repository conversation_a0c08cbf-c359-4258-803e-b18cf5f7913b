package com.dbj.classpal.admin.service.service.sys.dict.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.dict.DictItemQueryBo;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictDetailBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictUpdBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictUpdStatusBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.constant.ConstantRedis;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictBusiness;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictItemBusiness;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.dbj.classpal.admin.service.service.sys.dict.ISysDictService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDictService
 * @description
 * @date 2025-03-14 15:58
 **/
@Service
@Slf4j
public class SysDictServiceImpl implements ISysDictService {

    @Resource
    private ISysDictBusiness sysDictBusiness;

    @Resource
    private ISysDictItemBusiness sysDictItemBusiness;
    @Autowired
    private RedissonRedisUtils redisUtils;

    @Override
    public SysDictDTO getSysDictInfo(SysDictDetailBO reqBo) throws BusinessException {
        SysDict sysDict = sysDictBusiness.getById(reqBo.getId());
        if(sysDict == null){
            throw new BusinessException(AdminErrorCode.DICT_NOT_EXIST_CODE,AdminErrorCode.DICT_NOT_EXIST_MSG);
        }
        return BeanUtil.copyProperties(sysDict, SysDictDTO.class);
    }

    @Override
    public Boolean saveSysDict(SysDictSaveBO bo) throws BusinessException {
        //判断是否有重复
        List<SysDict> sysDictList = sysDictBusiness.lambdaQuery().eq(SysDict::getDictCode,bo.getDictCode()).list();
        if(CollectionUtils.isNotEmpty(sysDictList)){
            throw new BusinessException(AdminErrorCode.DICT_CODE_REPEAT_CODE,AdminErrorCode.DICT_CODE_REPEAT_MSG);
        }
        sysDictList = sysDictBusiness.lambdaQuery().eq(SysDict::getDictName,bo.getDictName()).list();
        if(CollectionUtils.isNotEmpty(sysDictList)){
            throw new BusinessException(AdminErrorCode.DICT_NAME_EXIST_CODE,AdminErrorCode.DICT_NAME_EXIST_CODE);
        }
        sysDictBusiness.save(BeanUtil.copyProperties(bo, SysDict.class));
        return true;
    }

    @Override
    public Boolean updateSysDict(SysDictUpdBO bo) throws BusinessException {
        //判断数据是否存在，是否有重复
        SysDict sysDict = sysDictBusiness.getById(bo.getId());
        if(sysDict == null){
            throw new BusinessException(AdminErrorCode.DICT_NOT_EXIST_CODE,AdminErrorCode.DICT_NOT_EXIST_MSG);
        }
        //判断是否有重复
        if(!StringUtils.equals(sysDict.getDictCode(),bo.getDictCode())){
            List<SysDict> sysDictList = sysDictBusiness.lambdaQuery().eq(SysDict::getDictCode,bo.getDictCode()).list();
            if(CollectionUtils.isNotEmpty(sysDictList)){
                throw new BusinessException(AdminErrorCode.DICT_CODE_REPEAT_CODE,AdminErrorCode.DICT_CODE_REPEAT_MSG);
            }
        }
        //判断是否有重复
        if(!StringUtils.equals(sysDict.getDictName(),bo.getDictName())){
            List<SysDict> sysDictList = sysDictBusiness.lambdaQuery().eq(SysDict::getDictName,bo.getDictName()).list();
            if(CollectionUtils.isNotEmpty(sysDictList)){
                throw new BusinessException(AdminErrorCode.DICT_NAME_EXIST_CODE,AdminErrorCode.DICT_NAME_EXIST_CODE);
            }
        }
        sysDictBusiness.updateById(BeanUtil.copyProperties(bo, SysDict.class));
        return true;
    }

    @Override
    public Boolean batchUpdateStatus(SysDictUpdStatusBO bo) throws BusinessException {
        sysDictBusiness.lambdaUpdate().in(SysDict::getId,bo.getIds()).set(SysDict::getStatus,bo.getStatus()).update();
        return true;
    }

    @Override
    public Boolean batchDelSysDictInfo(BaseIdsBO baseIdsBO) throws BusinessException {
        //TODO 判断是否有子集 有子集不允许删除
        //判断数据是否存在，是否有重复
//        SysDict sysDict = sysDictBusiness.getById(reqBo.getId());
//        if(sysDict == null){
//            throw new BusinessException(AdminErrorCode.DICT_NOT_EXIST_CODE,AdminErrorCode.DICT_NOT_EXIST_MSG);
//        }
        sysDictBusiness.removeByIds(baseIdsBO.getIds());
        sysDictItemBusiness.lambdaUpdate().in(SysDictItem::getDictId,baseIdsBO.getIds()).remove();
        return null;
    }

    @Override
    public Page<SysDictDTO> pageSysDictInfo(PageInfo<SysDictBO> page) throws BusinessException {
        Page<SysDictDTO> sysDictPage = sysDictBusiness.pageSysDictInfo(page);
        return sysDictPage;
    }

    @Override
    public Map<String,List<SysDictItemDTO>> getSysDictInfoAll() throws BusinessException {
        List<SysDictDTO> sysDictDTOList = new ArrayList<>();
        if(redisUtils.hasKey(ConstantRedis.SYSDICT)){
            String sysDict = redisUtils.getValue(ConstantRedis.SYSDICT);
            sysDictDTOList =JSON.parseArray(sysDict,SysDictDTO.class);
        }else{
            List<SysDict> sysDictList = sysDictBusiness.lambdaQuery().eq(SysDict::getStatus, YesOrNoEnum.YES.getCode()).orderByDesc(SysDict::getSort).list();
            if(CollectionUtils.isNotEmpty(sysDictList)){
                sysDictDTOList = BeanUtil.copyToList(sysDictList, SysDictDTO.class);

                List<SysDictItem> sysDictItemList =  sysDictItemBusiness.lambdaQuery().eq(SysDictItem::getStatus, YesOrNoEnum.YES.getCode()).orderByDesc(SysDictItem::getSort).list();
                if(CollectionUtils.isNotEmpty(sysDictItemList)){
                    List<SysDictItemDTO> sysDictItemDTOList = BeanUtil.copyToList(sysDictItemList, SysDictItemDTO.class);
                    Map<Integer, List<SysDictItemDTO>> sysDictItemMap = sysDictItemDTOList.stream().collect(Collectors.groupingBy(SysDictItemDTO::getDictId));
                    for(SysDictDTO sysDictDTO :sysDictDTOList){
                        sysDictDTO.setSysDictItemDTOList(sysDictItemMap.getOrDefault(sysDictDTO.getId(), new ArrayList<>()));
                    }
                }

            }
            redisUtils.setValue(ConstantRedis.SYSDICT,JSON.toJSONString(sysDictDTOList),60*60*24, TimeUnit.SECONDS);
        }

        return sysDictDTOList.stream().collect(Collectors.toMap(SysDictDTO::getDictCode, SysDictDTO::getSysDictItemDTOList));
    }

    @Override
    public Boolean refreshCacheDict() throws BusinessException {
        redisUtils.delKey(ConstantRedis.SYSDICT);
        getSysDictInfoAll();
        return true;
    }

    @Override
    public SysDictItemDTO getAdjacentDictItem(DictItemQueryBo queryBo) {
        // 1. 从Redis获取字典数据
        SysDictDTO currentDict = getSysDictList(queryBo.getDictCode());
        if (currentDict == null || CollectionUtils.isEmpty(currentDict.getSysDictItemDTOList())) {
            log.warn("No dictionary data found");
            return null;
        }

        // 3. 在当前字典项中查找
        SysDictItemDTO currentItem = currentDict.getSysDictItemDTOList().stream()
                .filter(item -> item.getItemValue().equals(queryBo.getDictItemValue()))
                .findFirst()
                .orElse(null);

        if (currentItem == null) {
            log.warn("Current dict item not found, itemId: {}", queryBo.getDictItemValue());
            return null;
        }

        // 4. 在当前字典项组内查找下一项
        SysDictItemDTO nextItemInCurrentDict = currentDict.getSysDictItemDTOList().stream()
                .filter(item -> item.getSort() < currentItem.getSort())
                .max(Comparator.comparing(SysDictItemDTO::getSort))
                .orElse(null);

        // 5. 如果当前字典项组内没找到，查找下一个字典组 是药根据年纪来的
        if (nextItemInCurrentDict == null) {
            List<SysDictItem> sysDictItemList = sysDictItemBusiness.lambdaQuery()
                    .eq(SysDictItem::getStatus, YesOrNoEnum.YES.getCode())
                    .eq(SysDictItem::getItemValue, currentDict.getDictCode())
                    .orderByDesc(SysDictItem::getSort)
                    .list();
            if(CollectionUtils.isEmpty(sysDictItemList)){
                return null;
            }

            sysDictItemList = sysDictItemBusiness.lambdaQuery()
                    .eq(SysDictItem::getStatus, YesOrNoEnum.YES.getCode())
                    .eq(SysDictItem::getDictId, sysDictItemList.get(0).getDictId())
                    .orderByDesc(SysDictItem::getSort)
                    .list();
            //查找下一个阶段
            Optional<SysDictItem> optional = sysDictItemList.stream().filter(item  -> item.getItemValue().equals(currentDict.getDictCode())).findFirst();
            if(!optional.isPresent()){
                return null;
            }
            SysDictItem currentStage =  optional.get();
            SysDictItem sysDictItem =  sysDictItemList.stream()
                    .filter(item -> item.getSort() < currentStage.getSort())
                    .max(Comparator.comparing(SysDictItem::getSort))
                    .orElse(null);
            if(sysDictItem == null){
                return null;
            }
            SysDictDTO nextDict = getSysDictList(sysDictItem.getItemValue());
            if(nextDict == null || CollectionUtils.isEmpty(currentDict.getSysDictItemDTOList())){
                return null;
            }
            return nextDict.getSysDictItemDTOList().stream()
                    .max(Comparator.comparing(SysDictItemDTO::getSort))
                    .orElse(null);
        }

        return nextItemInCurrentDict;
    }

    @Override
    public SysDictItemApiDTO getDictItem(DictItemQueryBo queryBo) throws BusinessException {
        // 1. 从Redis获取字典数据
        SysDictDTO currentDict = getSysDictList(queryBo.getDictCode());
        if (currentDict == null || CollectionUtils.isEmpty(currentDict.getSysDictItemDTOList())) {
            log.warn("No dictionary data found");
            return null;
        }

        // 3. 在当前字典项中查找
        SysDictItemDTO currentItem = currentDict.getSysDictItemDTOList().stream()
                .filter(item -> item.getItemValue().equals(queryBo.getDictItemValue()))
                .findFirst()
                .orElse(null);

        if (currentItem == null) {
            log.warn("Current dict item not found, itemId: {}", queryBo.getDictItemValue());
            return null;
        }
        SysDictItemApiDTO sysDictItemApiDTO = new SysDictItemApiDTO();
        BeanUtil.copyProperties(currentItem, sysDictItemApiDTO);
        return sysDictItemApiDTO;
    }


    /**
     * 获取字典列表（先从缓存，没有则查库）
     */
    private SysDictDTO getSysDictList(String dictCode) {
        // 2. 缓存没有，从数据库查询
        List<SysDict> sysDictList = sysDictBusiness.lambdaQuery()
                .eq(SysDict::getStatus, YesOrNoEnum.YES.getCode())
                .eq(StringUtils.isNotEmpty(dictCode),SysDict::getDictCode, dictCode)
                .orderByDesc(SysDict::getSort)
                .list();

        if (CollectionUtils.isEmpty(sysDictList)) {
            return null;
        }

        // 3. 转换并关联字典项
        SysDictDTO sysDictDTO = BeanUtil.copyProperties(sysDictList.get(0), SysDictDTO.class);

        List<SysDictItem> sysDictItemList = sysDictItemBusiness.lambdaQuery()
                .eq(SysDictItem::getStatus, YesOrNoEnum.YES.getCode())
                .eq(SysDictItem::getDictId, sysDictDTO.getId())
                .orderByDesc(SysDictItem::getSort)
                .list();

        if (CollectionUtils.isNotEmpty(sysDictItemList)) {
            List<SysDictItemDTO> sysDictItemDTOList = BeanUtil.copyToList(sysDictItemList, SysDictItemDTO.class);
            sysDictDTO.setSysDictItemDTOList(sysDictItemDTOList);
        }


        return sysDictDTO;
    }

}


