package com.dbj.classpal.admin.service.biz.sys.user;

import com.dbj.classpal.admin.common.dto.sys.dept.SysUserDeptGroupDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysDeptUserNameDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDeptDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户与部门关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface ISysUserDeptBusiness extends IService<SysUserDept> {

    /**
     * 根据部门id统计部门信息
     *
     * @return SysUserDeptGroupDTO
     */
    List<SysUserDeptGroupDTO> getSysUserInfoByDeptIdGroup();



    
    /**
     * <AUTHOR>
     * @Description  根据用户id查询部门信息
     * @Date 2025/3/18 9:23 
     * @param 
     * @return 
     **/
    List<SysUserDeptDTO> getUserDeptList(List<Integer> userIds);

    /**
     * <AUTHOR>
     * @Description  根据用户id查询部门信息
     * @Date 2025/3/18 9:23
     * @param
     * @return
     **/
    List<SysDeptUserNameDTO> getUserNameDeptList();
}
