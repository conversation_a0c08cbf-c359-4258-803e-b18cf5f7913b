package com.dbj.classpal.admin.service.remote.appgray;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.api.gray.AppFeatureClientApi;
import com.dbj.classpal.app.client.bo.gray.FeatureEditApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureIdApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureIdsApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureQueryApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureSaveApiBO;
import com.dbj.classpal.app.client.dto.gray.FeatureDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:16:30
 */
@Component
public class AppFeatureRemoteService {
    @Resource
    private AppFeatureClientApi appFeatureClientApi;

    public Page<FeatureDetailApiDTO> pageFeatures(PageInfo<FeatureQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<FeatureDetailApiDTO>> result = appFeatureClientApi.pageFeatures(pageRequest);
        return result.returnProcess(result);
    }

    public void saveFeature(FeatureSaveApiBO feature) throws BusinessException {
        RestResponse<Void> result = appFeatureClientApi.saveFeature(feature);
        result.returnProcess(result);
    }

    public void editFeature(FeatureEditApiBO feature) throws BusinessException {
        RestResponse<Void> result = appFeatureClientApi.editFeature(feature);
        result.returnProcess(result);
    }


    public FeatureDetailApiDTO getFeatureDetail(FeatureIdApiBO featureId) throws BusinessException {
        RestResponse<FeatureDetailApiDTO>  result =appFeatureClientApi.getFeatureDetail(featureId);
        return result.returnProcess(result);
    }


    public void batchEnable(FeatureIdsApiBO featureIds) throws BusinessException {
        RestResponse<Void> result =appFeatureClientApi.batchEnable(featureIds);
        result.returnProcess(result);
    }

    public void batchDisable(FeatureIdsApiBO featureIds) throws BusinessException {
        RestResponse<Void> result = appFeatureClientApi.batchDisable(featureIds);
        result.returnProcess(result);
    }

    public void batchDelete(FeatureIdsApiBO featureIds) throws BusinessException {
        RestResponse<Void> result = appFeatureClientApi.batchDelete(featureIds);
        result.returnProcess(result);
    }
}
