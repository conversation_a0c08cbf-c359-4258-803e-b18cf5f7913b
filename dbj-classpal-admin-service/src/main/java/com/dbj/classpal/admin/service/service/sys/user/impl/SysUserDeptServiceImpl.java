package com.dbj.classpal.admin.service.service.sys.user.impl;

import com.dbj.classpal.admin.service.biz.sys.user.ISysUserDeptBusiness;
import com.dbj.classpal.admin.common.bo.sys.user.AllocationDeptBO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserDeptService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class SysUserDeptServiceImpl implements ISysUserDeptService {


    @Resource
    private ISysUserDeptBusiness sysUserDeptBusiness;



    @Override
    public Boolean allocationDept(AllocationDeptBO allocationDeptBO) {

        sysUserDeptBusiness.lambdaUpdate().in(SysUserDept::getUserId,allocationDeptBO.getUserIds()).remove();
        List<SysUserDept> sysUserDeptList =  new ArrayList<>();
        for (Integer userId : allocationDeptBO.getUserIds()){
            for (Integer deptId : allocationDeptBO.getDeptIds()){
                SysUserDept sysUserDept = new SysUserDept();
                sysUserDept.setUserId(userId);
                sysUserDept.setDeptId(deptId);
                sysUserDeptList.add(sysUserDept);
            }
        }
        sysUserDeptBusiness.saveBatch(sysUserDeptList);
        return true;
    }
}
