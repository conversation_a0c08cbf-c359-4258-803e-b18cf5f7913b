package com.dbj.classpal.admin.service.biz.sys.dict;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDictItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <p>
 * 数据字典项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface ISysDictItemBusiness extends IService<SysDictItem> {



    Page<SysDictItemDTO> pageSysDictItemInfo(PageInfo<SysDictItemBO> page);
}
