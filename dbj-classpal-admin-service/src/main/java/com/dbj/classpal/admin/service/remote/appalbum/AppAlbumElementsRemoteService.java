package com.dbj.classpal.admin.service.remote.appalbum;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.album.AppAlbumElementsApi;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumRemoteService
 * Date:     2025-04-15 14:27:26
 * Description: 表名： ,描述： 表
 */
@Component
public class AppAlbumElementsRemoteService {

    @Resource
    private AppAlbumElementsApi api;

    public List<AppAlbumElementsQueryApiDTO> getAppAlbumElementsList(AppAlbumElementsQueryApiBO bo) throws BusinessException {
        RestResponse<List<AppAlbumElementsQueryApiDTO>> result = api.getAppAlbumElementsList(bo);
        return result.returnProcess(result);
    }

    public Page<AppAlbumElementsQueryApiDTO>pageAlbumElements(PageInfo<AppAlbumElementsQueryApiBO> bo) throws BusinessException {
        RestResponse<Page<AppAlbumElementsQueryApiDTO>> result = api.pageAlbumElements(bo);
        return result.returnProcess(result);
    }

    public AppAlbumElementsQueryApiDTO getAppAlbumElement(CommonIdApiBO bo) throws BusinessException {
        RestResponse<AppAlbumElementsQueryApiDTO> result = api.getAppAlbumElement(bo);
        return result.returnProcess(result);
    }

    public Boolean saveAlbumElements(AppAlbumElementsSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.saveAlbumElements(bo);
        return result.returnProcess(result);
    }

    public Boolean updateAlbumElements(AppAlbumElementsUpdateApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.updateAlbumElements(bo);
        return result.returnProcess(result);
    }

    public Boolean deleteAppAlbumElements(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.deleteAppAlbumElements(bo);
        return result.returnProcess(result);
    }

    public Boolean updateAppAlbumElementCover(AppAlbumElementsUpdateCoverApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.updateAppAlbumElementCover(bo);
        return result.returnProcess(result);
    }

    public Boolean updateAppAlbumElementTitle(AppAlbumElementsUpdateTitleApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.updateAppAlbumElementTitle(bo);
        return result.returnProcess(result);
    }

    public Boolean updateAppAlbumElementRemark(AppAlbumElementsUpdateRemarkApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.updateAppAlbumElementRemark(bo);
        return result.returnProcess(result);
    }

    public Boolean updateAppAlbumElementVisible(AppAlbumElementsUpdateVisibleApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.updateAppAlbumElementVisible(bo);
        return result.returnProcess(result);
    }

    public Boolean updateAppAlbumElementStatus(AppAlbumElementsUpdateStatusApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = api.updateAppAlbumElementStatus(bo);
        return result.returnProcess(result);
    }
}
