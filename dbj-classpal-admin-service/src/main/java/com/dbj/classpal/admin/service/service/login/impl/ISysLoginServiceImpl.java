package com.dbj.classpal.admin.service.service.login.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dbj.classpal.admin.common.dto.login.ButtonAuth;
import com.dbj.classpal.admin.service.biz.sys.menu.ISysMenuBusiness;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleBusiness;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleMenuBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserDeptBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserRoleBusiness;
import com.dbj.classpal.admin.common.bo.login.LoginUserBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.dto.login.GtestCodeDTO;
import com.dbj.classpal.admin.common.dto.login.SysLoginUserDTO;
import com.dbj.classpal.admin.common.dto.sys.menu.SysMenuDTO;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import com.dbj.classpal.admin.service.entity.sys.role.SysRoleMenu;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.dbj.classpal.admin.common.enums.MenuTypeEnum;
import com.dbj.classpal.admin.service.service.login.ISysLoginService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.constant.Constants;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.jwt.utils.JwtToken;
import com.dbj.classpal.framework.jwt.utils.entity.JwtAuthVO;
import com.dbj.classpal.framework.jwt.utils.entity.JwtVO;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.dbj.classpal.framework.utils.util.PwdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-printer-system-bus
 * @className SysLoginService
 * @description
 * @date 2024-02-18 15:06
 **/
@Slf4j
@Service
public class ISysLoginServiceImpl implements ISysLoginService {

    @Resource
    private RedissonRedisUtils redissonClient;

    @Resource
    private ISysUserBusiness sysUserBusiness;
    @Resource
    private ISysUserDeptBusiness sysUserDeptBusiness;
    @Resource
    private ISysUserRoleBusiness sysUserRoleBusiness;
    @Resource
    private ISysRoleBusiness sysRoleBusiness;
    @Resource
    private ISysMenuBusiness sysMenuBusiness;
    @Resource
    private ISysRoleMenuBusiness sysRoleMenuBusiness;
    @Value("${gtest.appId:a73283df8b07bca6909aff0e3fa265ff}")
    private String gTestAppId;
    @Value("${gtest.secret:2f55d11898284477402706cca6ea5747}")
    private String gTestSecretKey;

    @Override
    public GtestCodeDTO preLogin() {
        String l = System.currentTimeMillis() + "";
        String s = RandomUtil.randomInt(1000, 9999) + "";
        //时间戳加随机数作为key 进行
        redissonClient.setValue(l + s, s, 60, TimeUnit.SECONDS);
        GtestCodeDTO dto = new GtestCodeDTO();
        dto.setCode(s);
        dto.setTimestamp(l);
        dto.setAppId(gTestAppId);
        dto.setAppSecret(gTestSecretKey);
        return dto;
    }

    @Override
    public SysLoginUserDTO accountsLogin(LoginUserBO loginUserBO) throws BusinessException {
        //校验验证码是否存在redis
        String code = loginUserBO.getCode();
        String timestamp = loginUserBO.getTimestamp();
        String accounts = loginUserBO.getAccounts();
        String yzm = redissonClient.getValue(timestamp + code);
        if (StringUtils.isEmpty(yzm)) {
            throw new BusinessException(AdminErrorCode.GTEST_ERROR_CODE, AdminErrorCode.GTEST_ERROR_MSG);
        }
        SysUser sysUser = sysUserBusiness.lambdaQuery().eq(SysUser::getAccounts, accounts).or().eq(SysUser::getPhone, accounts).eq(SysUser::getAccountsStatus, YesOrNoEnum.YES.getCode()).one();

        SysLoginUserDTO sysUserDTO = new SysLoginUserDTO();
        if (sysUser == null) {
            throw new BusinessException(AdminErrorCode.NO_USER_CODE, AdminErrorCode.NO_USER_MSG);
        } else {
            if(!Objects.equals(YesOrNoEnum.YES.getCode(),sysUser.getAccountsStatus())){
                throw new BusinessException(AdminErrorCode.NO_USER_CODE, AdminErrorCode.NO_USER_MSG);
            }
            String loginPassWord  = PwdUtils.genPwd(sysUser.getSalt(), loginUserBO.getPassword());
            if (!StringUtils.equals(loginPassWord, sysUser.getPassword())) {
                throw new BusinessException(AdminErrorCode.PW_ERROR_CODE, AdminErrorCode.PW_ERROR_MSG);
            } else {

                getUser(sysUserDTO,sysUser,loginUserBO.getNoLogin());
            }
        }

        return sysUserDTO;
    }

    @Override
    public SysLoginUserDTO getLoginUserMenu() throws BusinessException {

        SysUser sysUser =   sysUserBusiness.getSysUserById(ContextUtil.getUserIdInt());
        SysLoginUserDTO sysUserDTO = new SysLoginUserDTO();
        if (sysUser == null) {
            throw new BusinessException(AdminErrorCode.NO_USER_CODE, AdminErrorCode.NO_USER_MSG);
        } else {
            if(!Objects.equals(YesOrNoEnum.YES.getCode(),sysUser.getAccountsStatus())){
                throw new BusinessException(AdminErrorCode.NO_USER_CODE, AdminErrorCode.NO_USER_MSG);
            }
            getUser(sysUserDTO,sysUser,ContextUtil.getNoLogin());
        }
        return sysUserDTO;
    }


    public void getUser(SysLoginUserDTO sysUserDTO, SysUser user, Boolean noLogin) throws BusinessException {
        BeanUtils.copyProperties(user, sysUserDTO);
        JwtVO jwtVO = new JwtVO();
        JwtAuthVO jwtAuthVO = new JwtAuthVO();
        jwtAuthVO.setNoLogin(noLogin);
        jwtVO.setUserId(user.getId());
        jwtVO.setUserName(user.getNickName());
        jwtVO.setPhone(user.getPhone());
        jwtVO.setTenantId(ContextUtil.getTenantId());
        // 超管账号
        boolean isAdmin = StrUtil.equals(Constants.ADMINISTRATORS, user.getAccounts());
        if (isAdmin) {
            jwtAuthVO.setIsAdministrators(YesOrNoEnum.YES.getCode());
            sysUserDTO.setIsAdministrators(YesOrNoEnum.YES.getCode());
        }

        // 查询用户部门id
        List<SysUserDept> sysUserDeptDomainList = sysUserDeptBusiness.lambdaQuery().eq(SysUserDept::getUserId, user.getId()).list();
        if(CollectionUtils.isNotEmpty(sysUserDeptDomainList)){
            jwtAuthVO.setDeptId(sysUserDeptDomainList.get(0).getDeptId());
        }

        List<SysMenuDTO> appMenus = new ArrayList<>();
        if(isAdmin){
            appMenus = BeanUtil.copyToList(sysMenuBusiness.list(),SysMenuDTO.class);
        }else {
            // 查询公共角色
            List<SysUserRole> sysUserRoleList =  sysUserRoleBusiness.lambdaQuery().eq(SysUserRole::getUserId, user.getId()).list();
            SysRole commonRole = sysRoleBusiness.lambdaQuery().eq(SysRole::getRoleCode, Constants.COMMON).one();
            if (CollectionUtils.isEmpty(sysUserRoleList) && commonRole == null) {
                throw new BusinessException(AdminErrorCode.NOT_LOGIN_CODE, AdminErrorCode.NOT_LOGIN_MSG);
            }
            List<Integer> roles = sysUserRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
            if(commonRole != null){
                roles.add(commonRole.getId());
            }
            //查询用户角色信息
            List<SysRole> sysRoleDomainList = sysRoleBusiness.lambdaQuery().in(SysRole::getId, roles).eq(SysRole::getRoleStatus, YesOrNoEnum.YES.getCode()).list();
            List<SysRole> adminDates = sysRoleDomainList.stream()
                    .filter(a -> StringUtils.equals(a.getRoleCode(), Constants.ADMINISTRATORS))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(adminDates)){
                appMenus = BeanUtil.copyToList(sysMenuBusiness.list(),SysMenuDTO.class);
            }else{
                //通过角色查询菜单即可
                List<Integer> appRoleIds = sysRoleDomainList.stream().map(SysRole::getId).collect(Collectors.toList());
                List<SysRoleMenu> SysRoleMenuList =  sysRoleMenuBusiness.lambdaQuery().in(SysRoleMenu::getRoleId,appRoleIds).list();
                if(CollectionUtils.isNotEmpty(SysRoleMenuList)){
                    List<Integer> menuIds = SysRoleMenuList.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
                    appMenus = BeanUtil.copyToList(sysMenuBusiness.listByIds(menuIds),SysMenuDTO.class);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(appMenus)) {
            List<SysMenuDTO> buttonMenus = appMenus.stream()
                    .filter(a -> Objects.equals(MenuTypeEnum.BUTTON.getCode(), a.getMenuType()) && StringUtils.isNotEmpty(a.getPerms())).collect(Collectors.toList());
            List<ButtonAuth> buttonAuthCodes = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(buttonMenus)){
                buttonAuthCodes = BeanUtil.copyToList(buttonMenus,ButtonAuth.class);
            }
            List<SysMenuDTO> menuList = appMenus.stream()
                    .filter(a -> !Objects.equals(MenuTypeEnum.BUTTON.getCode(), a.getMenuType()))
                    .collect(Collectors.toList());

            List<SysMenuDTO> rootMenus = menuList.stream()
                    .filter(a -> Objects.equals(0, a.getParentId()))
                    .collect(Collectors.toList());

            for (SysMenuDTO rootMenu : rootMenus) {
                getMenuTree(menuList, rootMenu);
            }

            sysUserDTO.setButtonAuthCodes(buttonAuthCodes);
            sysUserDTO.setMenuDTOS(rootMenus);
        }

        String token = JwtToken.createToken(jwtVO);
        if (noLogin) {
            redissonClient.setValue(Constants.USER_ADMIN_TOKEN + user.getId(), token, 7, TimeUnit.DAYS);
            redissonClient.setValue(MessageFormat.format(Constants.USER_ADMIN_TOKEN_AUTH_ID, user.getId()), JSONUtil.toJsonStr(jwtAuthVO), 7, TimeUnit.DAYS);
        } else {
            redissonClient.setValue(Constants.USER_ADMIN_TOKEN + user.getId(), token, 1, TimeUnit.DAYS);
            redissonClient.setValue(MessageFormat.format(Constants.USER_ADMIN_TOKEN_AUTH_ID, user.getId()), JSONUtil.toJsonStr(jwtAuthVO), 1, TimeUnit.DAYS);
        }
        sysUserDTO.setToken(token);
        //修改最后登陆时间
        SysUser sysUserDomain =  new SysUser();
        sysUserDomain.setId(user.getId());
        sysUserDomain.setLastLoginTime(LocalDateTime.now());
        sysUserBusiness.updateById(sysUserDomain);
    }





    /**
     * 递归获取菜单树
     *
     * @param allMenus
     * @param currentMenu
     */
    public void getMenuTree(List<SysMenuDTO> allMenus, SysMenuDTO currentMenu) {
        if (currentMenu != null) {
            List<SysMenuDTO> children = getChildren(allMenus, currentMenu.getId());
            currentMenu.setChildren(children);

            for (SysMenuDTO child : children) {
                getMenuTree(allMenus, child);
            }
        }
    }

    /**
     * 递归获取菜单树
     * @param parentId 父节点id
     * @param allMenus 菜单树
     * @return
     */
    private List<SysMenuDTO> getChildren(List<SysMenuDTO> allMenus, Integer parentId) {
        List<SysMenuDTO> children = new ArrayList<>();
        for (SysMenuDTO menu : allMenus) {
            if (Objects.equals(menu.getParentId(), parentId)) {
                children.add(menu);
            }
        }
        return children;
    }
}
