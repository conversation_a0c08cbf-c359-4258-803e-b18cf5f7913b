package com.dbj.classpal.admin.service.biz.app.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.admin.client.bo.app.config.AppConfigSharePosterApiQueryBO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigSharePosterApiQueryDTO;
import com.dbj.classpal.admin.common.bo.app.config.AppConfigItemSharePosterQueryBO;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.admin.service.entity.app.config.AppConfigItem;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * @Classname IAppConfigItemBusiness
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:51:24
 * @Created by xuezhi
 */
public interface IAppConfigItemBiz extends IService<AppConfigItem> {

    /**
     * 分页查询海报列表
     * @param page
     * @return
     */
    Page<AppConfigItemQueryDTO> pageSharePosterInfo(PageInfo<AppConfigItemSharePosterQueryBO> page);

    /**
     * 分页查询海报列表-Api端
     * @param page
     * @return
     */
    Page<AppConfigSharePosterApiQueryDTO> apiPageSharePosterInfo(PageInfo<AppConfigSharePosterApiQueryBO> page);
}
