package com.dbj.classpal.admin.service.biz.sys.dict.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.service.entity.sys.dict.SysDict;
import com.dbj.classpal.admin.service.mapper.sys.dict.SysDictMapper;
import com.dbj.classpal.admin.service.biz.sys.dict.ISysDictBusiness;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据字典主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
public class SysDictBusinessImpl extends ServiceImpl<SysDictMapper, SysDict> implements ISysDictBusiness {

    @Resource
    private SysDictMapper sysDictMapper;

    @Override
    public Page<SysDictDTO> pageSysDictInfo(PageInfo<SysDictBO> page) throws BusinessException {
        return sysDictMapper.pageSysDictInfo(page.getPage(),page.getData());
    }

    @Override
    public List<SysDictDTO> listSysDictInfo(SysDictBO sysDictBO) {
        return sysDictMapper.listSysDictInfo(sysDictBO);
    }
}
