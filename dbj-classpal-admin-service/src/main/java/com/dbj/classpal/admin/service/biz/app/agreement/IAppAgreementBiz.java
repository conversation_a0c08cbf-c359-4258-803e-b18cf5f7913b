package com.dbj.classpal.admin.service.biz.app.agreement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.admin.client.bo.app.agreement.AppAgreementApiQueryBO;
import com.dbj.classpal.admin.client.dto.app.agreement.AppAgreementApiQueryDTO;
import com.dbj.classpal.admin.service.entity.app.agreement.AppAgreement;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

public interface IAppAgreementBiz extends IService<AppAgreement> {
    /**
     * 获取所有协议列表
     * @return
     */
    List<AppAgreementApiQueryDTO> getAllAppAgreement();

    /**
     * 根据id查询单个协议内容
     * @param id
     * @return
     */
    AppAgreementApiQueryDTO getAppAgreementById(Integer id) throws BusinessException;

    /**
     * 根据协议类型查询单个协议内容
     * @param bo
     * @return
     */
    AppAgreementApiQueryDTO getAppAgreementByTypeId(AppAgreementApiQueryBO bo);

}
