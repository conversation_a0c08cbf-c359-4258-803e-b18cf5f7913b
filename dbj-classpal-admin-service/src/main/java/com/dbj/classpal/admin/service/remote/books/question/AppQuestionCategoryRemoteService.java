package com.dbj.classpal.admin.service.remote.books.question;

import com.dbj.classpal.books.client.api.question.QuestionCategoryApi;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryRefApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/03/24 15:23:01
 */
@Component
public class AppQuestionCategoryRemoteService {
    @Resource
    private QuestionCategoryApi questionCategoryApi;

    /**
     * 获取分类列表
     */
   public List<QuestionCategoryApiDTO> getCategoryList(QuestionCategoryIdApiBO apiBO) throws BusinessException {
        RestResponse<List<QuestionCategoryApiDTO>> result = questionCategoryApi.getCategoryList(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 获取分类列表
     */
    public List<QuestionCategoryRefApiDTO> getBusinessRefs(QuestionCategoryIdQueryApiBO apiBO) throws BusinessException {
        RestResponse<List<QuestionCategoryRefApiDTO>> result = questionCategoryApi.getBusinessRefs(apiBO);
        return result.returnProcess(result);
    }


    /**
     * 创建分类
     */
    public Integer createCategory(QuestionCategoryApiBO apiBO) throws BusinessException {
        RestResponse<Integer> result = questionCategoryApi.createCategory(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 更新分类
     */
    public Void updateCategory(QuestionCategoryApiBO apiBO) throws BusinessException{
        RestResponse<Void> result = questionCategoryApi.updateCategory(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 删除分类
     */
    public Void batchDeleteCategory(@RequestBody QuestionCategoryIdsApiBO apiBO) throws BusinessException{
        RestResponse<Void> result = questionCategoryApi.batchDeleteCategory(apiBO);
        return result.returnProcess(result);
    }

    /**
     * 更新分类排序
     */
   public Void updateSort(@RequestBody QuestionCategorySortApiBO apiBO) throws BusinessException{
        RestResponse<Void> result = questionCategoryApi.updateSort(apiBO);
        return result.returnProcess(result);
    }

}