package com.dbj.classpal.admin.service.job;

import com.dbj.classpal.admin.client.enums.sys.file.FileBusinessTypeEnum;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobBO;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobUpdateBO;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.dbj.classpal.books.client.enums.material.MaterialTransCodeEnum;
import com.dbj.classpal.books.client.mq.entity.AppMaterialMqEntity;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.dbj.classpal.admin.common.constant.ExchangeConstant.CLASSPAL_BOOKS_EXCHANGE;
import static com.dbj.classpal.admin.common.constant.RoutingKeyConstant.AUDIO_SYNC_MATERIAL_ROUTING_KEY;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialConversionJob
 * Date:     2025-04-09 11:41:40
 * Description: 表名： ,描述： 表
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExcelFileImportJob {

    @Resource
    private ISysFileImportExcelBusiness business;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;

    /**
     * 查询是否导入超过两个小时的数据，并将数据状态改为异常
     * @return
     */
    @XxlJob("excelFileImportJob")
    @Transactional(rollbackFor = Exception.class)
    public void excelFileImportJob() throws BusinessException, ExecutionException, InterruptedException {
            log.info("---------------job定时补偿修改导入超时2小时的文件--------------");
        AppMaterialJobBO jobBO = new AppMaterialJobBO();
        jobBO.setStatus(FileStatusEnum.PROCESSED.getCode());
        jobBO.setHours(2);
        List<SysFileImportExcel> updateList = business.getFailImportExcel(jobBO);
        //List<SysFileImportExcel> ttsList = updateList.stream().filter(sysFileImportExcel -> sysFileImportExcel.getBusinessType().equals(FileBusinessTypeEnum.BUSINESS_TTS.getCode())).toList();
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<Integer> idList = updateList.stream().map(SysFileImportExcel::getId).collect(Collectors.toList());
            AppMaterialJobUpdateBO updateBO = new AppMaterialJobUpdateBO();
            updateBO.setIds(idList);
            updateBO.setMsg("导入超时");
            updateBO.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
            business.updateFailImportExcel(updateBO);
        }
    }
}
