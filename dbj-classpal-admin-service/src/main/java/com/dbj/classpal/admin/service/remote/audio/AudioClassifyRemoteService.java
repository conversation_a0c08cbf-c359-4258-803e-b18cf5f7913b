package com.dbj.classpal.admin.service.remote.audio;

import com.dbj.classpal.books.client.api.audio.AudioClassifyApi;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyMoveBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyQueryBO;
import com.dbj.classpal.books.client.dto.audio.AudioClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/27 17:12
 */
@Component
@RequiredArgsConstructor
public class AudioClassifyRemoteService {

    private final AudioClassifyApi audioClassifyApi;

    @Transactional(rollbackFor = Exception.class)
    public Boolean save(AudioClassifyAddBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioClassifyApi.save(bo);
        return response.returnProcess(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(AudioClassifyDelBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioClassifyApi.remove(bo);
        return response.returnProcess(response);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean move(AudioClassifyMoveBO bo) throws BusinessException {
        RestResponse<Boolean> response = audioClassifyApi.move(bo);
        return response.returnProcess(response);
    }

    public List<AudioClassifyDTO> list(AudioClassifyQueryBO bo) throws BusinessException {
        RestResponse<List<AudioClassifyDTO>> response = audioClassifyApi.list(bo);
        return response.returnProcess(response);
    }
}
