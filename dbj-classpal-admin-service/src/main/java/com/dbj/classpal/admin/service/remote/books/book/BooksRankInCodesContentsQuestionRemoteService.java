package com.dbj.classpal.admin.service.remote.books.book;

import com.dbj.classpal.books.client.api.books.AdminBooksRankInCodeContentsQuestionApi;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionDetailBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionSavaBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionUpdateBO;
import com.dbj.classpal.books.client.dto.books.BooksRankClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className BooksRankInCodesContentsQuestionRemoteService
 * @description
 * @date 2025-04-18 15:44
 **/
@Component
public class BooksRankInCodesContentsQuestionRemoteService {


    @Resource
    private AdminBooksRankInCodeContentsQuestionApi adminBooksRankInCodeContentsQuestionApi;


    public BooksRankInCodesContentsQuestionDetailBO details(Integer inCodesContentsId) throws BusinessException {
        RestResponse<BooksRankInCodesContentsQuestionDetailBO> result = adminBooksRankInCodeContentsQuestionApi.details(inCodesContentsId);
        return result.returnProcess(result);
    }

    public Boolean save(BooksRankInCodesContentsQuestionSavaBO saveBO) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsQuestionApi.save(saveBO);
        return result.returnProcess(result);
    }

    public Boolean update(@RequestBody BooksRankInCodesContentsQuestionUpdateBO saveBO) throws BusinessException{
        RestResponse<Boolean>  result = adminBooksRankInCodeContentsQuestionApi.update(saveBO);
        return result.returnProcess(result);
    }
}
