package com.dbj.classpal.admin.service.biz.sys.user.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserBusiness;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserBO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.dbj.classpal.admin.service.mapper.sys.user.SysUserMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className SysUserBiz
 * @description
 * @date 2025-03-10 08:56
 **/
@Service
public class SysUserBusinessImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserBusiness {

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public SysUser getSysUserById(Integer id) {
        return this.getById(id);
    }

    @Override
    public Page<SysUserDTO> pageSysUser(PageInfo<SysUserBO> page) {
        Page<SysUserDTO> page1 =sysUserMapper.pageSysUser(page.getPage(), page.getData());
        return page1;
    }
}
