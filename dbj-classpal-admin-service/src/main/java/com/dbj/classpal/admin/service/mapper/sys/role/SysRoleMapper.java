package com.dbj.classpal.admin.service.mapper.sys.role;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.role.SysRolePageBO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 角色信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {



    Page<SysRolePageDTO> pageSysRole(Page page,@Param("bo") SysRolePageBO bo);
}
