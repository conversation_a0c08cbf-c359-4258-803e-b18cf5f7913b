package com.dbj.classpal.admin.service.remote.audio;

import cn.hutool.core.collection.CollectionUtil;
import com.dbj.classpal.books.client.api.audio.AudioContextInfoApi;
import com.dbj.classpal.books.client.bo.audio.AudioContextInfoBO;
import com.dbj.classpal.books.client.bo.audio.AudioIntroIdBO;
import com.dbj.classpal.books.client.bo.audio.AudioTaskBO;
import com.dbj.classpal.books.client.bo.audio.AudioTrialUseBO;
import com.dbj.classpal.books.client.dto.audio.AudioContextInfoListDTO;
import com.dbj.classpal.books.client.dto.audio.AudioSynthesizerTaskInfoDTO;
import com.dbj.classpal.books.client.dto.audio.AudioTrialUseDTO;
import com.dbj.classpal.books.client.dto.audio.SynthesisResultDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 音频文本语音合成
 * <AUTHOR>
 */
@Component
public class AudioContextInfoRemoteService {

    @Autowired
    private AudioContextInfoApi audioContextInfoApi;

    public Integer saveContext(AudioContextInfoBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        if (CollectionUtil.isNotEmpty(bo.getGlobalConfigList()) && bo.getGlobalConfigList().size() > 2) {
            throw new BusinessException("全局配置项重复");
        }
        RestResponse<Integer> result = audioContextInfoApi.save(bo);
        return result.returnProcess(result);
    }

    public AudioContextInfoListDTO getContextInfoList(AudioIntroIdBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<AudioContextInfoListDTO> result = audioContextInfoApi.getContextInfoList(bo);
        return result.returnProcess(result);
    }

    public Integer cancel(AudioIntroIdBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Integer> result = audioContextInfoApi.cancel(bo);
        return result.returnProcess(result);
    }

    public Integer synthesis(AudioContextInfoBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        if (bo.getAppMaterialId() == null) {
            throw new BusinessException("素材库id不能为空");
        }
        RestResponse<Integer> result = audioContextInfoApi.synthesis(bo);
        return result.returnProcess(result);

    }

    public Integer resynthesis(AudioIntroIdBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<Integer> result = audioContextInfoApi.resynthesis(bo);
        return result.returnProcess(result);
    }

    public SynthesisResultDTO getSynthesizeStatus(AudioIntroIdBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<SynthesisResultDTO> result = audioContextInfoApi.getSynthesizeStatus(bo);
        return result.returnProcess(result);
    }

    public AudioSynthesizerTaskInfoDTO submitSynthesis(AudioTrialUseBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<AudioSynthesizerTaskInfoDTO> result = audioContextInfoApi.submitSynthesis(bo);
        return result.returnProcess(result);
    }

    public AudioSynthesizerTaskInfoDTO getTaskInfo(AudioTaskBO bo) throws BusinessException {
        if (bo == null) {
            throw new BusinessException("参数不能为空");
        }
        RestResponse<AudioSynthesizerTaskInfoDTO> result = audioContextInfoApi.getTaskInfo(bo);
        return result.returnProcess(result);
    }
}
