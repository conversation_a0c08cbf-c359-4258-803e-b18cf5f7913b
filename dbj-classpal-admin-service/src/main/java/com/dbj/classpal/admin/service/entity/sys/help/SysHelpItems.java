package com.dbj.classpal.admin.service.entity.sys.help;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_help_items")
@Tag(name ="SysHelpItems对象", description="角色信息表")
public class SysHelpItems extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "页面id")
    @TableField("menu_page_id")
    private Integer menuPageId;

    @Schema(description = "角色描述")
    @TableField("content_detail")
    private String contentDetail;


    @Schema(description = "状态 0 禁用 1启用")
    @TableField("status")
    private Integer status;



}
