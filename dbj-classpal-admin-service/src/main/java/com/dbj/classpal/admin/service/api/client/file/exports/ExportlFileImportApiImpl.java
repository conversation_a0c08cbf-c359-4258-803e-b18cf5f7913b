package com.dbj.classpal.admin.service.api.client.file.exports;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.client.api.file.exports.ExportlFileImportApi;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.service.biz.file.ISysFileExportExcelBusiness;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileImportApi
 * Date:     2025-04-08 11:04:00
 * Description: 表名： ,描述： 表
 */
@RestController
public class ExportlFileImportApiImpl implements ExportlFileImportApi {

    @Resource
    private ISysFileExportExcelBusiness sysFileExportBusiness;
    @Override
    public RestResponse<SysFileExportExcelBO> getById(Integer id) throws BusinessException {
        SysFileExportExcel fileDomain = sysFileExportBusiness.getById(id);
        if(fileDomain == null){
            throw new BusinessException(AdminErrorCode.EXPORT_EXCEL_FILE_NOT_EXIST_CODE,AdminErrorCode.EXPORT_EXCEL_FILE_NOT_EXIST_MSG);
        }
        return RestResponse.success(BeanUtil.copyProperties(fileDomain, SysFileExportExcelBO.class));
    }

    @Override
    public RestResponse<Boolean> handleProcessingFailedSys(SysFileExportExcelBO fileDomain) {
        sysFileExportBusiness.lambdaUpdate().eq(SysFileExportExcel::getId, fileDomain.getId())
                .set(SysFileExportExcel::getErrorMsg, fileDomain.getErrorMsg())
                .set(SysFileExportExcel::getHandleEndTime, new Date())
                .set(SysFileExportExcel::getStatus, FileStatusEnum.PROCESSING_FAILED_SYS.getCode()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateFileProcessed(SysFileExportExcelBO fileDomain) {
        sysFileExportBusiness.lambdaUpdate().eq(SysFileExportExcel::getId, fileDomain.getId())
                .set(SysFileExportExcel::getFileUrl, fileDomain.getFileUrl())
                .set(SysFileExportExcel::getStatus, FileStatusEnum.PROCESSED.getCode())
                .set(SysFileExportExcel::getHandleEndTime, new Date())
                .update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateFileProcessing(SysFileExportExcelBO fileDomain) {
        sysFileExportBusiness.lambdaUpdate().eq(SysFileExportExcel::getId, fileDomain.getId())
                .set(SysFileExportExcel::getStatus, FileStatusEnum.PROCESSING.getCode())
                .set(SysFileExportExcel::getHandleStartTime, new Date())
                .update();
        return RestResponse.success(true);
    }
}
