package com.dbj.classpal.admin.service.remote.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pointreading.PointReadingCategoryApi;
import com.dbj.classpal.books.client.bo.pointreading.*;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingCategoryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 点读分类相关远程服务
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Component
public class PointReadingCategoryRemoteService {

    @Resource
    private PointReadingCategoryApi pointReadingCategoryApi;

    /**
     * 分页查询点读书分类列表
     */
    public Page<PointReadingCategoryApiDTO> pageInfo(PageInfo<PointReadingCategoryQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<PointReadingCategoryApiDTO>> result = pointReadingCategoryApi.page(pageRequest);
        return result.returnProcess(result);
    }

    /**
     * 查询分类树形结构
     */
    public List<PointReadingCategoryApiDTO> tree(PointReadingCategoryQueryApiBO queryBO) throws BusinessException {
        RestResponse<List<PointReadingCategoryApiDTO>> result = pointReadingCategoryApi.tree(queryBO);
        return result.returnProcess(result);
    }

    /**
     * 查询分类详情
     */
    public PointReadingCategoryApiDTO detail(Integer id) throws BusinessException {
        PointReadingCategoryIdApiBO idBO = new PointReadingCategoryIdApiBO();
        idBO.setId(id);
        RestResponse<PointReadingCategoryApiDTO> result = pointReadingCategoryApi.detail(idBO);
        return result.returnProcess(result);
    }

    /**
     * 新增分类
     */
    public Integer save(PointReadingCategorySaveApiBO saveBO) throws BusinessException {
        RestResponse<Integer> result = pointReadingCategoryApi.save(saveBO);
        return result.returnProcess(result);
    }

    /**
     * 更新分类
     */
    public Boolean update(PointReadingCategoryUpdateApiBO updateBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingCategoryApi.update(updateBO);
        return result.returnProcess(result);
    }

    /**
     * 删除分类
     */
    public Boolean delete(Integer id) throws BusinessException {
        PointReadingCategoryIdApiBO idBO = new PointReadingCategoryIdApiBO();
        idBO.setId(id);
        RestResponse<Boolean> result = pointReadingCategoryApi.delete(idBO);
        return result.returnProcess(result);
    }

    /**
     * 批量删除分类
     */
    public Boolean deleteBatch(PointReadingCategoryBatchApiBO batchBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingCategoryApi.deleteBatch(batchBO);
        return result.returnProcess(result);
    }

    /**
     * 批量启用分类
     */
    public Boolean enableBatch(PointReadingCategoryBatchApiBO batchBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingCategoryApi.enableBatch(batchBO);
        return result.returnProcess(result);
    }

    /**
     * 批量禁用分类
     */
    public Boolean disableBatch(PointReadingCategoryBatchApiBO batchBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingCategoryApi.disableBatch(batchBO);
        return result.returnProcess(result);
    }
}
