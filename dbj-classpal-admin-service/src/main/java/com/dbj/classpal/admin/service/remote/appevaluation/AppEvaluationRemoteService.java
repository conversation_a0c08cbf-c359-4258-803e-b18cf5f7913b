package com.dbj.classpal.admin.service.remote.appevaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.evaluation.AdminEvaluationApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.*;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationDetailQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationRemoteService
 * Date:     2025-05-16 16:11:44
 * Description: 表名： ,描述： 表
 */
@Component
public class AppEvaluationRemoteService {
    @Resource
    private AdminEvaluationApi evaluationApi;

    /**
     * 分页查询评测表
     * @param pageRequest
     * @return
     * @throws BusinessException
     */
    public Page<AdminEvaluationQueryApiDTO>pageInfo(PageInfo<AdminEvaluationQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<AdminEvaluationQueryApiDTO>> result = evaluationApi.pageInfo(pageRequest);
        return result.returnProcess(result);
    }

    /**
     * 查询评测表列表
     * @param
     * @return
     * @throws BusinessException
     */
    public List<AdminEvaluationQueryApiDTO> list() throws BusinessException {
        RestResponse<List<AdminEvaluationQueryApiDTO>> result = evaluationApi.list();
        return result.returnProcess(result);
    }

    /**
     * 新增评测表
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean saveAppEvaluation(AdminEvaluationSaveApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.saveAppEvaluation(bo);
        return result.returnProcess(result);
    }

    /**
     * 删除评测表
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean deleteAppEvaluation(CommonIdsApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.deleteAppEvaluation(bo);
        return result.returnProcess(result);
    }


    /**
     * 查看评测表详情
     * @param bo
     * @return
     * @throws BusinessException
     */
    public AdminEvaluationDetailQueryApiDTO getDetail(CommonIdApiBO bo) throws BusinessException {
        RestResponse<AdminEvaluationDetailQueryApiDTO> result = evaluationApi.getDetail(bo);
        return result.returnProcess(result);
    }

    /**
     * 重命名评测表
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean reName(AdminEvaluationReNameApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.reName(bo);
        return result.returnProcess(result);
    }

    /**
     * 修改评测表封面
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean reCover(AdminEvaluationReCoverApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.reCover(bo);
        return result.returnProcess(result);
    }

    /**
     * 修改评测表简介
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean reRemark(AdminEvaluationReRemarkApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.reRemark(bo);
        return result.returnProcess(result);
    }

    /**
     * 修改评测表上架状态
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean updateStatus(AdminEvaluationUpdateStatusApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.updateStatus(bo);
        return result.returnProcess(result);
    }

    /**
     * 修改评测表隐藏状态
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean updateVisible(AdminEvaluationUpdateVisibleApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.updateVisible(bo);
        return result.returnProcess(result);
    }

    /**
     * 修改评测表启用状态
     * @param bo
     * @return
     * @throws BusinessException
     */
    public Boolean updateOpen(AdminEvaluationUpdateOpenApiBO bo) throws BusinessException {
        RestResponse<Boolean> result = evaluationApi.updateOpen(bo);
        return result.returnProcess(result);
    }
}
