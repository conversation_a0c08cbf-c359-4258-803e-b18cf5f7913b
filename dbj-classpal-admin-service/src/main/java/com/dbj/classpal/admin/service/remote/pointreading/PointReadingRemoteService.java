package com.dbj.classpal.admin.service.remote.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pointreading.PointReadingBookApi;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookUpdateApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingBookApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 点读相关远程服务
 *
 * <AUTHOR>
 * @since 2025-01-XX
 */
@Component
public class PointReadingRemoteService {

    @Resource
    private PointReadingBookApi pointReadingBookApi;

    public Page<PointReadingBookApiDTO> pageInfo(PageInfo<PointReadingBookQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<PointReadingBookApiDTO>> result = pointReadingBookApi.page(pageRequest);
        return result.returnProcess(result);
    }

    public Integer save(PointReadingBookSaveApiBO saveBO) throws BusinessException {
        RestResponse<Integer> result = pointReadingBookApi.save(saveBO);
        return result.returnProcess(result);
    }

    public Boolean update(PointReadingBookUpdateApiBO updateBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingBookApi.update(updateBO);
        return result.returnProcess(result);
    }

    public PointReadingBookApiDTO detail(Integer id) throws BusinessException {
        PointReadingBookIdApiBO idBO = new PointReadingBookIdApiBO();
        idBO.setId(id);
        RestResponse<PointReadingBookApiDTO> result = pointReadingBookApi.detail(idBO);
        return result.returnProcess(result);
    }

    public Boolean delete(Integer id) throws BusinessException {
        PointReadingBookIdApiBO idBO = new PointReadingBookIdApiBO();
        idBO.setId(id);
        RestResponse<Boolean> result = pointReadingBookApi.delete(idBO);
        return result.returnProcess(result);
    }
}