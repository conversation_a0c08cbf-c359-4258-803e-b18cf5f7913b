package com.dbj.classpal.admin.service.remote.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pointreading.PointReadingApi;
import com.dbj.classpal.books.client.api.pointreading.PointReadingBookApi;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingUpdateApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingApiDTO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingBookApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 点读相关远程服务
 *
 * <AUTHOR>
 * @since 2025-01-XX
 */
@Component
public class PointReadingRemoteService {

    @Resource
    private PointReadingBookApi pointReadingApi;

    public Page<PointReadingBookApiDTO> pageInfo(PageInfo<PointReadingQueryApiBO> pageRequest) throws BusinessException {
        RestResponse<Page<PointReadingApiDTO>> result = pointReadingApi.pageInfo(pageRequest);
        return result.returnProcess(result);
    }

    public Boolean save(PointReadingSaveApiBO saveBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingApi.save(saveBO);
        return result.returnProcess(result);
    }

    public Boolean update(PointReadingUpdateApiBO updateBO) throws BusinessException {
        RestResponse<Boolean> result = pointReadingApi.update(updateBO);
        return result.returnProcess(result);
    }

    public PointReadingApiDTO detail(Integer id) throws BusinessException {
        RestResponse<PointReadingApiDTO> result = pointReadingApi.detail(id);
        return result.returnProcess(result);
    }

    public Boolean delete(Integer id) throws BusinessException {
        RestResponse<Boolean> result = pointReadingApi.delete(id);
        return result.returnProcess(result);
    }

    public List<PointReadingApiDTO> list(PointReadingQueryApiBO queryBO) throws BusinessException {
        RestResponse<List<PointReadingApiDTO>> result = pointReadingApi.list(queryBO);
        return result.returnProcess(result);
    }
}