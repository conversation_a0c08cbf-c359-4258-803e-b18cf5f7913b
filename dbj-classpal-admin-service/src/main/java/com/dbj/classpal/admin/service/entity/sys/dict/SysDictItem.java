package com.dbj.classpal.admin.service.entity.sys.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数据字典项表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_dict_item")
@Tag(name="SysDictItem对象", description="数据字典项表")
public class SysDictItem extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "所属字典ID")
    @TableField("dict_id")
    private Integer dictId;

    @Schema(description = "字典项名称")
    @TableField("item_name")
    private String itemName;

    @Schema(description = "字典项值（支持JSON格式扩展）")
    @TableField("item_value")
    private String itemValue;

    @Schema(description = "启用状态")
    @TableField("status")
    private Integer status;

    @Schema(description = "排序权重")
    @TableField("sort")
    private Integer sort;

    @Schema(description = "备注说明")
    @TableField("remark")
    private String remark;

    @Schema(description = "版本号")
    @TableField("version")
    private Integer version;


}
