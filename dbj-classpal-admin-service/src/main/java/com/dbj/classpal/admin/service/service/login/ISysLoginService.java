package com.dbj.classpal.admin.service.service.login;

import com.dbj.classpal.admin.common.bo.login.LoginUserBO;
import com.dbj.classpal.admin.common.dto.login.GtestCodeDTO;
import com.dbj.classpal.admin.common.dto.login.SysLoginUserDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-printer-system-bus
 * @className SysLoginService
 * @description
 * @date 2024-02-18 15:06
 **/
public interface ISysLoginService {


    /**
     * 预登录
     * @return
     */
    GtestCodeDTO preLogin();

    SysLoginUserDTO accountsLogin(LoginUserBO loginUserBO) throws BusinessException;


    SysLoginUserDTO getLoginUserMenu() throws BusinessException;
}
