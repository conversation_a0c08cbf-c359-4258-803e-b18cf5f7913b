package com.dbj.classpal.admin.service.mq.listener.file.imports.handle;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictImportBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuExportBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuImportBO;
import com.dbj.classpal.admin.service.biz.file.ISysFileImportExcelBusiness;
import com.dbj.classpal.admin.service.biz.sys.menu.ISysMenuBusiness;
import com.dbj.classpal.admin.service.entity.sys.menu.SysMenu;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.dto.ReadFileDTO;
import com.dbj.classpal.framework.utils.file.ExcelFileStrategy;
import com.dbj.classpal.framework.utils.util.ImportExcelUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19 13:56:37
 */
@Service("sysMenuExcelFileStrategy")
@Slf4j
public class SysMenuExcelFileStrategy extends AdminExcelFileStrategy<SysMenuImportBO> {
    @Resource
    ISysMenuBusiness sysMenuBusiness;
    private final static Integer SIZE = 20000;
    static Map<String,Integer> menuTypeMap = new HashMap<>();
    static Map<String,Integer> visibleMap = new HashMap<>();
    static Map<String,String> targetMap = new HashMap<>();
    static Map<String,Integer> isRefreshMap = new HashMap<>();
    static {
        menuTypeMap.put("菜单",1);
        menuTypeMap.put("页面",2);
        menuTypeMap.put("按钮",3);
        visibleMap.put("显示",0);
        visibleMap.put("隐藏",1);
        isRefreshMap.put("是",0);
        isRefreshMap.put("否",1);
        targetMap.put("页签","menuItem");
        targetMap.put("新窗口","menuBlank");
    }

    @Override
    public List<SysMenuImportBO> convert(File file) {
        try {
            return  ImportExcelUtil.readFile(file, SysMenuImportBO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void business(List<SysMenuImportBO> importBOList, SysFileImportExcelBO fileDomain, String tmpFileName) throws Exception {
        //保存结果
        List<SysMenu> sysMenuExportBOList = BeanUtil.copyToList(importBOList,SysMenu.class);
        //需要保存对应的父id
        Map<String,Integer> parentIdMap = new HashMap<>();
        for(SysMenu sysMenu : sysMenuExportBOList){
            SysMenu menu = sysMenuBusiness.lambdaQuery().eq(SysMenu::getIdentifier,sysMenu.getIdentifier()).one();
            if(menu == null){
                sysMenuBusiness.save(sysMenu);
            }else{
                sysMenu.setId(menu.getId());
                sysMenuBusiness.updateById(sysMenu);
            }
            parentIdMap.put(sysMenu.getIdentifier(),sysMenu.getId());
        }
        for(SysMenuImportBO sysMenuImportBO : importBOList){
            String parentIdentifier = sysMenuImportBO.getParentIdentifier();
            if(StringUtils.isNotEmpty(parentIdentifier)){
                Integer parentId = parentIdMap.get(parentIdentifier);
                if(parentId != null){
                    SysMenu menu = sysMenuBusiness.lambdaQuery().eq(SysMenu::getIdentifier,sysMenuImportBO.getIdentifier()).one();
                    if(menu != null){
                        sysMenuBusiness.lambdaUpdate().eq(SysMenu::getId,menu.getId()).set(SysMenu::getParentId,parentId).update();
                    }

                }
            }
        }
    }

    @Override
    public boolean dataCheck(List<SysMenuImportBO> importBOList, SysFileImportExcelBO fileDomain, String errFileName) {
        //判断上级是否存在  是否与表中重名 是否与数据库重名
        List<String> parentIdentifierMap= importBOList.stream().filter(SysMenuImportBO-> !StringUtils.isEmpty(SysMenuImportBO.getParentIdentifier())).map(SysMenuImportBO::getParentIdentifier).collect(Collectors.toList());
        int errNum = 0;
        for(SysMenuImportBO sysMenuImportBO : importBOList){
            boolean flag = false;
            //判断上级是否存在
            String parentIdentifier = sysMenuImportBO.getParentIdentifier();
            if(StringUtils.isNotEmpty(parentIdentifier) && !parentIdentifierMap.contains(parentIdentifier)){
                SysMenu menu = sysMenuBusiness.lambdaQuery().eq(SysMenu::getIdentifier,sysMenuImportBO.getIdentifier()).one();
                if(menu == null){
                    sysMenuImportBO.setRowTips("上级不存在");
                    flag = true;
                }
                if(flag){
                    errNum++;
                }
            }
        }

        if(errNum > 0){
            handleProcessingFailedBusiness(importBOList,fileDomain,errFileName,errNum);
            return false;
        }
        return true;
    }
}
