package com.dbj.classpal.admin.service.service.sys.user.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserFirstUpdPasswordBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdNameBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdPasswordBO;
import com.dbj.classpal.admin.service.biz.sys.role.ISysRoleBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserDeptBusiness;
import com.dbj.classpal.admin.service.biz.sys.user.ISysUserRoleBusiness;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserSaveBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdAccountsStatusBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdBO;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.constant.Constant;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDeptDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDetailDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserRoleDTO;
import com.dbj.classpal.admin.service.entity.sys.role.SysRole;
import com.dbj.classpal.admin.service.entity.sys.user.SysUser;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserDept;
import com.dbj.classpal.admin.service.entity.sys.user.SysUserRole;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.constant.Constants;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.dbj.classpal.framework.utils.util.PwdUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class SysUserServiceImpl implements ISysUserService {

    @Resource
    private ISysUserBusiness sysUserBusiness;
    @Resource
    private ISysUserRoleBusiness sysUserRoleBusiness;
    @Resource
    private ISysRoleBusiness sysRoleBusiness;
    @Resource
    private ISysUserDeptBusiness sysUserDeptBusiness;

    @Resource
    private RedissonRedisUtils redissonClient;

    @Override
    public SysUserDetailDTO getSysUserById(Integer id) throws BusinessException {
        SysUser sysUser = sysUserBusiness.getById(id);
        if(sysUser == null){
            throw new BusinessException(AdminErrorCode.USER_NOT_EXIST_CODE,AdminErrorCode.USER_NOT_EXIST_MSG);
        }
        SysUserDetailDTO sysUserDetailDTO = BeanUtil.copyProperties(sysUser, SysUserDetailDTO.class);
        List<SysUserRole> sysUserRoleList =  sysUserRoleBusiness.lambdaQuery().eq(SysUserRole::getUserId,id).list();
        if(CollectionUtils.isNotEmpty(sysUserRoleList)){
            List<Integer> roleIds = sysUserRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
            List<SysRole> sysRoleList =  sysRoleBusiness.lambdaQuery().eq(SysRole::getRoleStatus,YesOrNoEnum.YES.getCode()).in(SysRole::getId,roleIds).list();
            if(CollectionUtils.isNotEmpty(sysRoleList)){
                roleIds =sysRoleList.stream().map(SysRole::getId).collect(Collectors.toList());
                sysUserDetailDTO.setRoleIds(roleIds);
            }
        }
        List<SysUserDept> sysUserDeptList = sysUserDeptBusiness.lambdaQuery().eq(SysUserDept::getUserId,id).list();
        if(CollectionUtils.isNotEmpty(sysUserDeptList)){
            List<Integer> deptIds = sysUserDeptList.stream().map(SysUserDept::getDeptId).collect(Collectors.toList());
            sysUserDetailDTO.setDeptIds(deptIds);
        }
        return sysUserDetailDTO;
    }

    @Override
    public Page<SysUserDTO> pageSysUser(PageInfo<SysUserBO> page) {
        Page<SysUserDTO> sysUserDTOPage =sysUserBusiness.pageSysUser(page);
        List<SysUserDTO> sysUserDTOList = sysUserDTOPage.getRecords();
        if(CollectionUtils.isNotEmpty(sysUserDTOList)){
            List<Integer> userIds =  sysUserDTOList.stream().map(SysUserDTO::getId).collect(Collectors.toList());
            List<SysUserRoleDTO> sysUserRoleDTOList = sysUserRoleBusiness.getRoleList(userIds);
            Map<Integer,List<String>> roleNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(sysUserRoleDTOList)){
                roleNameMap = sysUserRoleDTOList.stream().collect(Collectors.groupingBy(SysUserRoleDTO::getUserId,Collectors.mapping(SysUserRoleDTO::getRoleName,Collectors.toList())));
            }
            List<SysUserDeptDTO> sysUserDeptDTOList = sysUserDeptBusiness.getUserDeptList(userIds);
            Map<Integer,List<String>> deptNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(sysUserDeptDTOList)){
                deptNameMap = sysUserDeptDTOList.stream().collect(Collectors.groupingBy(SysUserDeptDTO::getUserId,Collectors.mapping(SysUserDeptDTO::getDeptName,Collectors.toList())));
            }
            for(SysUserDTO sysUserDTO :sysUserDTOList){
                sysUserDTO.setRoleList(roleNameMap.get(sysUserDTO.getId()));
                sysUserDTO.setDeptList(deptNameMap.get(sysUserDTO.getId()));
            }
        }
        return sysUserDTOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSysUser(SysUserSaveBO sysUserSaveBO) throws BusinessException {
        SysUser sysUser = new SysUser();
        BeanUtil.copyProperties(sysUserSaveBO,sysUser);

        List<SysUser> sysUsers =  sysUserBusiness.lambdaQuery().eq(SysUser::getAccounts,sysUser.getAccounts()).list();
        if(CollectionUtils.isNotEmpty(sysUsers) ){
            throw new BusinessException(AdminErrorCode.USER_ACCOUNTS_REPEAT_CODE,AdminErrorCode.USER_ACCOUNTS_REPEAT_MSG);
        }


        //生成密码
        String salt = System.currentTimeMillis() + "";
        String pwd = PwdUtils.genPwd(salt, Constant.DEFAULT_PWD);
        sysUser.setSalt(salt);
        sysUser.setPassword(pwd);
        sysUserBusiness.save(sysUser);

        save(BeanUtil.copyProperties(sysUserSaveBO,SysUserUpdBO.class),sysUser);
        return true;
    }

    @Override
    public Boolean updateSysUser(SysUserUpdBO sysUserUpdateBO) throws BusinessException {

        SysUser sysUser = sysUserBusiness.getById(sysUserUpdateBO.getId());
        if(sysUser == null){
            throw new BusinessException(AdminErrorCode.USER_NOT_EXIST_CODE,AdminErrorCode.USER_NOT_EXIST_MSG);
        }
        sysUser = new SysUser();
        BeanUtil.copyProperties(sysUserUpdateBO,sysUser);
        //删除角色和部门
        sysUserRoleBusiness.lambdaUpdate().eq(SysUserRole::getUserId,sysUser.getId()).remove();
        sysUserDeptBusiness.lambdaUpdate().eq(SysUserDept::getUserId,sysUser.getId()).remove();
        //重新保存
        sysUserBusiness.updateById(sysUser);
        save(sysUserUpdateBO,sysUser);
        return true;
    }

    @Override
    public Boolean updateSysUserName(SysUserUpdNameBO sysUserUpdateBO) throws BusinessException {
        SysUser sysUser = sysUserBusiness.getById(sysUserUpdateBO.getId());
        if(sysUser == null){
            throw new BusinessException(AdminErrorCode.USER_NOT_EXIST_CODE,AdminErrorCode.USER_NOT_EXIST_MSG);
        }
        sysUser = new SysUser();
        BeanUtil.copyProperties(sysUserUpdateBO,sysUser);
        sysUserBusiness.updateById(sysUser);
        return true;
    }

    @Override
    public Boolean updateAccountsStatus(SysUserUpdAccountsStatusBO sysUser) {
        sysUserBusiness.lambdaUpdate().in(SysUser::getId,sysUser.getIds()).set(SysUser::getAccountsStatus,sysUser.getAccountsStatus()).update();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(BaseIdsBO baseIds) {
        List<Integer> ids = baseIds.getIds();
        String salt = System.currentTimeMillis() + "";
        String pwd = PwdUtils.genPwd(salt, Constant.DEFAULT_PWD);
        sysUserBusiness.lambdaUpdate().in(SysUser::getId,ids)
                .set(SysUser::getSalt,salt)
                .set(SysUser::getPassword,pwd)
                .set(SysUser::getIsForce,YesOrNoEnum.YES.getCode()).update();
        removeToken(ids);
        return true;
    }

    @Override
    public Boolean updatePassword(SysUserUpdPasswordBO sysUserUpdPasswordBO) throws BusinessException {

        if(!StringUtils.equals(sysUserUpdPasswordBO.getNewPassword(),sysUserUpdPasswordBO.getValidatePassword())){
            throw new BusinessException(AdminErrorCode.PASSWORD_NOT_SAME_CODE,AdminErrorCode.PASSWORD_NOT_SAME_MSG);
        }
        SysUser sysUser = sysUserBusiness.getById(ContextUtil.getUserIdInt());
        if(sysUser == null){
            throw new BusinessException(AdminErrorCode.USER_NOT_EXIST_CODE,AdminErrorCode.USER_NOT_EXIST_MSG);
        }
        if(!StringUtils.equals(PwdUtils.genPwd(sysUser.getSalt(),sysUserUpdPasswordBO.getOldPassword()),sysUser.getPassword())){
            throw new BusinessException(AdminErrorCode.PW_ERROR_CODE,AdminErrorCode.PW_ERROR_MSG);
        }
        //生成密码
        String salt = System.currentTimeMillis() + "";
        String pwd = PwdUtils.genPwd(salt, sysUserUpdPasswordBO.getNewPassword());

        sysUserBusiness.lambdaUpdate().eq(SysUser::getId,sysUser.getId())
                .set(SysUser::getSalt,salt)
                .set(SysUser::getPassword,pwd)
                .set(SysUser::getIsForce,YesOrNoEnum.NO.getCode())
                .update();
        removeToken(Arrays.asList(sysUser.getId()));
        return true;
    }

    @Override
    public Boolean firstUpdatePassword(SysUserFirstUpdPasswordBO sysUserFirstUpdPasswordBO) throws BusinessException {
        if(!StringUtils.equals(sysUserFirstUpdPasswordBO.getNewPassword(),sysUserFirstUpdPasswordBO.getValidatePassword())){
            throw new BusinessException(AdminErrorCode.PASSWORD_NOT_SAME_CODE,AdminErrorCode.PASSWORD_NOT_SAME_MSG);
        }
        SysUser sysUser = sysUserBusiness.getById(ContextUtil.getUserIdInt());
        if(sysUser == null){
            throw new BusinessException(AdminErrorCode.USER_NOT_EXIST_CODE,AdminErrorCode.USER_NOT_EXIST_MSG);
        }
        //生成密码
        String salt = System.currentTimeMillis() + "";
        String pwd = PwdUtils.genPwd(salt, sysUserFirstUpdPasswordBO.getNewPassword());

        sysUserBusiness.lambdaUpdate().eq(SysUser::getId,sysUser.getId())
                .set(SysUser::getSalt,salt)
                .set(SysUser::getPassword,pwd)
                .set(SysUser::getIsForce,YesOrNoEnum.NO.getCode())
                .update();
        removeToken(Arrays.asList(sysUser.getId()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delSysUser(BaseIdsBO baseIds) {
        List<Integer> ids = baseIds.getIds();
        sysUserBusiness.lambdaUpdate().in(SysUser::getId,ids).remove();
        sysUserRoleBusiness.lambdaUpdate().in(SysUserRole::getUserId,ids).remove();
        sysUserDeptBusiness.lambdaUpdate().in(SysUserDept::getUserId,ids).remove();
        removeToken(ids);
        return true;
    }


    public void removeToken(List<Integer> ids){
        for(Integer id : ids){
            redissonClient.delKey(Constants.USER_ADMIN_TOKEN + id);
            redissonClient.delKey(MessageFormat.format(Constants.USER_ADMIN_TOKEN_AUTH_ID, id));
        }
    }

    public void save(SysUserUpdBO sysUserUpdateBO,SysUser sysUser){
        List<Integer> roleIds = sysUserUpdateBO.getRoleIds();
        if(CollectionUtils.isNotEmpty(roleIds)){
            List<SysUserRole> sysUserRoleList = new ArrayList<>();
            for(Integer roleId : roleIds){
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleList.add(sysUserRole);
            }
            sysUserRoleBusiness.saveBatch(sysUserRoleList);
        }
        List<Integer> deptIds = sysUserUpdateBO.getDeptIds();
        List<SysUserDept> sysUserDeptList = new ArrayList<>();
        for(Integer deptId : deptIds){
            SysUserDept sysUserDept = new SysUserDept();
            sysUserDept.setUserId(sysUser.getId());
            sysUserDept.setDeptId(deptId);
            sysUserDeptList.add(sysUserDept);
        }
        sysUserDeptBusiness.saveBatch(sysUserDeptList);
    }
}
