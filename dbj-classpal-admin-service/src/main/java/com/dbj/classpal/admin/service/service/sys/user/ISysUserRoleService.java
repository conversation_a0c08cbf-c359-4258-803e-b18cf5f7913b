package com.dbj.classpal.admin.service.service.sys.user;

import com.dbj.classpal.admin.common.bo.sys.user.AllocationRoleBO;
import com.dbj.classpal.admin.common.bo.sys.user.AllocationUserBO;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public interface ISysUserRoleService {



    Boolean allocationUser(AllocationUserBO allocationUser);

    Boolean allocationRole(AllocationRoleBO allocationRoleBO);

    List<Integer> getUserByRoleId(Integer roleId);
}
