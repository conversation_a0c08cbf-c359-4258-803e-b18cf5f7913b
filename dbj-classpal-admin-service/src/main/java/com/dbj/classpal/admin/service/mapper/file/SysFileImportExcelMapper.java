package com.dbj.classpal.admin.service.mapper.file;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobBO;
import com.dbj.classpal.admin.common.bo.books.material.AppMaterialJobUpdateBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelQueryBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelTypeBO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileImportExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 导入文件记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface SysFileImportExcelMapper extends BaseMapper<SysFileImportExcel> {




    /**
     * <AUTHOR>
     * @Description  分页查询文件导入数据
     * @Date 2025/3/20 11:56
     * @param
     * @return
     **/
    Page<SysFileImportExcelDTO> pageSysFileImportExcel(Page page, @Param("bo") SysFileImportExcelBO bo);


    List<SysFileImportExcelCountDTO> sysFileImportExcelCount(@Param("userId")Integer userId);


    /**
     * top3
     * @param bo
     * @return
     */
    List<SysFileImportExcelDTO> getFileInfoTop3(SysFileImportExcelTypeBO bo);


    /**
     * 判断是否存在文件正在处理的md5文件
     * @param bo
     * @return
     */
    List<SysFileImportExcelDTO>checkProcessMd5File(@Param("bo")SysFileImportExcelQueryBO bo);

    /**
     * 查询处理超时2小时的所有文件
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SysFileImportExcel> getFailImportExcel(@Param("bo")AppMaterialJobBO appMaterialJobBO);


    /**
     * 修改处理超时2小时的所有文件状态为处理失败
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    Boolean updateFailImportExcel(@Param("bo") AppMaterialJobUpdateBO bo);


    /**
     * 根据分析模板任务id查询导入信息
     * @param bo
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ExcelFileImportQueryApiDTO> getByAnalysisJobId(@Param("bo")ExcelFileImportQueryApiBO bo);


    /**
     * 根据转码任务id查询导入信息
     * @param bo
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ExcelFileImportQueryApiDTO> getByTransCodeJobId(@Param("bo")ExcelFileImportQueryApiBO bo);
}
