package com.dbj.classpal.admin.service.biz.file;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelBO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO;
import com.dbj.classpal.admin.service.entity.file.SysFileExportExcel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 导出文件记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISysFileExportExcelBusiness extends IService<SysFileExportExcel> {


    /**
     * 分页查询数据
     * @param page
     * @return
     */
    Page<SysFileExportExcelDTO> pageSysFileExportExcel(PageInfo<SysFileExportExcelBO> page);

    List<SysFileExportExcelCountDTO> sysFileExportExcelCount();

}
