<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.role.SysRoleMapper">

    <select id="pageSysRole" resultType="com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO">
            select * from sys_role
            <where>
                is_deleted = 0
                <if test="bo.name != null and bo.name != ''">
                    and name like concat('%', #{bo.name}, '%')
                </if>
                <if test="bo.describes != null and bo.describes != ''">
                    and describes like concat('%', #{bo.describes}, '%')
                </if>
                <if test="bo.roleStatusList != null and bo.roleStatusList.size() > 0">
                    and role_status in
                    <foreach collection="bo.roleStatusList" item="roleStatus" open="(" separator="," close=")">
                        #{roleStatus}
                    </foreach>
                </if>
            </where>
            order by role_sort desc
    </select>
</mapper>
