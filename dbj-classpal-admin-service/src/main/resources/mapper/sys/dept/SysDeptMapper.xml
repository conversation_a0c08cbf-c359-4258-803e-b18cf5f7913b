<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.dept.SysDeptMapper">


    <select id="getSysDeptInfos" resultType="java.lang.Integer">
        SELECT id
        FROM (
                 SELECT id,father_id
                 FROM sys_dept WHERE is_deleted = 0
                 ORDER BY father_id, id
             ) sys_dept,
             (SELECT @id := #{deptId}) initialisation
        WHERE FIND_IN_SET(father_id, @id) > 0
          AND @id := CONCAT(@id, ',', id)
            AND is_deleted = 0

    </select>
</mapper>
