<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.dict.SysDictMapper">


    <select id="pageSysDictInfo" resultType="com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO">
        select * from sys_dict
        <where>
            is_deleted = 0
            <if test="bo.dictName != null and bo.dictName != ''">
                and dict_name like concat('%',#{bo.dictName},'%')
            </if>
            <if test="bo.dictCode != null and bo.dictCode != ''">
                and dict_code like concat('%',#{bo.dictCode},'%')
            </if>
            <if test="bo.status != null and bo.status.size() > 0">
                and status in
                 <foreach collection="bo.status" item="item" separator="," open="(" close=")" >
                     #{item}
                 </foreach>
            </if>
            <if test="bo.description != null and bo.description != ''">
                and description like concat('%',#{bo.description},'%')
            </if>
        </where>
    </select>


    <select id="listSysDictInfo" resultType="com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO">
        select * from sys_dict
        <where>
            is_deleted = 0
            <if test="bo.dictName != null and bo.dictName != ''">
                and dict_name like concat('%',#{bo.dictName},'%')
            </if>
            <if test="bo.dictCode != null and bo.dictCode != ''">
                and dict_code like concat('%',#{bo.dictCode},'%')
            </if>
            <if test="bo.status != null and bo.status.size() > 0">
                and status in
                <foreach collection="bo.status" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>

            </if>
            <if test="bo.ids != null and bo.ids.size() > 0">
                and id in
                <foreach collection="bo.ids" item="item" separator="," open="(" close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
