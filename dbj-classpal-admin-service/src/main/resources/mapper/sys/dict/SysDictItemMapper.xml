<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.dict.SysDictItemMapper">

    <select id="pageSysDictItemInfo" resultType="com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO">
        SELECT
            t.id,
            t.dict_id,
            t.item_name,
            t.item_value,
            t.status,
            t.sort,
            t.remark,
            t.create_time,
            t.create_by,
            t.update_time,
            t.update_by
        FROM
            sys_dict_item t
        WHERE
            t.is_deleted = 0
            AND t.dict_id = #{bo.dictId}
        <if test="bo.itemName != null and bo.itemName != ''">
            AND t.item_name LIKE CONCAT('%', #{bo.itemName}, '%')
        </if>
        <if test="bo.itemValue != null and bo.itemValue != ''">
            AND t.item_value LIKE CONCAT('%', #{bo.itemValue}, '%')
        </if>
        <if test="bo.status != null">
            AND t.status = #{bo.status}
        </if>
        <if test="bo.remark != null and bo.remark != ''">
            AND t.remark LIKE CONCAT('%', #{bo.remark}, '%')
        </if>
        order by t.sort desc
    </select>
</mapper>
