<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.help.SysHelpItemsMapper">

    <select id="pageSysHelpItems" resultType="com.dbj.classpal.admin.common.dto.help.SysHelpItemsDTO">
        select * from sys_help_items a
        <where>
            is_deleted = 0
            <if test="bo.menuPageId != null">
                and a.menu_page_id = #{bo.menuPageId}
            </if>
            <if test="bo.contentDetail != null and bo.contentDetail != ''">
                and a.content_detail like concat('%',#{bo.contentDetail},'%')
            </if>
            <if test="bo.status != null and bo.status.size > 0">
                and a.status in
                <foreach collection="bo.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
