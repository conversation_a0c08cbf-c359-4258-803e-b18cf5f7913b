<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.user.SysUserRoleMapper">

    <select id="getRoleList" resultType="com.dbj.classpal.admin.common.dto.sys.user.SysUserRoleDTO">
        SELECT s.user_id,
               s.role_id,
               t.name as roleName
        FROM sys_user_role s
                 LEFT JOIN  sys_role t on s.role_id = t.id
        WHERE
            s.is_deleted = 0
            and t.role_status = 1
              AND s.user_id IN
              <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
              </foreach>
    </select>
</mapper>
