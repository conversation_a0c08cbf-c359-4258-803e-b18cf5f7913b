<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.user.SysUserMapper">

    <select id="pageSysUser" resultType="com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO">
        select * from sys_user a
        <where>
            a.is_deleted = 0
            <if test="bo.deptId != null">
                and a.id in (select user_id from sys_user_dept b where  is_deleted = 0 and b.dept_id = #{bo.deptId})
            </if>
            <if test="bo.accounts != null and bo.accounts != ''">
                and a.accounts like concat('%', #{bo.accounts}, '%')
            </if>
            <if test="bo.nickName != null and bo.nickName != ''">
                and a.nick_name like concat('%', #{bo.nickName}, '%')
            </if>
            <if test="bo.phone != null and bo.phone != ''">
                and a.phone  like concat('%', #{bo.phone}, '%')
            </if>
            <if test="bo.roleName != null and bo.roleName != ''">
                and EXISTS (
                    SELECT 1
                    FROM sys_user_role s
                    INNER JOIN  sys_role t on s.role_id = t.id
                    WHERE t.`name` like concat('%', #{bo.roleName}, '%')
                    and t.is_deleted = 0
                    and s.is_deleted = 0
                    and t.status = 1
                    and a.id = s.user_id
                )
            </if>
            <if test="bo.deptName != null and bo.deptName != ''">
                and EXISTS (
                    SELECT 1
                    FROM sys_user_dept s
                    INNER JOIN  sys_dept t on s.dept_id = t.id
                    WHERE t.`dept_name` like concat('%', #{bo.deptName}, '%')
                    and t.is_deleted = 0
                    and s.is_deleted = 0
                    and s.user_id = a.id
                )
            </if>
            <if test="bo.accountsStatus != null">
                and a.accounts_status = #{bo.accountsStatus}
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>
