<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.sys.user.SysUserDeptMapper">

    <select id="getSysUserInfoByDeptIdGroup" resultType="com.dbj.classpal.admin.common.dto.sys.dept.SysUserDeptGroupDTO">
            WITH RECURSIVE dept_tree AS (
                -- 基础查询：获取所有部门（作为根节点）
                SELECT
                    id,
                    father_id,
                    dept_name,
                    tenant_id,
                    id AS root_dept_id  -- 标记当前部门的根父部门ID
                FROM sys_dept
                    where is_deleted = 0

                UNION ALL

                -- 递归查询：逐级关联子部门
                SELECT
                    d.id,
                    d.father_id,
                    d.dept_name,
                    d.tenant_id,
                    dt.root_dept_id  -- 继承根父部门ID
                FROM sys_dept d
                         INNER JOIN dept_tree dt ON d.father_id = dt.id
                where d.is_deleted = 0
            )
-- 按根父部门统计所有子部门的人数
            SELECT
                dt.root_dept_id AS dept_id,
                COUNT(ud.user_id) AS num
            FROM dept_tree dt
                     LEFT JOIN sys_user_dept ud ON ud.dept_id = dt.id  -- 关联子部门的用户
            where ud.is_deleted = 0
            GROUP BY dt.root_dept_id
            ORDER BY dt.root_dept_id;
    </select>

    <select id="getUserDeptList" resultType="com.dbj.classpal.admin.common.dto.sys.user.SysUserDeptDTO">
        SELECT
            s.user_id,
            s.dept_id,
            t.dept_name
        FROM sys_user_dept s
                 LEFT JOIN  sys_dept t on s.dept_id = t.id
        WHERE
            s.is_deleted = 0
            AND t.is_deleted = 0
            AND s.user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
    </select>

    <select id="getUserNameDeptList" resultType="com.dbj.classpal.admin.common.dto.sys.user.SysDeptUserNameDTO">
        SELECT
            s.user_id,
            s.dept_id,
            u.nick_name,
            u.avatar
        FROM sys_user_dept s
            LEFT JOIN  sys_user u on s.user_id = u.id
        WHERE
            s.is_deleted = 0
          AND u.is_deleted = 0

    </select>
</mapper>
