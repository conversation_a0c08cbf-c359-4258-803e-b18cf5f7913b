<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.file.SysFileExportExcelMapper">


    <select id="pageSysFileExportExcel" resultType="com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO">
        SELECT
            t.id,
            t.sign,
            t.file_name,
            t.file_url,
            t.handle_start_time,
            t.handle_end_time,
            t.type,
            t.error_msg,
            t.param_json,
            t.status
        FROM
            sys_file_export_excel t
        WHERE
            is_deleted = 0
          and t.create_by = #{bo.createBy}
        <if test="bo.status != null and bo.status.size > 0">
            AND t.status in
            <foreach collection="bo.status" item="item" separator="," close=")" open="(">
                 #{item}
            </foreach>
        </if>
        order by t.id desc
    </select>


     <select id="sysFileExportExcelCount" resultType="com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO">
        SELECT
            t.status,
            count(1) as num
        FROM
            sys_file_export_excel t
        WHERE
            is_deleted = 0
          and is_read = 0
          and t.create_by = #{userId}
        GROUP BY
            t.status
    </select>
</mapper>
