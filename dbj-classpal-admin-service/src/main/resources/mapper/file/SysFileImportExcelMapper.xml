<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.file.SysFileImportExcelMapper">

    <select id="pageSysFileImportExcel" resultType="com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO">
        select * from sys_file_import_excel
        <where>
            is_deleted = 0
            and create_by = #{bo.createBy}
            <if test="bo.status != null and bo.status.size > 0">
                and status in
                <foreach collection="bo.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="sysFileImportExcelCount" resultType="com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO">

        select status,count(1) as num from sys_file_import_excel
        <where>
            is_deleted = 0
            and is_read = 0
            and create_by = #{userId}
        </where>
        group by status

    </select>

    <select id="getFileInfoTop3" resultType="com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO">
        select * from sys_file_import_excel
        <where>
            is_deleted = 0
            and type = #{type}
        </where>
            order by create_time desc
            limit 3
    </select>
    <select id="checkProcessMd5File" resultType="com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO">
        select * from sys_file_import_excel
        <where>
            is_deleted = 0
            and param_json like concat ('%\"md5\":\"', #{bo.md5}, '\"%') and status &lt; 2
        </where>
    </select>

    <select id="getFailImportExcel" resultType="com.dbj.classpal.admin.service.entity.file.SysFileImportExcel">
        select * from sys_file_import_excel where status &lt; #{bo.status} and create_time  &lt; NOW() - INTERVAL #{bo.hours}*3600 SECOND
    </select>


    <update id="updateFailImportExcel">
        UPDATE
            sys_file_import_excel
        SET status = #{bo.status},error_msg = #{bo.msg}
        WHERE id IN
        <foreach item="id" collection="bo.ids" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>
    <select id="getByAnalysisJobId" resultType="com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO">
        select
            *
        from sys_file_import_excel
        where analysis_submit_job_id = #{bo.analysisSubmitJobId}
    </select>
    <select id="getByTransCodeJobId" resultType="com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO">
        select
            *
        from sys_file_import_excel
        where trans_submit_job_id = #{bo.transSubmitJobId}
    </select>
</mapper>
