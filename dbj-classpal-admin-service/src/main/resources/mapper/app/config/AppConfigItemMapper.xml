<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.admin.service.mapper.app.config.AppConfigItemMapper">
    <select id="pageSharePosterInfo" resultType="com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO">
        select
            aci.*
        from app_config_item aci
        left join app_config_type act on act.id = aci.type_id
        where act.type_code = #{bo.typeCode} and aci.is_deleted = 0
        <if test="bo.itemTitle != null and bo.itemTitle != ''">
            and aci.item_title like concat('%',#{bo.itemTitle},'%')
        </if>
        <if test="bo.itemStatus != null and bo.itemStatus != ''">
            and aci.item_status = #{bo.itemStatus}
        </if>
        <if test="bo.version != null and bo.version != ''">
            and aci.version = #{bo.version}
        </if>
        order by aci.sort_order desc
    </select>

    <select id="apiPageSharePosterInfo" resultType="com.dbj.classpal.admin.client.dto.app.config.AppConfigSharePosterApiQueryDTO">
        select
            aci.*
        from app_config_item aci
        left join app_config_type act on act.id = aci.type_id
        where act.type_code = #{bo.typeCode} and aci.is_deleted = 0  and aci.item_status = #{bo.itemStatus}
        <if test="bo.itemTitle != null and bo.itemTitle != ''">
            and aci.item_title like concat('%',#{bo.itemTitle},'%')
        </if>
        <if test="bo.version != null and bo.version != ''">
            and aci.version = #{bo.version}
        </if>
        order by aci.sort_order desc,aci.create_time desc
    </select>
</mapper>
