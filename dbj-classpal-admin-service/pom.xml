<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-admin-bus</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>dbj-classpal-admin-service</artifactId>
    <name>dbj-classpal-admin-service</name>


    <dependencies>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-admin-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-admin-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-commons-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-mybatis-plus-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-redisson-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-enums-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-jwt-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-app-client</artifactId>
            <version>${parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-books-client</artifactId>
            <version>${parent.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
    </build>
</project>