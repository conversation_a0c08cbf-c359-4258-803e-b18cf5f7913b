PARAMS="--server.port=${Server_Port:-62001}"

# JVM堆内存的相关选项
JAVA_OPTS="-Xms${XMS_OPTS:-256m} -Xmx${XMX_OPTS:-1G}"

# NACOS相关的选项
NACOS_OPTS="-Djava.security.egd=file:/dev/./urandom \
	-Dfile.encoding=utf8 \
	-Dspring.profiles.active=${profiles_active:-prod} \
	-Dspring.cloud.nacos.username=${Nacos_Username:-nacos} \
	-Dspring.cloud.nacos.password=${Nacos_Password:-nacos} \
	-Dspring.cloud.nacos.discovery.namespace.=${Nacos_Namespace:-k8s-classpal-prod} \
	-Dspring.cloud.nacos.config.namespace=${Nacos_Namespace:-k8s-classpal-prod} \
	-Dspring.cloud.nacos.config.file-extension=yaml \
	-Dspring.cloud.nacos.server-addr=${Nacos_Server_Addr:-172.23.176.188:8848}"

# 启动命令
java -jar ${JAVA_OPTS} ${NACOS_OPTS}  /dbj-classpal-admin-api.jar  ${PARAMS}
