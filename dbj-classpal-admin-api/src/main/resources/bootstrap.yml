spring:
  application:
    name: dbj-classpal-admin-api-${version:v1}
    version: ${version:v1}
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      username: ${Nacos_Username:nacos}
      password: ${Nacos_Password:nacos}
      discovery:
        server-addr: ${Nacos_Server_Addr:192.168.110.209:18848}  #nacos服务地址
        namespace: ${Nacos_Namespace:classpal}
        group: DEFAULT_GROUP
        service: ${spring.application.name}
      config:
        server-addr: ${Nacos_Server_Addr:192.168.110.209:18848}
        file-extension: yaml
        namespace: ${Nacos_Namespace:classpal}
        shared-configs:
          - data-id: common.yaml
            refresh: true
          - data-id: dbj-classpal-admin-api.yaml
            refresh: true
