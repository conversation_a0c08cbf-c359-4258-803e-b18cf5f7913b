package com.dbj.classpal.admin.api.web.sys.user;


import com.dbj.classpal.admin.common.bo.sys.user.SysUserHelpSaveBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserSaveBO;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserHelpService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户与部门关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@RequestMapping("/sys-user-help")
@Tag(name = "用户部门关联信息接口", description = "用户部门关联相关操作")
public class SysUserHelpController {


    @Resource
    private ISysUserHelpService sysUserService;

    @PostMapping("/saveSysUserHelp")
    @Operation(summary = "保存用户帮助文档操作信息", description = "保存用户帮助文档操作信息")
    public Boolean saveSysUserHelp(@Validated @RequestBody SysUserHelpSaveBO sysUser) throws BusinessException {
        return sysUserService.saveSysUserHelp(sysUser);
    }
}
