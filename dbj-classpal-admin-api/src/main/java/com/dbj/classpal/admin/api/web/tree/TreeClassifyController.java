package com.dbj.classpal.admin.api.web.tree;


import com.dbj.classpal.books.client.api.tree.TreeClassifyApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyAddBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyEditBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyListBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyMoveBO;
import com.dbj.classpal.books.client.dto.tree.TreeClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 树形目录 前端控制器
 * </p>
 *
 * <AUTHOR> Yi
 * @since 2025-05-27
 */
@Tag(name = "树形目录", description = "树形目录相关接口")
@RestController
@RequestMapping("/tree-classify")
public class TreeClassifyController {

	@Resource
	private TreeClassifyApi treeClassifyApi;

	@Operation(summary = "获取树", description = "获取树")
	@PostMapping("/getTree")
	public List<TreeClassifyDTO> getTree(@Validated @RequestBody TreeClassifyListBO bo) throws BusinessException {
		RestResponse<List<TreeClassifyDTO>> result = treeClassifyApi.getTree(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "添加树节点", description = "添加树节点")
    @PostMapping(value = "/addNode")
    public Boolean addNode(@Validated @RequestBody TreeClassifyAddBO bo) throws BusinessException {
		RestResponse<Boolean> result = treeClassifyApi.addNode(bo);
		return result.returnProcess(result);
    }

	@Operation(summary = "重命名树节点", description = "重命名树节点")
	@PostMapping(value = "/renameNode")
	public Boolean renameNode(@Validated @RequestBody TreeClassifyEditBO bo) throws BusinessException {
		RestResponse<Boolean> result = treeClassifyApi.renameNode(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "删除树节点", description = "删除树节点")
	@PostMapping(value = "/deleteNode")
	public Boolean deleteNode(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
		RestResponse<Boolean> result = treeClassifyApi.deleteNode(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "移动树节点", description = "移动树节点")
	@PostMapping(value = "/moveNode")
	public Boolean moveNode(@Validated @RequestBody TreeClassifyMoveBO bo) throws BusinessException {
		RestResponse<Boolean> result = treeClassifyApi.moveNode(bo);
		return result.returnProcess(result);
	}
}
