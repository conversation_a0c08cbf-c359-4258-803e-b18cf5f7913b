package com.dbj.classpal.admin.api.web.file;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelBO;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelClearBO;
import com.dbj.classpal.admin.common.bo.file.excelfile.SysFileExportExcelSaveBO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.excelfile.SysFileExportExcelDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.service.service.file.ISysFileExportExcelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 导出文件记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "导出excel文件上传接口", description = "导出excel文件上传接口")
@RestController
@RequestMapping("/sys-file-export-excel")
public class SysFileExportExcelController {

    @Resource
    private ISysFileExportExcelService sysFileExportService;


    /**
     * 查询一条数据的明细
     * @param id
     * @return
     */
    /**
     * 获取一个File
     *
     * @param id
     * @return RestResponse<FileDTO>
     */
    @Operation(summary =  "获取一个File", description = "获取一个File")
    @GetMapping("getSysFileExportExcel")
    public SysFileExportExcelDTO getSysFileExportExcel(@Parameter(description = "主键id") @RequestParam Integer id) throws BusinessException{
        return sysFileExportService.getSysFileExportExcel(id);
    }

    /**
     * 保存导入数据
     * @param saveBO
     * @return
     * @throws Exception
     */
    /**
     * 新增File
     *
     * @param bo
     * @return RestResponse<Integer>
     */
    @Operation(summary =  "保存导入数据", description = "保存导入数据")
    @PostMapping("saveExportFile")
    public Integer saveExportFile(@RequestBody SysFileExportExcelSaveBO saveBO) throws Exception{
        return sysFileExportService.saveExportFile(saveBO);
    }

    @Operation(summary =  "获取文件上传记录统计", description = "获取文件上传记录")
    @PostMapping("sysFileExportExcelCount")
    public List<SysFileExportExcelCountDTO> sysFileExportExcelCount(){
        return sysFileExportService.sysFileExportExcelCount();
    }

    /**
     * 分页查询
     * @param page
     * @return
     */
    @Operation(summary =  "分页查询", description = "分页查询")
    @PostMapping("pageSysFileExportExcel")
    public Page<SysFileExportExcelDTO> pageSysFileExportExcel(@RequestBody PageInfo<SysFileExportExcelBO> page){
        return sysFileExportService.pageSysFileExportExcel(page);
    }

    /**
     * 清空失败和清空全部
     * @param bo
     * @return
     */
    @Operation(summary =  "清空失败和清空全部", description = "清空失败和清空全部")
    @PostMapping("deleteClear")
    public Boolean deleteClear(@RequestBody SysFileExportExcelClearBO bo){
        return sysFileExportService.deleteClear(bo);
    }



}
