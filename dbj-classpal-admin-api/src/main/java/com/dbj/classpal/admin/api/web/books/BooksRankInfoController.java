package com.dbj.classpal.admin.api.web.books;


import com.dbj.classpal.admin.service.remote.books.book.BooksRankRemoteService;
import com.dbj.classpal.books.client.bo.books.BooksRankClassifyBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoUpdForceApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankClassifyDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksTreeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 图书卷册赠册表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Tag(name = "【图书】-册数管理接口", description = "【图书】-册数管理接口")
@RestController
@RequestMapping("/books-rank-info")
public class BooksRankInfoController {


    @Resource
    private BooksRankRemoteService booksRankRemoteService;

    @Operation(summary =  "查询图书下的所有册数", description = "查询图书下的所有册数")
    @PostMapping("/books/rank/list")
    public List<BooksRankInfoApiDTO> list(@RequestBody BooksRankInfoApiBO bookRankInfoApiBO) throws BusinessException{
        return booksRankRemoteService.list(bookRankInfoApiBO);
    }

    @Operation(summary =  "修改册数强制连接", description = "修改册数强制连接")
    @PostMapping("/books/rank/update/forcePromotionUrl")
    public Boolean updateForcePromotionUrl(@RequestBody BooksRankInfoUpdForceApiBO booksRankInfoUpdForceApiBO) throws BusinessException{
        return booksRankRemoteService.updateForcePromotionUrl(booksRankInfoUpdForceApiBO);
    }

    @Operation(summary =  "查看链接详情", description = "查看链接详情")
    @GetMapping("/books/rank/detail")
    public BooksRankInfoDetailDTO detail(@RequestParam Integer id) throws BusinessException{
        return booksRankRemoteService.detail(id);
    }


    @Operation(summary =  "制码导出树结构查询", description = "制码导出树结构查询")
    @GetMapping("/books/rank/incode/tree")
    public BooksTreeDTO tree(@RequestParam Integer booksId) throws BusinessException{
        return booksRankRemoteService.tree(booksId);
    }
}
