package com.dbj.classpal.admin.api.web.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appuser.AppUserRemoteService;
import com.dbj.classpal.app.client.bo.user.CancelAccountApiBO;
import com.dbj.classpal.app.client.bo.user.DisableAccountApiBO;
import com.dbj.classpal.app.client.bo.user.UserExactMatchApiBo;
import com.dbj.classpal.app.client.bo.user.UserGuideApiBO;
import com.dbj.classpal.app.client.bo.user.UserIdApiBO;
import com.dbj.classpal.app.client.bo.user.UserIdsApiBO;
import com.dbj.classpal.app.client.bo.user.UserQueryApiBO;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.app.client.dto.user.UserDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * App用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "APP用户管理")
@RestController
@RequestMapping("/api/user")
@Slf4j
public class AppUserController {

    @Resource
    private AppUserRemoteService userService;
    @Operation(summary = "获取当前用户信息")
    @GetMapping("/current")
    public CurrentUserApiDTO getCurrentUser() throws BusinessException {
        UserIdApiBO userIdApiBO = new UserIdApiBO();
        return userService.getCurrentUser(userIdApiBO);
    }

    @Operation(summary = "根据用户手机号或UID精确查询用户信息")
    @PostMapping("/exact/match")
    public UserDetailApiDTO exactMatch(@RequestBody @Valid UserExactMatchApiBo request) throws BusinessException {
        return userService.exactMatch(request);
    }



    @Operation(summary = "更新学段年级")
    @PostMapping("/guide/stage")
    public void updateStage(@RequestBody @Valid UserGuideApiBO request) throws BusinessException {
        // 1. 参数校验
        if (request.getStage() == null) {
            throw new BusinessException("学段不能为空");
        }
        
        // 2. 更新用户信息
        userService.updateUserGuide(request);
    }

    @Operation(summary = "更新设备类型")
    @PostMapping("/guide/device")
    public void updateDevice(@RequestBody @Valid UserGuideApiBO request) throws BusinessException {
        // 1. 参数校验
        if (request.getDeviceTypeId() == null) {
            throw new BusinessException("设备类型不能为空");
        }
        
        // 2. 更新用户信息
        userService.updateUserGuide(request);
    }

    @Operation(summary = "更新用户类型")
    @PostMapping("/guide/type")
    public void updateUserType(@RequestBody @Valid UserGuideApiBO request) throws BusinessException {
        // 1. 参数校验
        if (request.getUserType() == null) {
            throw new BusinessException("用户类型不能为空");
        }
        
        // 2. 更新用户信息
        userService.updateUserGuide(request);
    }



    @Operation(summary = "分页查询用户")
    @PostMapping("/page")
    public Page<UserDetailApiDTO> pageUsers(@RequestBody @Valid PageInfo<UserQueryApiBO> pageRequest) throws BusinessException {
        return userService.pageUsers(pageRequest);
    }

    @Operation(summary = "获取用户详情")
    @PostMapping("/detail")
    public UserDetailApiDTO getUserDetail(@RequestBody UserIdApiBO userIdBO) throws BusinessException {
        return userService.getUserDetail(userIdBO );
    }
    @Operation(summary = "启用账号")
    @PostMapping("/enable")
    public void enableAccount(@RequestBody @Valid DisableAccountApiBO request) throws BusinessException {
        userService.enableAccount(request);
    }

    @Operation(summary = "禁用账号")
    @PostMapping("/disable")
    public void disableAccount(@RequestBody @Valid DisableAccountApiBO request) throws BusinessException {
        userService.disableAccount(request);
    }

    @Operation(summary = "注销账号")
    @PostMapping("/cancel")
    public void cancelAccount(@RequestBody @Valid CancelAccountApiBO request) throws BusinessException {
        userService.cancelAccount(request);
    }
}
