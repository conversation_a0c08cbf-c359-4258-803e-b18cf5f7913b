package com.dbj.classpal.admin.api.web.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appebooksconfig.AppEbooksConfigWatermarkTemplateRemoteService;
import com.dbj.classpal.admin.service.remote.appevaluation.AppEvaluationRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateEditApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationQueryApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationSaveApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/app-ebooks-config-watermark-template")
@Tag(name = "电子样书-样书配置-水印模板", description = "电子样书-样书配置-水印模板")
public class AppEbooksConfigWatermarkTemplateController {
    @Resource
    private AppEbooksConfigWatermarkTemplateRemoteService watermarkTemplateRemoteService;

    @Operation(summary = "分页查询水印模板列表", description = "分页查询水印模板列表")
    @PostMapping("/pageInfo")
    public Page<AppEbooksConfigWatermarkTemplateQueryApiDTO> pageInfo(@Validated @RequestBody PageInfo<AppEbooksConfigWatermarkTemplateQueryApiBO> pageInfo) throws BusinessException {
        return watermarkTemplateRemoteService.pageInfo(pageInfo);
    }

    @Operation(summary = "查询所有水印模板列表", description = "查询所有水印模板列表")
    @PostMapping("/getAll")
    public List<AppEbooksConfigWatermarkTemplateQueryApiDTO> getAll() throws BusinessException {
        return watermarkTemplateRemoteService.getAll();
    }

    @Operation(summary = "新增水印模板", description = "新增水印模板")
    @PostMapping("/saveEbooksConfigWatermarkTemplate")
    public Boolean saveEbooksConfigWatermarkTemplate(@Validated @RequestBody AppEbooksConfigWatermarkTemplateSaveApiBO bo) throws BusinessException {
        return watermarkTemplateRemoteService.saveEbooksConfigWatermarkTemplate(bo);
    }

    @Operation(summary = "修改水印模板", description = "修改水印模板")
    @PostMapping("/updateEbooksConfigWatermarkTemplate")
    public Boolean updateEbooksConfigWatermarkTemplate(@Validated @RequestBody AppEbooksConfigWatermarkTemplateEditApiBO bo) throws BusinessException {
        return watermarkTemplateRemoteService.updateEbooksConfigWatermarkTemplate(bo);
    }

    @Operation(summary = "删除水印模板", description = "删除水印模板")
    @PostMapping("/deleteEbooksConfigWatermarkTemplate")
    public Boolean deleteEbooksConfigWatermarkTemplate(@Validated @RequestBody CommonIdsApiBO bo) throws BusinessException {
        return watermarkTemplateRemoteService.deleteEbooksConfigWatermarkTemplate(bo);
    }
}
