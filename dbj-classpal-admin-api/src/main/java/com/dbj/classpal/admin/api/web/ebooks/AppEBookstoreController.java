package com.dbj.classpal.admin.api.web.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.ebooks.AppEBookstoreRemoteService;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app-ebook-store")
@Tag(name = "电子样书-书城", description = "电子样书-书城")
public class AppEBookstoreController {

    @Resource
    private AppEBookstoreRemoteService appEBookstoreRemoteService;

    @Operation(summary = "查询书城列表", description = "查询书城列表")
    @PostMapping("/pageInfo")
    public Page<AppEBookstoreApiDTO> page(@RequestBody PageInfo<AppEBookstoreQueryApiBO> pageRequest) throws BusinessException {
        return appEBookstoreRemoteService.page(pageRequest);
    }

    @Operation(summary = "查看详情", description = "查看详情")
    @PostMapping("/detail")
    public AppEBookstoreApiDTO detail(@RequestBody @Valid AppEBookstoreIdApiBO idBO) throws BusinessException{
        return appEBookstoreRemoteService.detail(idBO);
    }

    @Operation(summary = "添加书城", description = "添加书城")
    @PostMapping("/save")
    public Integer save(@RequestBody @Valid AppEBookstoreSaveApiBO saveBO) throws BusinessException{
        return appEBookstoreRemoteService.save(saveBO);
    }

    @Operation(summary = "修改书城", description = "修改书城")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid AppEBookstoreUpdateApiBO saveBO) throws BusinessException{
        return appEBookstoreRemoteService.update(saveBO);
    }

    @Operation(summary = "删除书城", description = "删除书城")
    @PostMapping("/delete")
    public Boolean delete(@RequestBody @Valid AppEBookstoreIdApiBO idBO) throws BusinessException{
        return appEBookstoreRemoteService.delete(idBO);
    }

    @Operation(summary = "批量删除", description = "批量删除")
    @PostMapping("/deleteBatch")
    public Boolean deleteBatch(@RequestBody @Valid AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        return appEBookstoreRemoteService.deleteBatch(idsBO);
    }
    
    @Operation(summary = "批量启用", description = "批量启用")
    @PostMapping("/enableBatch")
    public Boolean enableBatch(@RequestBody @Valid AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        return appEBookstoreRemoteService.enableBatch(idsBO);
    }

    @Operation(summary = "批量禁用", description = "批量禁用")
    @PostMapping("/disableBatch")
    public Boolean disableBatch(@RequestBody @Valid AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        return appEBookstoreRemoteService.disableBatch(idsBO);
    }

    @Operation(summary = "批量允许下载", description = "批量允许下载")
    @PostMapping("/allowDownloadBatch")
    public Boolean allowDownloadBatch(@RequestBody @Valid AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        return appEBookstoreRemoteService.allowDownloadBatch(idsBO);
    }

    @Operation(summary = "批量禁用下载", description = "批量禁用下载")
    @PostMapping("/disableDownloadBatch")
    public Boolean disableDownloadBatch(@RequestBody @Valid AppEBookstoreIdsApiBO idsBO) throws BusinessException{
        return appEBookstoreRemoteService.disableDownloadBatch(idsBO);
    }
}
