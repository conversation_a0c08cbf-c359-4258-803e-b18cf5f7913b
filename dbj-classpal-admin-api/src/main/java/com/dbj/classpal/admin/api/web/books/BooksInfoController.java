package com.dbj.classpal.admin.api.web.books;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.service.remote.books.book.BooksInfoRemoteService;
import com.dbj.classpal.books.client.bo.books.*;
import com.dbj.classpal.books.client.dto.books.BooksInfoDetailApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 图书表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Tag(name = "【图书】-图书管理接口", description = "【图书】-图书管理接口")
@RestController
@RequestMapping("/books-info")
public class BooksInfoController {

    @Resource
    private BooksInfoRemoteService booksInfoRemoteService;

    @Operation(summary =  "分页查询图书", description = "分页查询图书")
    @PostMapping("/books/info/pageInfo")
    public Page<BooksInfoPageApiDTO> pageInfo(@RequestBody PageInfo<BooksInfoPageBO> pageRequest) throws BusinessException{
        return booksInfoRemoteService.pageInfo(pageRequest);
    }
    @Operation(summary =  "新增图书", description = "新增图书")
    @PostMapping("/books/info/save")
    public Boolean save(@Valid @RequestBody BooksInfoSaveApiBO saveBO) throws BusinessException{
        return booksInfoRemoteService.save(saveBO);
    }
    @Operation(summary =  "修改图书", description = "修改图书")
    @PostMapping("/books/info/update")
    public Boolean update(@Valid  @RequestBody BooksInfoUpdApiBO updBO) throws BusinessException{
        return booksInfoRemoteService.update(updBO);
    }
    @Operation(summary =  "批量删除", description = "批量删除")
    @PostMapping("/books/info/batchDelete")
    public Boolean batchDelete(@Valid @RequestBody BaseIdsBO bo) throws BusinessException{
        return booksInfoRemoteService.batchDelete(bo.getIds());
    }
    @Operation(summary =  "查询详情", description = "查询详情")
    @GetMapping("/books/info/detail")
    public BooksInfoDetailApiDTO detail(@RequestParam Integer id) throws BusinessException{
        return booksInfoRemoteService.detail(id);
    }
    @Operation(summary =  "批量隐藏显示", description = "批量隐藏显示")
    @PostMapping("/books/info/batchHide")
    public Boolean batchHide(@Valid @RequestBody BooksBatchHideApiBO batchHideBO) throws BusinessException{
        return booksInfoRemoteService.batchHide(batchHideBO);
    }
    @Operation(summary =  "批量上下架", description = "批量上下架")
    @PostMapping("/books/info/batchLaunch")
    public Boolean batchLaunch(@Valid @RequestBody BooksBatchLaunchApiBO batchLaunchBO) throws BusinessException{
        return booksInfoRemoteService.batchLaunch(batchLaunchBO);
    }

    /**
     * 根据产品名称或者编码获取列表
     * @param request
     * @return
     */
    @Operation(summary =  "根据产品名称或者编码获取列表", description = "根据产品名称或者编码获取列表")
    @PostMapping("/books/printer/info")
    List<ProductInfoApiDTO> queryProductListByCodeOrName(@RequestBody ProductInfoApiBO request) throws BusinessException {
        return booksInfoRemoteService.queryProductListByCodeOrName(request);
    }

    /**
     * 根据产品ID获取详情
     * @param request
     * @return
     */
    @Operation(summary =  "根据产品ID获取详情", description = "根据产品ID获取详情")
    @PostMapping("/books/printer/detail")
    ProductInfoDetailApiDTO queryProductInfoById(@RequestBody ProductInfoIdApiBO request) throws BusinessException {
        return booksInfoRemoteService.queryProductInfoById(request);
    }

    @Operation(summary =  "查询图书列表", description = "查询图书列表")
    @PostMapping("/books/info/list")
    public List<BooksInfoPageApiDTO> list(@RequestBody BooksInfoPageBO pageRequest) throws BusinessException {
        return booksInfoRemoteService.list(pageRequest);
    }
}
