package com.dbj.classpal.admin.api.web.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appnotice.AppNoticeRemoteService;
import com.dbj.classpal.app.client.bo.notice.*;
import com.dbj.classpal.app.client.dto.notice.NoticeDetailApiDTO;
import com.dbj.classpal.app.client.dto.notice.NoticeListApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * APP公告管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "APP公告管理")
@RestController
@RequestMapping("/api/notice")
public class AppNoticeController {

    @Resource
    private AppNoticeRemoteService noticeService;

    @Operation(summary = "分页查询公告")
    @PostMapping("/page")
    public Page<NoticeDetailApiDTO> pageNotices(@RequestBody @Valid PageInfo<NoticeQueryApiBO> pageRequest) throws BusinessException {
        return noticeService.pageNotices(pageRequest);
    }

    @Operation(summary = "新增公告")
    @PostMapping("/save")
    public Boolean saveNotice(@RequestBody @Valid NoticeSaveApiBO notice) throws BusinessException {
       return noticeService.saveNotice(notice);
    }

    @Operation(summary = "修改公告")
    @PostMapping("/update")
    public Boolean updateNotice(@RequestBody @Valid NoticeUpdateApiBO notice) throws BusinessException {
       return noticeService.updateNotice(notice);
    }

    @Operation(summary = "删除公告")
    @PostMapping("/delete")
    public Boolean deleteNotice(@RequestBody @Valid NoticeIdApiBO notice) throws BusinessException {
        return noticeService.deleteNotice(notice);
    }


    @Operation(summary = "启用公告")
    @PostMapping("/enable")
    public Boolean enableNotice(@RequestBody @Valid NoticeIdApiBO notice) throws BusinessException {
        return noticeService.enableNotice(notice);
    }

    @Operation(summary = "禁用公告")
    @PostMapping("/disable")
    public Boolean disableNotice(@RequestBody @Valid NoticeIdApiBO notice) throws BusinessException {
        return noticeService.disableNotice(notice);
    }

    @Operation(summary = "获取公告详情")
    @PostMapping("/detail")
    public NoticeDetailApiDTO getNoticeDetail(@RequestBody NoticeIdApiBO req) throws BusinessException {
        return noticeService.getNoticeDetail(req);
    }

    @Operation(summary = "批量启用")
    @PostMapping("/batch/enable")
    public Boolean batchEnable(@RequestBody NoticeIdsApiBO ids) throws BusinessException {
        return noticeService.batchEnable(ids);
    }

    @Operation(summary = "批量禁用")
    @PostMapping("/batch/disable")
    public Boolean batchDisable(@RequestBody NoticeIdsApiBO ids) throws BusinessException {
        return noticeService.batchDisable(ids);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/batch/delete")
    public Boolean batchDelete(@RequestBody NoticeIdsApiBO ids) throws BusinessException {
        return noticeService.batchDelete(ids);
    }

    @Operation(summary = "获取有效公告列表")
    @PostMapping("/list")
    public List<NoticeListApiDTO> listValidNotices(@RequestBody NoticeQueryParamApiBO queryParamApiBO) throws BusinessException {
        return noticeService.listValidNotices(queryParamApiBO);
    }
}
