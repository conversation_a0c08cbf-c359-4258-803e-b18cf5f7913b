package com.dbj.classpal.admin.api.web.album;

import com.dbj.classpal.admin.service.remote.appalbum.AppAlbumElementsBusinessRefRemoteService;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefQueryApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefQueryCommonApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefQueryApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefController
 * Date:     2025-04-18 14:54:03
 * Description: 表名： ,描述： 表
 */
@RestController
@RequestMapping("/api/albumElementsBusinessRef")
@Tag(name = "内容管理-专辑管理-专辑业务关联")
public class AppAlbumElementsBusinessRefController {

    @Resource
    private AppAlbumElementsBusinessRefRemoteService service;

    @Operation(summary = "专辑-业务关联专辑列表查询")
    @PostMapping("/getElementsBusinessRef")
    public List<AppAlbumElementsBusinessRefQueryApiDTO> getElementsBusinessRefMaterialRef(@RequestBody @Validated AppAlbumElementsBusinessRefQueryCommonApiBO bo) throws BusinessException {
        return service.getElementsBusinessRef(bo);
    }

    @Operation(summary = "专辑-关联素材列表业务查询")
    @PostMapping("/getElementsBusinessRefMaterialRef")
    public AppAlbumElementsBusinessRefMaterialQueryApiDTO getElementsBusinessRefMaterialRef(@RequestBody @Validated AppAlbumElementsBusinessRefQueryApiBO bo) throws BusinessException {
        return service.getElementsBusinessRefMaterialRef(bo);
    }

    @Operation(summary = "专辑-业务修改专辑关联")
    @PostMapping("/saveOrUpdateElementsBusinessRefMaterialRef")
    public Boolean saveOrUpdateElementsBusinessRefMaterialRef(@RequestBody @Validated AppAlbumElementsBusinessRefSaveApiBO bo) throws BusinessException {
        return service.saveOrUpdateElementsBusinessRefMaterialRef(bo);
    }

    @Operation(summary = "专辑-查看单条引用数据")
    @PostMapping("/getAlbumElementsBusinessRefBooks")
    public BooksRefDirectApiDTO getAlbumElementsBusinessRefBooks(@RequestBody @Validated CommonIdApiBO bo) throws BusinessException {
        return service.getAlbumElementsBusinessRefBooks(bo);
    }
}
