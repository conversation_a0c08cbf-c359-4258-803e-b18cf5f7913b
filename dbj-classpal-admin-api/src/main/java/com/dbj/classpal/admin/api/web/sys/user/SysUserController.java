package com.dbj.classpal.admin.api.web.sys.user;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserFirstUpdPasswordBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserSaveBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdAccountsStatusBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdNameBO;
import com.dbj.classpal.admin.common.bo.sys.user.SysUserUpdPasswordBO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDetailDTO;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@RestController
@RequestMapping("/sys-user")
@Tag(name = "用户信息接口", description = "用户相关操作")
public class SysUserController {

    @Autowired
    private ISysUserService sysUserService;

    @GetMapping("/getSysUserById")
    @Operation(summary = "根据ID获取用户信息", description = "根据ID获取用户信息")
    public SysUserDetailDTO getSysUserById(@Parameter(description = "用户ID") @RequestParam Integer id) throws BusinessException {
        // ...
        return sysUserService.getSysUserById(id);
    }
    @PostMapping("/pageSysUser")
    @Operation(summary = "分页获取用户信息", description = "分页获取用户信息")
    public Page<SysUserDTO> pageSysUser(@RequestBody PageInfo<SysUserBO> page) {
        return sysUserService.pageSysUser(page);
    }
    @PostMapping("/saveSysUser")
    @Operation(summary = "保存用户信息", description = "保存用户信息")
    public Boolean saveSysUser(@Validated @RequestBody SysUserSaveBO sysUser) throws BusinessException {
        return sysUserService.saveSysUser(sysUser);
    }
    @PostMapping("/updateSysUser")
    @Operation(summary = "修改用户信息", description = "修改用户信息")
    public Boolean updateSysUser(@Validated @RequestBody SysUserUpdBO sysUser) throws BusinessException {
        return sysUserService.updateSysUser(sysUser);
    }

    @PostMapping("/updateSysUserName")
    @Operation(summary = "修改用户信息", description = "修改用户信息")
    public Boolean updateSysUserName(@Validated @RequestBody SysUserUpdNameBO sysUserUpdateBO) throws BusinessException{
        return sysUserService.updateSysUserName(sysUserUpdateBO);
    }
    @PostMapping("/updateAccountsStatus")
    @Operation(summary = "修改用户状态", description = "修改用户状态")
    public Boolean updateAccountsStatus(@Validated @RequestBody SysUserUpdAccountsStatusBO sysUser) throws BusinessException {
        return sysUserService.updateAccountsStatus(sysUser);
    }
    @PostMapping("/resetPassword")
    @Operation(summary = "重置密码", description = "重置密码")
    public Boolean resetPassword(@RequestBody BaseIdsBO baseIds) throws BusinessException {
        return sysUserService.resetPassword(baseIds);
    }
    @PostMapping("/updatePassword")
    @Operation(summary = "修改密码", description = "修改密码")
    public Boolean updatePassword(@RequestBody SysUserUpdPasswordBO sysUserUpdPasswordBO) throws BusinessException {
        return sysUserService.updatePassword(sysUserUpdPasswordBO);
    }
    @PostMapping("/firstUpdatePassword")
    @Operation(summary = "第一次修改密码", description = "第一次修改密码")
    public Boolean firstUpdatePassword(@RequestBody SysUserFirstUpdPasswordBO sysUserFirstUpdPasswordBO) throws BusinessException {
        return sysUserService.firstUpdatePassword(sysUserFirstUpdPasswordBO);
    }
    @PostMapping("/delSysUser")
    @Operation(summary = "删除用户", description = "删除用户")
    public Boolean delSysUser(@Parameter(description = "主键ids") @RequestBody BaseIdsBO baseIds) throws BusinessException {
        return sysUserService.delSysUser(baseIds);
    }

}
