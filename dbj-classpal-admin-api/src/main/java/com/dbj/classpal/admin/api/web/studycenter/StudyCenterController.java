package com.dbj.classpal.admin.api.web.studycenter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.studycenter.StudyCenterRemoteService;
import com.dbj.classpal.books.client.bo.studycenter.*;
import com.dbj.classpal.books.client.dto.studycenter.AppStudyModuleDetailApiDTO;
import com.dbj.classpal.books.client.dto.studycenter.AppStudyModuleListApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
@RestController
@RequestMapping("/studyCenter")
@Tag(name = "学习中心", description = "学习中心")
public class StudyCenterController {
    @Resource
    private StudyCenterRemoteService service;
    @PostMapping("/module/create")
    @Operation(summary = "新增", description = "新增")
    Boolean createModule(@Validated @RequestBody AppStudyModuleCreateApiBO bo) throws BusinessException{
        return service.createModule(bo);
    }

    @PostMapping("/module/update")
    @Operation(summary = "编辑", description = "编辑")
    Boolean updateModule(@Validated @RequestBody AppStudyModuleUpdateApiBO bo) throws BusinessException{
        return service.updateModule(bo);
    }

    @PostMapping("/module/delete")
    @Operation(summary = "批量删除", description = "批量删除")
    Boolean deleteModule(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException{
        return service.deleteModule(idsApiBO);
    }

    @PostMapping("/module/page")
    @Operation(summary = "分页查询", description = "分页查询")
    Page<AppStudyModuleListApiDTO> pageModule(@Validated @RequestBody PageInfo<AppStudyModuleQueryPageApiBO> pageInfo) throws BusinessException {
        return service.pageModule(pageInfo);
    }

    @PostMapping("/module/detail")
    @Operation(summary = "查看详情", description = "查看详情")
    AppStudyModuleDetailApiDTO getModuleDetail(@Validated @RequestBody AppStudyModuleIdApiBO idApiBO) throws BusinessException{
        return service.getModuleDetail(idApiBO);
    }

    @PostMapping("/module/batch-publish")
    @Operation(summary = "批量发布", description = "批量发布")
    Boolean batchPublish(@Validated @RequestBody AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        return service.batchPublish(idsApiBo);
    }

    @PostMapping("/module/batch-unpublish")
    @Operation(summary = "批量取消发布", description = "批量取消发布")
    Boolean batchUnpublish(@Validated @RequestBody AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        return service.batchUnpublish(idsApiBo);
    }

    @PostMapping("/module/batch-show")
    @Operation(summary = "批量显示", description = "批量显示")
    Boolean batchShow(@Validated @RequestBody AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        return service.batchShow(idsApiBo);
    }

    @PostMapping("/module/batch-hide")
    @Operation(summary = "批量隐藏", description = "批量隐藏")
    Boolean batchHide(@Validated @RequestBody AppStudyModuleIdsApiBO  idsApiBo) throws BusinessException{
        return service.batchHide(idsApiBo);
    }
}