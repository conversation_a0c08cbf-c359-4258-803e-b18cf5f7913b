package com.dbj.classpal.admin.api.web.login;


import com.dbj.classpal.admin.common.bo.login.LoginUserBO;
import com.dbj.classpal.admin.common.dto.login.GtestCodeDTO;
import com.dbj.classpal.admin.common.dto.login.SysLoginUserDTO;
import com.dbj.classpal.admin.service.service.login.ISysLoginService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 登陆 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@RestController
@RequestMapping("/login")
@Tag(name = "用户登陆接口", description = "用户登陆接口")
public class LoginController {


    @Resource
    private ISysLoginService loginService;

    /**
     * 预登录
     * @return
     */
    @Operation(summary = "获取验证码", description = "获取验证码")
    @GetMapping("/preLogin")
    public GtestCodeDTO preLogin() {
        return loginService.preLogin();
    }
    /**
     * 登录
     * @param loginUserBO
     * @return
     */
    @Operation(summary = "帐号登陆", description = "帐号登陆")
    @PostMapping("/accounts")
    public SysLoginUserDTO accountsLogin(@RequestBody LoginUserBO loginUserBO) throws BusinessException {
        return loginService.accountsLogin(loginUserBO);
    }


    /**
     * 获取用户菜单
     * @return
     */
    @Operation(summary = "获取登陆用户菜单", description = "获取登陆用户菜单")
    @PostMapping("/getLoginUserMenu")
    public SysLoginUserDTO getLoginUserMenu() throws BusinessException {
        return loginService.getLoginUserMenu();
    }
}
