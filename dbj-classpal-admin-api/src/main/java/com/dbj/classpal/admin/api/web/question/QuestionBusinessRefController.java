package com.dbj.classpal.admin.api.web.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appevaluation.question.QuestionBusinessRefRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefPageApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSortApiBO;
import com.dbj.classpal.books.client.dto.question.QuestionBusinessRefApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: QuestionBusinessRefController
 * Date:     2025-05-21 17:40:53
 * Description: 表名： ,描述： 表
 */
@RestController
@RequestMapping("/question-business-ref")
@Tag(name = "题目业务关联查询接口", description = "题目业务关联查询接口")
public class QuestionBusinessRefController {
    @Resource
    private QuestionBusinessRefRemoteService questionBusinessRefRemoteService;

    /**
     * 分页查询题目列表
     */
    @PostMapping("/pageList")
    @Operation(summary = "分页查询关联题目列表",description = "分页查询关联题目列表")
    public Page<QuestionBusinessRefApiDTO> pageList(@RequestBody @Valid PageInfo<QuestionBusinessRefPageApiBO> pageApiBO) throws BusinessException {
        return questionBusinessRefRemoteService.pageList(pageApiBO);
    }

    /**
     * 新增题目业务关联关系
     */
    @PostMapping("/saveQuestionBusinessRef")
    @Operation(summary = "新增题目业务关联关系",description = "新增题目业务关联关系")
    public Boolean saveQuestionBusinessRef(@RequestBody @Valid QuestionBusinessRefSaveApiBO bo) throws BusinessException {
        return questionBusinessRefRemoteService.saveQuestionBusinessRef(bo);
    }


    /**
     * 删除题目业务关联关系
     */
    @PostMapping("/deleteQuestionBusinessRef")
    @Operation(summary = "删除题目业务关联关系",description = "删除题目业务关联关系")
    public Boolean deleteQuestionBusinessRef(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException {
        return questionBusinessRefRemoteService.deleteQuestionBusinessRef(bo);
    }


    /**
     * 题目业务关联关系排序
     */
    @PostMapping("/sortQuestionBusinessRef")
    @Operation(summary = "题目业务关联关系排序",description = "题目业务关联关系排序")
    public Boolean sortQuestionBusinessRef(@RequestBody @Valid QuestionBusinessRefSortApiBO bo) throws BusinessException{
        return questionBusinessRefRemoteService.sortQuestionBusinessRef(bo);
    }

}
