package com.dbj.classpal.admin.api.web.pinyin;

import com.dbj.classpal.books.client.api.pinyin.PinyinClassifyApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 拼音分类 前端控制器
 * </p>
 *
 * <AUTHOR> Yi
 * @since 2025-05-21
 */
@Tag(name = "拼音分类", description = "拼音分类相关接口")
@RestController
@RequestMapping("/pinyin")
public class PinyinClassifyController {

	@Resource
	private PinyinClassifyApi pinyinClassifyApi;

	@Operation(summary = "获取所有拼音分类", description = "获取所有拼音分类")
	@PostMapping("/getPinyinClassifyAll")
	public List<PinyinClassifyDTO> getPinyinPage(@Validated @RequestBody PinyinClassifyBO bo) throws BusinessException {
		RestResponse<List<PinyinClassifyDTO>> result = pinyinClassifyApi.getPinyinClassifyAll(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "保存拼音分类", description = "保存拼音分类")
	@PostMapping(value = "/savePinyinClassify")
	public Boolean savePinyinClassify(@Validated @RequestBody PinyinClassifyUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinClassifyApi.savePinyinClassify(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "编辑拼音分类", description = "编辑拼音分类")
	@PostMapping(value = "/updatePinyinClassify")
	public Boolean updatePinyinClassify(@Validated @RequestBody PinyinClassifyUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinClassifyApi.updatePinyinClassify(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "删除拼音分类", description = "删除拼音分类")
	@PostMapping(value = "/deletePinyinClassify")
	public Boolean deletePinyinClassify(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinClassifyApi.deletePinyinClassify(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "重新排序拼音分类", description = "重新排序拼音分类")
	@PostMapping("/sortPinyinClassify")
	public Boolean sortPinyinClassify(@Validated @RequestBody CommonIdsApiBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinClassifyApi.sortPinyinClassify(bo);
		return result.returnProcess(result);
	}
}
