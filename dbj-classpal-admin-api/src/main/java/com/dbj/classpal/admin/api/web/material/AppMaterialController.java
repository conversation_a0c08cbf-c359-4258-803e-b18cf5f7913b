package com.dbj.classpal.admin.api.web.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.enums.sys.file.FileBusinessTypeEnum;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelMaterialSaveBO;
import com.dbj.classpal.admin.service.remote.appmaterial.AppMaterialRemoteService;
import com.dbj.classpal.admin.service.service.file.ISysFileImportExcelService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.material.*;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryDicTreeApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryRootApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialStatisticsSizeApiDTO;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialController
 * Date:     2025-04-08 16:33:18
 * Description: 表名： ,描述： 表
 */
@Tag(name = "素材中心")
@RestController
@RequestMapping("/api/material")
public class AppMaterialController {

    @Resource
    private ISysFileImportExcelService fileImportExcelService;
    @Resource
    private AppMaterialRemoteService remoteService;

    @Operation(summary =  "分页查询", description = "分页查询")
    @PostMapping("/pageInfo")
    public Page<AppMaterialQueryApiDTO> pageInfo(@RequestBody PageInfo<AppMaterialQueryApiBO> pageRequest) throws Exception{
        return remoteService.pageInfo(pageRequest);
    }

    @Operation(summary =  "查询根节点", description = "查询根节点")
    @PostMapping("/getRoot")
    public AppMaterialQueryRootApiDTO getRoot() throws Exception{
        return remoteService.getRoot();
    }

    @Operation(summary =  "查询所有文件夹", description = "查询所有文件夹")
    @PostMapping("/getAllDirectsTree")
    public AppMaterialQueryDicTreeApiDTO getAllDirectsTree() throws Exception{
        return remoteService.getAllDirectsTree();
    }

    @Operation(summary =  "查询素材中心资源是否上传过", description = "查询素材中心资源是否上传过")
    @PostMapping("/materialExist")
    public Boolean materialExist(@RequestBody @Valid AppMaterialExistQueryApiBO bo) throws Exception{
        return remoteService.materialExist(bo);
    }

    @Operation(summary =  "上传文件|文件夹", description = "上传文件|文件夹")
    @PostMapping("/saveImportFile")
    public Integer saveImportFile(@RequestBody @Valid SysFileImportExcelMaterialSaveBO bo) throws Exception{
        bo.setBusinessType(FileBusinessTypeEnum.BUSINESS_MATERIAL.getCode());
        return fileImportExcelService.saveImportMaterialFile(bo);
    }

    @Operation(summary =  "资源重命名", description = "资源重命名")
    @PostMapping("/renameMaterial")
    public Boolean renameMaterial(@RequestBody @Valid AppMaterialReNameApiBO bo) throws Exception{
        return remoteService.renameMaterial(bo);
    }

    @Operation(summary =  "编辑字幕", description = "编辑字幕")
    @PostMapping("/editCaption")
    public Boolean editCaption(@RequestBody @Valid AppMaterialEditCaptionApiBO bo) throws Exception{
        return remoteService.editCaption(bo);
    }

    @Operation(summary =  "新建文件夹", description = "新建文件夹")
    @PostMapping("/materialMkdir")
    public Boolean materialMkdir(@RequestBody @Valid AppMaterialSaveMkdirApiBO saveMkdirApiBO) throws Exception{
        return remoteService.materialMkdir(saveMkdirApiBO);
    }

    @Operation(summary =  "移动文件资源", description = "移动文件资源")
    @PostMapping("/moveMaterial")
    public Boolean moveMaterial(@RequestBody  @Valid AppMaterialIOApiBO apiBO) throws Exception{
        return remoteService.moveMaterial(apiBO);
    }

    @Operation(summary =  "批量移动文件资源", description = "批量移动文件资源")
    @PostMapping("/batchMoveMaterial")
    public Boolean batchMoveMaterial(@RequestBody  @Validated AppMaterialBatchIOApiBO apiBO) throws Exception{
        return remoteService.batchMoveMaterial(apiBO);
    }

    @Operation(summary =  "复制文件资源", description = "复制文件资源")
    @PostMapping("/copyMaterial")
    public Boolean copyMaterial(@RequestBody  @Valid AppMaterialIOApiBO ioApiBO) throws Exception{
        return remoteService.copyMaterial(ioApiBO);
    }

    @Operation(summary =  "批量复制文件资源", description = "批量复制文件资源")
    @PostMapping("/batchCopyMaterial")
    public Boolean batchCopyMaterial(@RequestBody  @Validated AppMaterialBatchIOApiBO ioApiBO) throws Exception{
        return remoteService.batchCopyMaterial(ioApiBO);
    }

    @Operation(summary =  "查询该文件夹父节点列表", description = "查询该文件夹父节点列表")
    @PostMapping("/getMaterialParentsPath")
    public List<AppMaterialQueryApiDTO> getMaterialParentsPath(@RequestBody @Valid BaseIdBO bo) throws Exception{
        return remoteService.getMaterialParentsPath(bo);
    }

    @Operation(summary =  "删除素材", description = "删除素材")
    @PostMapping("/deleteMaterial")
    public Boolean deleteMaterial(@RequestBody @Valid CommonIdApiBO bo) throws Exception{
        return remoteService.deleteMaterial(bo);
    }

    @Operation(summary =  "批量删除素材", description = "批量删除素材")
    @PostMapping("/batchDeleteMaterial")
    public Boolean batchDeleteMaterial(@RequestBody @Validated AppMaterialBatchCommonIdApiBO bo) throws Exception{
        return remoteService.batchDeleteMaterial(bo);
    }
    @Operation(summary =  "查询文件已使用大小（kb）", description = "查询文件已使用大小（kb）")
    @PostMapping("/usedSize")
    public AppMaterialStatisticsSizeApiDTO usedSize() throws Exception{
        return remoteService.usedSize();
    }
}
