package com.dbj.classpal.admin.api.web.books;


import com.dbj.classpal.admin.service.remote.books.book.BooksRankClassifyRemoteService;
import com.dbj.classpal.books.client.bo.books.BooksRankClassifyBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoUpdForceApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 图书配置-图书内容分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Tag(name = "【图书】-册数内容分类管理接口", description = "【图书】-册数内容分类管理接口")
@RestController
@RequestMapping("/books-rank-classify")
public class BooksRankClassifyController {

    @Resource
    private BooksRankClassifyRemoteService booksRankClassifyRemoteService;

    @Operation(summary =  "查询册数下所有内容分类", description = "查询册数下所有内容分类")
    @PostMapping("/books/rank/classify/list")
    public List<BooksRankClassifyDTO> list(@Valid @RequestBody BooksRankClassifyBO boardBooksRankClassifyBO) throws BusinessException {
        return booksRankClassifyRemoteService.list(boardBooksRankClassifyBO);
    }

}
