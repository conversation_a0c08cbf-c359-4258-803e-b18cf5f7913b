package com.dbj.classpal.admin.api.web.sys.menu;


import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuSaveBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuTypeBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdIsRefreshBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuUpdVisibleBO;
import com.dbj.classpal.admin.common.dto.sys.menu.SysMenuDTO;
import com.dbj.classpal.admin.service.service.sys.menu.SysMenuService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Copyright (C), 2017-2020, cn.zlinks
 * FileName: SysMenuController
 * Date:     2024-2-2 13:36:06
 * Description: 控制层
 * <AUTHOR> Liu
 */
@RestController
@RequestMapping("/sys-menu")
@Tag(name = "菜单信息接口", description = "菜单相关操作")
public class SysMenuController {

	@Resource
	private SysMenuService sysMenuService;
	
	/**
	 * <AUTHOR>
	 * @Description 获取单个菜单数据
	 * @Date 2025/3/17 14:28
	 * @param id 主键id
	 * @return SysMenuDTO 菜单数据
	 **/
	@Operation(summary =  "获取单个菜单数据")
    @GetMapping(value = "/getSysMenuInfo")
    public SysMenuDTO getSysMenuInfo(@Parameter(description = "菜单id") @RequestParam Integer id) throws BusinessException {
        return this.sysMenuService.getSysMenuInfo(id);
    }

	/**
	 * <AUTHOR>
	 * @Description  添加单个数据
	 * @Date 2025/3/17 14:29
	 * @param bo 新增菜单信息
	 * @return Boolean 是否保存成功
	 **/
	@Operation(summary =  "添加单个数据")
	@PostMapping(value = "/saveSysMenu")
	public Boolean saveSysMenu(@Validated @RequestBody SysMenuSaveBO bo) throws BusinessException {
		return this.sysMenuService.saveSysMenu(bo);
		
	}
	
	/**
	 * <AUTHOR>
	 * @Description  修改菜单数据
	 * @Date 2025/3/17 14:29
	 * @param bo 修改的菜单信息
	 * @return Boolean 是否修改成功
	 **/
	@Operation(summary =  "修改菜单数据")
	@PostMapping(value = "/updateSysMenu")
	public Boolean updateSysMenu(@Validated @RequestBody SysMenuUpdBO bo) throws BusinessException {
		return this.sysMenuService.updateSysMenu(bo);
	}

	/**
	 * <AUTHOR>
	 * @Description  修改显示状态
	 * @Date 2025/3/17 14:31
	 * @param bo 是否心事入参
	 * @return Boolean 是否成功
	 **/
	@Operation(summary =  "修改显示状态")
	@PostMapping(value = "/updateSysMenuVisible")
	public Boolean updateSysMenuVisible(@Validated @RequestBody SysMenuUpdVisibleBO bo) throws BusinessException {
		return this.sysMenuService.updateSysMenuVisible(bo);
	}
	/**
	 * <AUTHOR>
	 * @Description  修改刷新状态
	 * @Date 2025/3/17 14:31
	 * @param bo 刷新状态入参
	 * @return Boolean 是否成功
	 **/
	@Operation(summary =  "修改刷新状态")
	@PostMapping(value = "/updateSysMenuIsRefresh")
	public Boolean updateSysMenuIsRefresh(@Validated @RequestBody SysMenuUpdIsRefreshBO bo) throws BusinessException {
		return this.sysMenuService.updateSysMenuIsRefresh(bo);
	}
	/**
	 * <AUTHOR>
	 * @Description  修改刷新状态
	 * @Date 2025/3/17 14:31
	 * @param bo 刷新状态入参
	 * @return Boolean 是否成功
	 **/
	@Operation(summary =  "删除菜单")
	@PostMapping(value = "/delSysMenu")
	public Boolean delSysMenu(@Validated @RequestBody BaseIdBO bo) throws BusinessException {
		return this.sysMenuService.delSysMenu(bo);
	}

	/**
	 *
	 * @Title: getSysMenuAll
	 * @Description: 获取所有的数据
	 * @return  SysMenuDTO 菜单数据
	 * @date: 2022年10月20日
	 */
	@Operation(summary =  "查询菜单")
	@GetMapping(value = "/sysMenuAll")
	public List<SysMenuDTO> getSysMenuAll(){
		return this.sysMenuService.getSysMenuAll();
	}
	/**
	 *
	 * @Title: getSysMenuAll
	 * @Description: 根据菜单类型获取菜单
	 * @return SysMenuDTO 菜单数据
	 * @date: 2022年10月20日
	 */
	@Operation(summary =  "根据菜单类型获取菜单")
	@GetMapping(value = "/getSysMenuByMenuType")
	public List<SysMenuDTO> getSysMenuByMenuType(){
		return this.sysMenuService.getSysMenuByMenuType();
	}


	/**
	 * 获取所有的数据
	 * @Title: getSysMenuAll
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @return
	 * @date: 2022年10月20日
	 * @throws
	 */
	@Operation(summary =  "获取用户权限视图")
	@GetMapping(value = "/getSysUserMenuAuth")
	public List<SysMenuDTO> getSysUserMenuAuth(@Parameter(description = "用户id") @RequestParam Integer userId) throws BusinessException {
		return this.sysMenuService.getSysUserMenuAuth(userId);
	}

}
