package com.dbj.classpal.admin.api.web.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.ebooks.AppEBookshelfRemoteService;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookshelfApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app-ebook-shelf")
@Tag(name = "电子样书-书架", description = "电子样书-单书")
public class AppEBookshelfController {

    @Resource
    private AppEBookshelfRemoteService appEBookshelfRemoteService;

    @Operation(summary = "查询书架列表", description = "查询书架列表")
    @PostMapping("/pageInfo")
    public Page<AppEBookshelfApiDTO> page(@RequestBody PageInfo<AppEBookshelfQueryApiBO> pageRequest) throws BusinessException{
        return appEBookshelfRemoteService.page(pageRequest);
    }

    @Operation(summary = "查看详情", description = "查看详情")
    @PostMapping("/detail")
    public AppEBookshelfApiDTO detail(@RequestBody @Valid AppEBookshelfIdApiBO idBO) throws BusinessException{
        return appEBookshelfRemoteService.detail(idBO);
    }

    @Operation(summary = "添加书架", description = "添加书架")
    @PostMapping("/save")
    public Integer save(@RequestBody @Valid AppEBookshelfSaveApiBO saveBO) throws BusinessException{
        return appEBookshelfRemoteService.save(saveBO);

    }

    @Operation(summary = "修改书架", description = "修改书架")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid AppEBookshelfUpdateApiBO saveBO) throws BusinessException{
        return appEBookshelfRemoteService.update(saveBO);
    }

    @Operation(summary = "删除书架", description = "删除书架")
    @PostMapping("/delete")
    public Boolean delete(@RequestBody @Valid AppEBookshelfIdApiBO idBO) throws BusinessException{
        return appEBookshelfRemoteService.delete(idBO);
    }

    @Operation(summary = "批量删除", description = "批量删除")
    @PostMapping("/deleteBatch")
    public Boolean deleteBatch(@RequestBody @Valid AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        return appEBookshelfRemoteService.deleteBatch(idsBO);
    }

    @Operation(summary = "批量启用", description = "批量启用")
    @PostMapping("/enableBatch")
    public Boolean enableBatch(@RequestBody @Valid AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        return appEBookshelfRemoteService.enableBatch(idsBO);
    }

    @Operation(summary = "批量禁用", description = "批量禁用")
    @PostMapping("/disableBatch")
    public Boolean disableBatch(@RequestBody @Valid AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        return appEBookshelfRemoteService.disableBatch(idsBO);
    }

    @Operation(summary = "批量允许下载", description = "批量允许下载")
    @PostMapping("/allowDownloadBatch")
    public Boolean allowDownloadBatch(@RequestBody @Valid AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        return appEBookshelfRemoteService.allowDownloadBatch(idsBO);
    }

    @Operation(summary = "批量禁用下载", description = "批量禁用下载")
    @PostMapping("/disableDownloadBatch")
    public Boolean disableDownloadBatch(@RequestBody @Valid AppEBookshelfIdsApiBO idsBO) throws BusinessException{
        return appEBookshelfRemoteService.disableDownloadBatch(idsBO);
    }
}
