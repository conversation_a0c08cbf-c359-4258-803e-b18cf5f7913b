package com.dbj.classpal.admin.api.web.books;


import com.dbj.classpal.admin.service.remote.books.book.BooksCategoryRemoteService;
import com.dbj.classpal.books.client.bo.books.BooksCategoryApiBO;
import com.dbj.classpal.books.client.dto.books.BooksCategoryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Tag(name = "【图书】-分类管理", description = "【图书】-分类管理")
@RestController
@RequestMapping("/books-category")
public class BooksCategoryController {


    @Resource
    private BooksCategoryRemoteService booksCategoryRemoteService;

    @Operation(summary =  "图书分类列表", description = "图书分类列表")
    @PostMapping("/books/category/list")
    public List<BooksCategoryApiDTO> list(@RequestBody BooksCategoryApiBO bookCategoryApiBO) throws BusinessException{
        return booksCategoryRemoteService.list(bookCategoryApiBO);
    }

    @Operation(summary =  "图书分类同步", description = "图书分类同步")
    @GetMapping("/books/category/sync")
    public Boolean sync() throws BusinessException{
        return booksCategoryRemoteService.sync();
    }
}
