package com.dbj.classpal.admin.api.web.poem;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.poem.AncientPoemApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemCopyBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemMoveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemUpsertBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 古诗文 前端控制器
 * </p>
 *
 * <AUTHOR> Yi
 * @since 2025-05-21
 */
@Tag(name = "古诗文", description = "古诗文相关接口")
@RestController
@RequestMapping("/ancient-poem")
public class AncientPoemController {

	@Resource
	private AncientPoemApi ancientPoemApi;

	@Operation(summary = "古诗文分页列表", description = "古诗文分页列表")
	@PostMapping("/getAncientPoemPage")
	public Page<AncientPoemDTO> getAncientPoemPage(@Validated @RequestBody PageInfo<AncientPoemPageBO> pageInfo) throws BusinessException {
		RestResponse<Page<AncientPoemDTO>> result = ancientPoemApi.getAncientPoemPage(pageInfo);
		return result.returnProcess(result);
	}

	@Operation(summary = "古诗文详情", description = "古诗文详情")
    @PostMapping(value = "/getAncientPoemInfo")
    public AncientPoemDTO getAncientPoemInfo(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
		RestResponse<AncientPoemDTO> result = ancientPoemApi.getAncientPoemInfo(bo);
		return result.returnProcess(result);
    }

	@Operation(summary = "保存古诗文", description = "保存古诗文")
	@PostMapping(value = "/saveAncientPoem")
	public Boolean saveAncientPoem(@Validated @RequestBody AncientPoemUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = ancientPoemApi.saveAncientPoem(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "编辑古诗文", description = "编辑古诗文")
	@PostMapping(value = "/updateAncientPoem")
	public Boolean updateAncientPoem(@Validated @RequestBody AncientPoemUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = ancientPoemApi.updateAncientPoem(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "删除古诗文", description = "删除古诗文")
	@PostMapping(value = "/deleteAncientPoem")
	public Boolean deleteAncientPoem(@Validated @RequestBody CommonIdsApiBO bo) throws BusinessException {
		RestResponse<Boolean> result = ancientPoemApi.deleteAncientPoem(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "复制古诗文", description = "复制古诗文")
	@PostMapping(value = "/copyAncientPoem")
	public Boolean copyAncientPoem(@Validated @RequestBody AncientPoemCopyBO bo) throws BusinessException {
		RestResponse<Boolean> result = ancientPoemApi.copyAncientPoem(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "移动古诗文", description = "移动古诗文")
	@PostMapping(value = "/moveAncientPoem")
	public Boolean moveAncientPoem(@Validated @RequestBody AncientPoemMoveBO bo) throws BusinessException {
		RestResponse<Boolean> result = ancientPoemApi.moveAncientPoem(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "古诗文引用", description = "古诗文引用")
	@PostMapping(value = "/getAncientPoemRelate")
	public List<AncientPoemRelateDTO> getAncientPoemRelate(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
		RestResponse<List<AncientPoemRelateDTO>> result = ancientPoemApi.getAncientPoemRelate(bo);
		return result.returnProcess(result);
	}
}
