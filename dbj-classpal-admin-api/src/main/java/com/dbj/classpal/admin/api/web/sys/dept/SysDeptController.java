package com.dbj.classpal.admin.api.web.sys.dept;


import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptBO;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dept.SysDeptUpdBO;
import com.dbj.classpal.admin.common.dto.sys.dept.SysDeptDTO;
import com.dbj.classpal.admin.service.service.sys.dept.ISysDeptService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@RestController
@RequestMapping("/sys-dept")
@Tag(name = "部门信息接口", description = "部门相关操作")
public class SysDeptController {



    @Resource
    private ISysDeptService sysDeptService;

    /**
     * 获取单个SysDept数据
     * @Title: saveSysDept
     * @Description: 添加SysDept数据
     * @param reqBo 入参
     * @return SysDeptDTO 返回的部门数据
     * @date: 2022年10月20日
     */
    @Operation(summary =  "获取单个部门数据")
    @PostMapping(value = "/getSysDeptInfo")
    public SysDeptDTO getSysDeptInfo(@Validated @RequestBody SysDeptBO reqBo) throws BusinessException {
        return this.sysDeptService.getSysDeptInfo(reqBo);
    }

    /**
     * 添加SysDept数据
     * @Title: saveSysDept
     * @Description: 添加SysDept数据
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "添加SysDept数据")
    @PostMapping(value = "/saveSysDept")
    public Boolean saveSysDept(@Validated @RequestBody SysDeptSaveBO bo) throws BusinessException {
        return this.sysDeptService.saveSysDept(bo);
    }

    /**
     * 修改数据
     * @Title: UpdateSysDept
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "修改数据")
    @PostMapping(value = "/updateSysDept")
    public Boolean updateSysDept(@Validated @RequestBody SysDeptUpdBO bo) throws BusinessException {
        return this.sysDeptService.updateSysDept(bo);
    }


    /**
     * 删除部门数据
     * @Title: delSysDeptInfo
     * @Description: 添加SysDept数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "删除部门数据")
    @PostMapping(value = "/delSysDeptInfo")
    public Boolean delSysDeptInfo(@Validated @RequestBody SysDeptBO reqBo) throws BusinessException {
        return this.sysDeptService.delSysDeptInfo(reqBo);
    }

    /**
     * 获取所有部门
     * @Title: getSysDeptAll
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "获取所有的部门数")
    @PostMapping(value = "/listSysDept")
    public List<SysDeptDTO> listSysDept(){
        return this.sysDeptService.listSysDept();
    }

    /**
     * 获取所有的数据
     * @Title: getSysDeptAll
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "获取所有的部门数")
    @PostMapping(value = "/getSysDeptAll")
    public List<SysDeptDTO> getSysDeptAll(){
        return this.sysDeptService.getSysDeptAll();
    }
    /**
     * 获取部门人员树
     * @Title: getSysDeptAll
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "获取部门人员树")
    @PostMapping(value = "/getSysDeptUserTree")
    public List<SysDeptDTO> getSysDeptUserTree(){
        return this.sysDeptService.getSysDeptUserTree();
    }

}
