package com.dbj.classpal.admin.api.web.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appevaluation.AppEvaluationRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.*;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationDetailQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationController
 * Date:     2025-05-16 16:14:22
 * Description: 表名： ,描述： 表
 */
@Tag(name = "内容管理-评测", description = "内容管理-评测")
@RestController
@RequestMapping("/evaluation")
public class AppEvaluationController {
    @Resource
    private AppEvaluationRemoteService appEvaluationRemoteService;

    @Operation(summary = "分页查询评测表列表", description = "分页查询评测表列表")
    @PostMapping("/pageInfo")
    public Page<AdminEvaluationQueryApiDTO> pageInfo(@Validated @RequestBody PageInfo<AdminEvaluationQueryApiBO> pageInfo) throws BusinessException {
        return appEvaluationRemoteService.pageInfo(pageInfo);
    }

    @Operation(summary = "查询评测表列表", description = "查询评测表列表")
    @PostMapping("/list")
    public List<AdminEvaluationQueryApiDTO> list() throws BusinessException {
        return appEvaluationRemoteService.list();
    }

    @Operation(summary = "新增评测表", description = "新增评测表")
    @PostMapping("/saveAppEvaluation")
    public Boolean saveAppEvaluation(@Validated @RequestBody AdminEvaluationSaveApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.saveAppEvaluation(bo);
    }

    @Operation(summary = "删除评测表", description = "删除评测表")
    @PostMapping("/deleteAppEvaluation")
    public Boolean deleteAppEvaluation(@Validated @RequestBody CommonIdsApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.deleteAppEvaluation(bo);
    }

    @Operation(summary = "查看评测表详情", description = "查看评测表详情")
    @PostMapping("/getDetail")
    public AdminEvaluationDetailQueryApiDTO getDetail(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
       return appEvaluationRemoteService.getDetail(bo);
    }

    @Operation(summary = "重命名评测表", description = "重命名评测表")
    @PostMapping("/reName")
    public Boolean reName(@Validated @RequestBody AdminEvaluationReNameApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.reName(bo);
    }

    @Operation(summary = "修改评测表封面", description = "修改评测表封面")
    @PostMapping("/reCover")
    public Boolean reCover(@Validated @RequestBody AdminEvaluationReCoverApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.reCover(bo);
    }

    @Operation(summary = "修改评测表简介", description = "修改评测表简介")
    @PostMapping("/reRemark")
    public Boolean reRemark(@Validated @RequestBody AdminEvaluationReRemarkApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.reRemark(bo);
    }

    @Operation(summary = "修改评测表上架状态", description = "修改评测表上架状态")
    @PostMapping("/updateStatus")
    public Boolean updateStatus(@Validated @RequestBody AdminEvaluationUpdateStatusApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.updateStatus(bo);
    }

    @Operation(summary = "修改评测表隐藏状态", description = "修改评测表隐藏状态")
    @PostMapping("/updateVisible")
    public Boolean updateVisible(@Validated @RequestBody AdminEvaluationUpdateVisibleApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.updateVisible(bo);
    }

    @Operation(summary = "启用|禁用评测表", description = "修改评测表隐藏状态")
    @PostMapping("/updateOpen")
    public Boolean updateOpen(@Validated @RequestBody AdminEvaluationUpdateOpenApiBO bo) throws BusinessException {
        return appEvaluationRemoteService.updateOpen(bo);
    }
}
