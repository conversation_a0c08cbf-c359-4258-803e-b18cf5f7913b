package com.dbj.classpal.admin.api.web.material;

import com.dbj.classpal.admin.service.remote.appmaterial.AppMaterialBusinessRefRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefQueryCommonApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefReNameApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.books.client.dto.material.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialController
 * Date:     2025-04-08 16:33:18
 * Description: 表名： ,描述： 表
 */
@Tag(name = "素材中心-素材业务关联")
@RestController
@RequestMapping("/api/materialBusinessRef")
public class AppMaterialBusinessRefController {

    @Resource
    private AppMaterialBusinessRefRemoteService remoteService;

    @Operation(summary =  "查询被引用素材列表", description = "查询被引用素材列表")
    @PostMapping("/beRefBusinessList")
    public List<AppMaterialBusinessRefMaterialQueryApiDTO> beRefBusinessList(@RequestBody @Valid AppMaterialBusinessRefQueryCommonApiBO bo) throws Exception{
        return remoteService.beRefBusinessList(bo);
    }

    @Operation(summary =  "查询引用关系列表", description = "查询引用关系列表")
    @PostMapping("/refBusinessList")
    public List<AppMaterialBusinessRefMaterialQueryApiDTO> refBusinessList(@RequestBody @Valid AppMaterialBusinessRefQueryCommonApiBO bo) throws Exception{
        return remoteService.refBusinessList(bo);
    }

    @Operation(summary =  "查询引用列表下各素材类型数量统计", description = "查询引用列表下各素材类型数量统计")
    @PostMapping("/getMaterialBusinessRefTypeCount")
    public List<AppMaterialBusinessRefTypeCountApiDTO> getMaterialBusinessRefTypeCount(@RequestBody @Valid CommonIdApiBO bo) throws Exception{
        return remoteService.getMaterialBusinessRefTypeCount(bo);
    }

    @Operation(summary =  "查看引用-内容管理-专辑", description = "查看引用-内容管理-专辑")
    @PostMapping("/getAppMaterialBusinessRefAlbum")
    public AppMaterialBusinessRefAlbumDirectApiDTO getAppMaterialBusinessRefAlbum(@RequestBody @Valid CommonIdApiBO bo) throws Exception{
        return remoteService.getAppMaterialBusinessRefAlbum(bo);
    }

    @Operation(summary =  "查看引用-图书管理-图书资源", description = "查看引用-图书管理-图书资源")
    @PostMapping("/getAppMaterialBusinessRefBooks")
    public BooksRefDirectApiDTO getAppMaterialBusinessRefBooks(@RequestBody @Valid CommonIdApiBO bo) throws Exception{
        return remoteService.getAppMaterialBusinessRefBooks(bo);
    }

    @Operation(summary =  "查看引用-图书管理-图书题库", description = "查看引用-图书管理-图书题库")
    @PostMapping("/getAppMaterialBusinessRefQuestion")
    public AppMaterialBusinessRefQuestionDirectApiDTO getAppMaterialBusinessRefQuestion(@RequestBody @Valid CommonIdApiBO bo) throws Exception{
        return remoteService.getAppMaterialBusinessRefQuestion(bo);
    }

    @Operation(summary =  "保存资源关联关系", description = "保存资源关联关系")
    @PostMapping("/saveAppMaterialBusinessRef")
    public Boolean saveAppMaterialBusinessRef(@RequestBody @Valid AppMaterialBusinessRefSaveApiBO bo) throws Exception{
        return remoteService.saveAppMaterialBusinessRef(bo);
    }

    @Operation(summary =  "关联资源关系重命名", description = "资源关联关系重命名")
    @PostMapping("/reNameAppMaterialBusinessRef")
    public Boolean reNameAppMaterialBusinessRef(@RequestBody @Valid AppMaterialBusinessRefReNameApiBO bo) throws Exception{
        return remoteService.reNameAppMaterialBusinessRef(bo);
    }

    @Operation(summary =  "移除关联资源关系", description = "移除关联资源关系")
    @PostMapping("/removeAppMaterialBusinessRef")
    public Boolean removeAppMaterialBusinessRef(@RequestBody @Valid CommonIdApiBO bo) throws Exception{
        return remoteService.removeAppMaterialBusinessRef(bo);
    }

    @Operation(summary =  "批量移除关联资源关系", description = "批量移除关联资源关系")
    @PostMapping("/removeBatchAppMaterialBusinessRef")
    public Boolean removeBatchAppMaterialBusinessRef(@RequestBody @Valid CommonIdsApiBO bo) throws Exception{
        return remoteService.removeBatchAppMaterialBusinessRef(bo);
    }

    @Operation(summary =  "关联资源关系排序", description = "关联资源关系排序")
    @PostMapping("/changeAppMaterialBusinessRefOrderNum")
    public Boolean changeAppMaterialBusinessRefOrderNum(@RequestBody @Valid CommonIdsApiBO bo) throws Exception{
        return remoteService.changeAppMaterialBusinessRefOrderNum(bo);
    }
}
