package com.dbj.classpal.admin.api.web.poem;


import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.BaseSortBO;
import com.dbj.classpal.admin.service.remote.poem.AncientPoemReciteCategoryRemoteService;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategoryUpdateBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 古诗文背诵分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Tag(name = "古诗背诵-古诗背诵分类管理")
@RestController
@RequestMapping("/ancient-poem-recite-category")
public class AncientPoemReciteCategoryController {

    @Resource
    private AncientPoemReciteCategoryRemoteService ancientPoemReciteCategoryRemoteService;

    @Operation(summary =  "分类查询", description = "分类查询")
    @PostMapping("/listAncientPoemReciteCategory")
    public List<AncientPoemReciteCategoryDTO> listAncientPoemReciteCategory() throws BusinessException {
        return ancientPoemReciteCategoryRemoteService.listAncientPoemReciteCategory();
    }

    @Operation(summary =  "获取分类详情", description = "获取分类详情")
    @GetMapping("/getAncientPoemReciteCategory")
    public AncientPoemReciteCategoryDetailDTO getAncientPoemReciteCategory(@RequestParam Integer id) throws BusinessException{
        return ancientPoemReciteCategoryRemoteService.getAncientPoemReciteCategory(id);
    }

    @Operation(summary =  "保存分类", description = "保存分类")
    @PostMapping("/save")
    public Boolean save(@RequestBody AncientPoemReciteCategorySaveBO ancientPoemReciteCategorySaveBO) throws BusinessException {
        return ancientPoemReciteCategoryRemoteService.save(ancientPoemReciteCategorySaveBO);
    }
    @Operation(summary =  "修改分类", description = "修改分类")
    @PostMapping("/update")
    public Boolean update(@RequestBody AncientPoemReciteCategoryUpdateBO ancientPoemReciteCategoryUpdateBO) throws BusinessException{
        return ancientPoemReciteCategoryRemoteService.update(ancientPoemReciteCategoryUpdateBO);
    }

    @Operation(summary =  "删除分类", description = "删除分类")
    @PostMapping("/delete")
    public Boolean delete(@RequestBody BaseIdBO baseIdBO) throws BusinessException{
        return ancientPoemReciteCategoryRemoteService.delete(baseIdBO.getId());
    }

    @Operation(summary =  "排序", description = "排序")
    @PostMapping("/sort")
    public Boolean sort(@RequestBody BaseSortBO<AncientPoemReciteCategorySortBO> sortBO) throws BusinessException{
        return ancientPoemReciteCategoryRemoteService.sort(sortBO.getSorts());
    }

}
