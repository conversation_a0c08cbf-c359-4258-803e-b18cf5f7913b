package com.dbj.classpal.admin.api.web.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appgray.AppFeatureRemoteService;
import com.dbj.classpal.app.client.bo.gray.FeatureEditApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureIdApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureIdsApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureQueryApiBO;
import com.dbj.classpal.app.client.bo.gray.FeatureSaveApiBO;
import com.dbj.classpal.app.client.dto.gray.FeatureDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "灰度管理")
@RestController
@RequestMapping("/api/feature")
public class AppFeatureController {

    @Resource
    private AppFeatureRemoteService featureService;

    @Operation(summary = "分页查询功能")
    @PostMapping("/page")
    public Page<FeatureDetailApiDTO> pageFeatures(@RequestBody @Valid PageInfo<FeatureQueryApiBO> pageRequest) throws BusinessException {
        return featureService.pageFeatures(pageRequest);
    }
    
    @Operation(summary = "保存功能")
    @PostMapping("/save")
    public void saveFeature(@RequestBody @Valid FeatureSaveApiBO feature) throws BusinessException {
        featureService.saveFeature(feature);
    }


    @Operation(summary = "更新功能")
    @PostMapping("/edit")
    public void editFeature(@RequestBody @Valid FeatureEditApiBO feature) throws BusinessException {
        featureService.editFeature(feature);
    }


    @Operation(summary = "获取功能详情")
    @PostMapping("/detail")
    public FeatureDetailApiDTO getFeatureDetail(@RequestBody FeatureIdApiBO featureIdApiBO) throws BusinessException {
        return featureService.getFeatureDetail(featureIdApiBO);
    }

    
    @Operation(summary = "批量启用")
    @PostMapping("/enable")
    public void batchEnable(@RequestBody FeatureIdsApiBO featureIds) throws BusinessException {
        featureService.batchEnable(featureIds);
    }
    
    @Operation(summary = "批量禁用")
    @PostMapping("/disable")
    public void batchDisable(@RequestBody FeatureIdsApiBO featureIds) throws BusinessException {
        featureService.batchDisable(featureIds);
    }
    
    @Operation(summary = "批量删除")
    @PostMapping("/delete")
    public void batchDelete(@RequestBody FeatureIdsApiBO featureIds) throws BusinessException {
        featureService.batchDelete(featureIds);
    }
} 