package com.dbj.classpal.admin.api.web.pinyin;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pinyin.PinyinApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinDelBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinStatusUpdateBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 拼音 前端控制器
 * </p>
 *
 * <AUTHOR> Yi
 * @since 2025-05-21
 */
@Tag(name = "拼音", description = "拼音相关接口")
@RestController
@RequestMapping("/pinyin")
public class PinyinController {

	@Resource
	private PinyinApi pinyinApi;

	@Operation(summary = "拼音列表", description = "拼音列表")
	@PostMapping("/getPinyinPage")
	public Page<PinyinDTO> getPinyinPage(@Validated @RequestBody PageInfo<PinyinPageBO> pageInfo) throws BusinessException {
		RestResponse<Page<PinyinDTO>> result = pinyinApi.getPinyinPage(pageInfo);
		return result.returnProcess(result);
	}

	@Operation(summary = "拼音详情", description = "拼音详情")
    @PostMapping(value = "/getPinyinInfo")
    public PinyinDTO getPinyinInfo(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
		RestResponse<PinyinDTO> result = pinyinApi.getPinyinInfo(bo);
		return result.returnProcess(result);
    }

	@Operation(summary = "保存拼音", description = "保存拼音")
	@PostMapping(value = "/savePinyin")
	public Boolean savePinyin(@Validated @RequestBody PinyinUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinApi.savePinyin(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "编辑拼音", description = "编辑拼音")
	@PostMapping(value = "/updatePinyin")
	public Boolean updatePinyin(@Validated @RequestBody PinyinUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinApi.updatePinyin(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "删除拼音", description = "删除拼音")
	@PostMapping(value = "/deletePinyin")
	public Boolean deletePinyin(@Validated @RequestBody PinyinDelBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinApi.deletePinyin(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "拼音启用/禁用", description = "拼音启用/禁用")
	@PostMapping("/updatePinyinStatus")
	public Boolean updatePinyinStatus(@Validated @RequestBody PinyinStatusUpdateBO bo) throws BusinessException {
		RestResponse<Boolean> result = pinyinApi.updatePinyinStatus(bo);
		return result.returnProcess(result);
	}
}
