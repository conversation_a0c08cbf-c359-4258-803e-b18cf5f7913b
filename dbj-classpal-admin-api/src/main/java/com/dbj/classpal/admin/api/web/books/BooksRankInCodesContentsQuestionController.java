package com.dbj.classpal.admin.api.web.books;


import com.dbj.classpal.admin.service.remote.books.book.BooksRankInCodesContentsQuestionRemoteService;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionDetailBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionSavaBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionUpdateBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 图书书内码题库表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Tag(name = "【图书】-书内码内容关联题库管理", description = "【图书】-书内码内容关联题库管理")
@RestController
@RequestMapping("/books-rank-in-codes-contents-question")
public class BooksRankInCodesContentsQuestionController {

    @Resource
    private BooksRankInCodesContentsQuestionRemoteService booksRankInCodesContentsQuestionRemoteService;

    @Operation(summary =  "查询书内码关联题库详情", description = "查询书内码关联题库详情")
    @GetMapping("/books/rank/in/code/contents/question/details")
    public BooksRankInCodesContentsQuestionDetailBO details(@RequestParam Integer inCodesContentsId) throws BusinessException{
        return booksRankInCodesContentsQuestionRemoteService.details(inCodesContentsId);
    }

    @Operation(summary =  "保存书内码关联题库", description = "保存书内码关联题库")
    @PostMapping("/books/rank/in/code/contents/question/save")
    public Boolean save(@RequestBody BooksRankInCodesContentsQuestionSavaBO saveBO) throws BusinessException{
        return booksRankInCodesContentsQuestionRemoteService.save(saveBO);

    }

    @Operation(summary =  "修改书内码关联题库", description = "修改书内码关联题库")
    @PostMapping("/books/rank/in/code/contents/question/update")
    public Boolean update(@RequestBody BooksRankInCodesContentsQuestionUpdateBO saveBO) throws BusinessException{
        return booksRankInCodesContentsQuestionRemoteService.update(saveBO);
    }
}
