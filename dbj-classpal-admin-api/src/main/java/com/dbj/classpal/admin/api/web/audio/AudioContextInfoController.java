package com.dbj.classpal.admin.api.web.audio;


import com.dbj.classpal.admin.service.remote.audio.AudioContextInfoRemoteService;
import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioContextInfoListDTO;
import com.dbj.classpal.books.client.dto.audio.AudioSynthesizerTaskInfoDTO;
import com.dbj.classpal.books.client.dto.audio.AudioTrialUseDTO;
import com.dbj.classpal.books.client.dto.audio.SynthesisResultDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 音频文本详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Tag(name = "新增音频-语音合成", description = "新增音频-语音合成管理")
@RestController
@RequestMapping("/audio-context-info")
public class AudioContextInfoController {

    @Autowired
    private AudioContextInfoRemoteService audioContextInfoRemoteService;

    @Operation(summary = "保存音频文本", description = "保存音频文本")
    @PostMapping("/saveContext")
    public Integer save(@Valid @RequestBody AudioContextInfoBO bo) throws BusinessException {
        return audioContextInfoRemoteService.saveContext(bo);
    }

    @Operation(summary = "查询音频文本列表", description = "查询音频文本列表")
    @PostMapping("/getContextInfoList")
    public AudioContextInfoListDTO getContextInfoList(@Valid @RequestBody AudioIntroIdBO bo) throws BusinessException {
        return audioContextInfoRemoteService.getContextInfoList(bo);
    }

    @Operation(summary = "取消", description = "取消音频合成")
    @PostMapping("/cancel")
    public Integer cancel(@Valid @RequestBody AudioIntroIdBO bo) throws BusinessException {
        return audioContextInfoRemoteService.cancel(bo);
    }

    @Operation(summary = "合成", description = "音频合成")
    @PostMapping("/synthesis")
    public Integer synthesis(@Valid @RequestBody AudioContextInfoBO bo) throws BusinessException {
        return audioContextInfoRemoteService.synthesis(bo);
    }

    @Operation(summary = "立即/重新合成", description = "立即/重新合成音频")
    @PostMapping("/resynthesis")
    public Integer resynthesis(@Valid @RequestBody AudioIntroIdBO bo) throws BusinessException {
        return audioContextInfoRemoteService.resynthesis(bo);
    }

    @Operation(summary = "查询合成状态", description = "查询音频合成状态")
    @PostMapping("/getSynthesizeStatus")
    public SynthesisResultDTO getSynthesizeStatus(@Valid @RequestBody AudioIntroIdBO bo) throws BusinessException {
        return audioContextInfoRemoteService.getSynthesizeStatus(bo);
    }

    @Operation(summary = "合成试听音频", description = "合成试听音频")
    @PostMapping("/submitSynthesis")
    public AudioSynthesizerTaskInfoDTO submitSynthesis(@RequestBody AudioTrialUseBO bo) throws BusinessException {
        return audioContextInfoRemoteService.submitSynthesis(bo);
    }

    @Operation(summary = "查询试听任务结果", description = "查询试听任务结果")
    @PostMapping("/getTaskInfo")
    public AudioSynthesizerTaskInfoDTO getTaskInfo(@RequestBody AudioTaskBO bo) throws BusinessException {
        return audioContextInfoRemoteService.getTaskInfo(bo);
    }


}
