package com.dbj.classpal.admin.api.web.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.config.BasicConfigRemoteService;
import com.dbj.classpal.books.client.bo.config.BasicConfigApiBO;
import com.dbj.classpal.books.client.bo.config.BasicConfigIdApiBO;
import com.dbj.classpal.books.client.bo.config.BasicConfigIdsApiBO;
import com.dbj.classpal.books.client.bo.config.BasicConfigQueryApiBO;
import com.dbj.classpal.books.client.dto.config.BasicConfigApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/04/18 09:52:42
 */
@RestController
@RequestMapping("/basic-config")
@Tag(name = "通用配置", description = "通用配置")
public class BasicConfigController {
    @Resource
    BasicConfigRemoteService configRemoteService;
    /**
     * 创建内容配置
     */
    @Operation(summary = "创建内容配置",description = "创建内容配置")
    @PostMapping("/create")
    Integer create(@RequestBody BasicConfigApiBO apiBO) throws BusinessException{
        return configRemoteService.create(apiBO);
    }

    /**
     * 更新内容配置
     */
    @Operation(summary = "更新内容配置",description = "更新内容配置")
    @PostMapping("/update")
    Void update(@RequestBody BasicConfigApiBO apiBO) throws BusinessException{
        return configRemoteService.update(apiBO);
    }

    /**
     * 更新内容配置
     */
    @Operation(summary = "更新内容配置",description = "删除内容配置")
    @PostMapping("/batch/delete")
    Void batchDelete(@RequestBody BasicConfigIdsApiBO idsApiBO) throws BusinessException{
        return configRemoteService.batchDelete(idsApiBO);
    }

    /**
     * 获取内容配置详情
     */
    @Operation(summary = "获取内容配置详情",description = "获取内容配置详情")
    @GetMapping("/detail")
    BasicConfigApiDTO detail(@RequestBody BasicConfigIdApiBO idApiBO) throws BusinessException {
        return configRemoteService.detail(idApiBO);
    }

    /**
     * 获取内容配置分页列表
     */
    @Operation(summary = "获取内容配置分页列表",description = "获取内容配置分页列表")
    @PostMapping("/page")
    Page<BasicConfigApiDTO> pageList(@RequestBody PageInfo<BasicConfigQueryApiBO> queryApiBO) throws BusinessException {
        return configRemoteService.pageList(queryApiBO);
    }

    /**
     * 获取内容配置列表
     */
    @Operation(summary = "获取内容配置列表",description = "获取内容配置列表")
    @PostMapping("/list")
    List<BasicConfigApiDTO> list(@RequestBody BasicConfigQueryApiBO queryApiBO) throws BusinessException {
        return configRemoteService.list(queryApiBO);
    }
}
