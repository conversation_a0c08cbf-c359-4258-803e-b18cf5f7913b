package com.dbj.classpal.admin.api.web.file;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dbj.classpal.admin.common.constant.AdminErrorCode;
import com.dbj.classpal.admin.common.constant.ConstantRedis;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.oss.config.OssTeamConfig;
import com.dbj.classpal.framework.oss.dto.StsTokenDTO;
import com.dbj.classpal.framework.oss.utils.OssUtil;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2022/10/26
 */
@Tag(name = "oss文件上传接口", description = "oss文件上传接口")
@RestController
@RequestMapping("/file-oss")
@Slf4j
public class FileOssController {

    @Resource
    private RedissonRedisUtils redisUtils;

    @Resource
    private OssUtil ossUtil;
    final static String BUCKET_NAME = "dbj-dev";
    final static String OSS_ACCESS_URL = "https://cdn.xiaoliuban.com/";
    /**
     * 获取oss临时token
     *
     * @return StsTokenDTO
     */
    @Operation(summary =  "获取oss临时token", description = "获取oss临时token")
    @PostMapping("getStsToken")
    public StsTokenDTO getStsToken() {
        String stsToken = redisUtils.getValue(ConstantRedis.OSS_TOKEN);
        if (stsToken != null) {
            return JSON.parseObject(stsToken, StsTokenDTO.class);
        }
        StsTokenDTO stsTokenDTO = ossUtil.getStsTokenDTO();
        if (stsTokenDTO != null) {
            redisUtils.setValue(ConstantRedis.OSS_TOKEN, JSONObject.toJSON(stsTokenDTO).toString(), 18, TimeUnit.MINUTES);
        }
        return stsTokenDTO;
    }

    /**
     * <AUTHOR>
     * @Description  获取文件大小
     * @Date 2025/3/20 11:14 
     * @param url 文件路径
     * @return Long 文件大小
     **/
    @Operation(summary =  "获取文件大小", description = "获取文件大小")
    @GetMapping("getFileSize")
    public Long getFileSize(@Parameter(description = "文件路径") @RequestParam String url) throws BusinessException {
        String key = url.replace(OSS_ACCESS_URL, "");
        Long fileSize = 0l;
        try {
            fileSize = ossUtil.getFileSize(BUCKET_NAME, key);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(AdminErrorCode.OSS_FILE_NOT_EXIST_CODE,AdminErrorCode.OSS_FILE_NOT_EXIST_MSG);
        }
        return fileSize;
    }

}

