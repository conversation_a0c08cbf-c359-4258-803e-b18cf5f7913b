package com.dbj.classpal.admin.api.web.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appgray.AppFeatureWhitelistRemoteService;
import com.dbj.classpal.app.client.bo.gray.WhitelistBatchSaveApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistIdsApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistQueryApiBO;
import com.dbj.classpal.app.client.bo.gray.WhitelistSaveApiBO;
import com.dbj.classpal.app.client.dto.gray.WhitelistDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * APP功能灰度白名单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "灰度发布-白名单管理")
@RestController
@RequestMapping("/api/feature/whitelist")
public class AppFeatureWhitelistController {

    @Resource
    private AppFeatureWhitelistRemoteService whitelistService;

    @Operation(summary = "分页查询白名单")
    @PostMapping("/page")
    public Page<WhitelistDetailApiDTO> pageWhitelist(@RequestBody @Valid PageInfo<WhitelistQueryApiBO> pageRequest) throws BusinessException {
        return whitelistService.pageWhitelists(pageRequest);
    }
    
    @Operation(summary = "添加白名单")
    @PostMapping("/add")
    public void addWhitelist(@RequestBody @Valid WhitelistSaveApiBO whitelist) throws BusinessException {
        whitelistService.saveWhitelist(whitelist);
    }
    
    @Operation(summary = "批量添加白名单")
    @PostMapping("/batch/add")
    public void batchAddWhitelist(@RequestBody @Valid WhitelistBatchSaveApiBO whitelists) throws BusinessException {
        whitelistService.batchSaveWhitelists(whitelists);
    }

    
    @Operation(summary = "批量删除白名单")
    @PostMapping("/batch/delete")
    public void batchDeleteWhitelist(@RequestBody WhitelistIdsApiBO ids) throws BusinessException {
        whitelistService.batchDelete(ids);
    }
}
