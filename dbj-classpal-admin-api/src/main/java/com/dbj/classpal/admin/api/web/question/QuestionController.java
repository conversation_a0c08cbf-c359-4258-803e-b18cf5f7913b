package com.dbj.classpal.admin.api.web.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.books.question.AppQuestionRemoteService;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionBlankContentApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/04/18 09:52:42
 */
@RestController
@RequestMapping("/question")
@Tag(name = "题库接口", description = "题库接口")
public class QuestionController {
    @Resource
    AppQuestionRemoteService questionRemoteService;
    /**
     * 分页查询题目列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询题目列表",description = "分页查询题目列表")
    Page<QuestionApiDTO> pageList(@RequestBody @Valid  PageInfo<QuestionPageApiBO> pageApiBO) throws BusinessException {
        return questionRemoteService.pageList(pageApiBO);
    }

    /**
     * 获取题目详情
     */
    @PostMapping("/detail")
    @Operation(summary = "获取题目详情",description = "获取题目详情")
    QuestionApiDTO getQuestion(@RequestBody @Valid  QuestionIdApiBO idApiBO) throws BusinessException {
        return questionRemoteService.getQuestion(idApiBO);
    }

    /**
     * 根据分类ID获取题目列表
     */
    @Operation(summary = "根据分类ID获取题目列表",description = "根据分类ID获取题目列表")
    @PostMapping("/category/question/list")
    List<QuestionApiDTO> getQuestionList(@RequestBody @Valid  QuestionCategoryIdQueryApiBO idApiBO) throws BusinessException {
        return questionRemoteService.getQuestionList(idApiBO);
    }

    /**
     * 创建题目
     */
    @Operation(summary = "创建题目",description = "创建题目")
    @PostMapping("/create")
    Integer createQuestion(@RequestBody @Valid  QuestionSaveApiBO question) throws BusinessException {
        return questionRemoteService.createQuestion(question);
    }

    /**
     * 更新题目
     */
    @Operation(summary = "更新题目",description = "更新题目")
    @PostMapping("/edit")
    Void updateQuestion( @RequestBody @Valid  QuestionEditApiBO question) throws BusinessException {
        return questionRemoteService.updateQuestion(question);
    }

    /**
     * 删除题目
     */
    @PostMapping("/batch/delete")
    @Operation(summary = "删除题目",description = "删除题目")
    Void batchDeleteQuestion(@RequestBody @Valid  QuestionIdsApiBO idsApiBO) throws BusinessException {
        return questionRemoteService.batchDeleteQuestion(idsApiBO);
    }

    /**
     * 获取题目正确答案
     */
    @Operation(summary = "获取题目正确答案",description = "获取题目正确答案")
    @PostMapping("/answer/get")
    List<String> getQuestionAnswer(@RequestBody @Valid  QuestionIdApiBO idApiBO) throws BusinessException {
        return questionRemoteService.getQuestionAnswer(idApiBO);
    }

    /**
     * 获取完形填空题内容
     */
    @Operation(summary = "获取完形填空题内容",description = "获取完形填空题内容")
    @PostMapping("/blank-content/get")
    QuestionBlankContentApiDTO getQuestionBlankContent(@RequestBody @Valid  QuestionIdApiBO idApiBO) throws BusinessException {
        return questionRemoteService.getQuestionBlankContent(idApiBO);
    }

    /**
     * 批量复制题目
     */
    @Operation(summary = "批量复制题目",description = "批量复制题目")
    @PostMapping("/batch/copy")
    Void batchCopyQuestion(@RequestBody @Valid  QuestionCopyApiBO copyApiBO) throws BusinessException {
        return questionRemoteService.batchCopyQuestion(copyApiBO);
    }

    /**
     * 批量移动题目
     */
    @Operation(summary = "批量移动题目",description = "批量移动题目")
    @PostMapping("/batch/move")
    Void batchMoveQuestion(@RequestBody @Valid  QuestionMoveApiBO moveApiBO) throws BusinessException {
        return questionRemoteService.batchMoveQuestion(moveApiBO);
    }

}
