package com.dbj.classpal.admin.api.web.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appalbum.AppAlbumElementsRemoteService;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumController
 * Date:     2025-04-15 14:31:31
 * Description: 表名： ,描述： 表
 */
@RestController
@RequestMapping("/api/albumElements")
@Tag(name = "内容管理-专辑管理-专辑")
public class AppAlbumElementsController {
    @Resource
    private AppAlbumElementsRemoteService service;

    @Operation(summary = "专辑-列表查询")
    @PostMapping("/getAppAlbumElementsList")
    public List<AppAlbumElementsQueryApiDTO> getAppAlbumElementsList(@RequestBody @Validated AppAlbumElementsQueryApiBO bo) throws BusinessException {
        return service.getAppAlbumElementsList(bo);
    }

    @Operation(summary = "专辑-分页查询")
    @PostMapping("/pageAlbumElements")
    public Page<AppAlbumElementsQueryApiDTO> pageAlbumElements(@RequestBody @Validated PageInfo<AppAlbumElementsQueryApiBO> bo) throws BusinessException {
        return service.pageAlbumElements(bo);
    }

    @Operation(summary = "专辑-单个查询")
    @PostMapping("/getAppAlbumElement")
    public AppAlbumElementsQueryApiDTO getAppAlbumElement(@RequestBody @Validated CommonIdApiBO bo) throws BusinessException {
        return service.getAppAlbumElement(bo);
    }

    @Operation(summary = "专辑-新增专辑")
    @PostMapping("/saveAlbumElements")
    public Boolean saveAlbumElements(@RequestBody @Valid AppAlbumElementsSaveApiBO bo) throws BusinessException {
        return service.saveAlbumElements(bo);
    }

    @Operation(summary = "专辑-修改专辑")
    @PostMapping("/updateAlbumElements")
    public Boolean updateAlbumElements(@RequestBody @Valid AppAlbumElementsUpdateApiBO bo) throws BusinessException {
        return service.updateAlbumElements(bo);
    }

    @Operation(summary = "专辑-删除专辑")
    @PostMapping("/deleteAppAlbumElements")
    public Boolean deleteAppAlbumElements(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException {
        return service.deleteAppAlbumElements(bo);
    }

    @Operation(summary = "专辑-修改专辑封面")
    @PostMapping("/updateAppAlbumElementCover")
    public Boolean updateAppAlbumElementCover(@RequestBody @Valid AppAlbumElementsUpdateCoverApiBO bo) throws BusinessException {
        return service.updateAppAlbumElementCover(bo);
    }

    @Operation(summary = "专辑-修改专辑标题")
    @PostMapping("/updateAppAlbumElementTitle")
    public Boolean updateAppAlbumElementTitle(@RequestBody @Valid AppAlbumElementsUpdateTitleApiBO bo) throws BusinessException {
        return service.updateAppAlbumElementTitle(bo);
    }

    @Operation(summary = "专辑-修改专辑简介")
    @PostMapping("/updateAppAlbumElementRemark")
    public Boolean updateAppAlbumElementRemark(@RequestBody @Valid AppAlbumElementsUpdateRemarkApiBO bo) throws BusinessException {
        return service.updateAppAlbumElementRemark(bo);
    }

    @Operation(summary = "专辑-修改专辑可见性")
    @PostMapping("/updateAppAlbumElementVisible")
    public Boolean updateAppAlbumElementVisible(@RequestBody @Valid AppAlbumElementsUpdateVisibleApiBO bo) throws BusinessException {
        return service.updateAppAlbumElementVisible(bo);
    }

    @Operation(summary = "专辑-修改专辑上架状态")
    @PostMapping("/updateAppAlbumElementStatus")
    public Boolean updateAppAlbumElementStatus(@RequestBody @Valid AppAlbumElementsUpdateStatusApiBO bo) throws BusinessException {
        return service.updateAppAlbumElementStatus(bo);
    }
}
