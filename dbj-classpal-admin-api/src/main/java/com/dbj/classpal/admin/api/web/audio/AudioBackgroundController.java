package com.dbj.classpal.admin.api.web.audio;


import com.dbj.classpal.admin.service.remote.audio.AudioBackgroundRemoteService;
import com.dbj.classpal.books.client.bo.audio.AudioBackgroundBO;
import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 音频背景音 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Tag(name = "新增音频-背景音", description = "新增音频-背景音管理")
@RestController
@RequestMapping("/audio-background")
public class AudioBackgroundController {

    @Autowired
    private AudioBackgroundRemoteService audioBackgroundRemoteService;

    @Operation(summary = "新增", description = "新增背景音")
    @PostMapping("/save")
    public Integer save(@Valid @RequestBody List<AudioBackgroundBO> bo) throws BusinessException {
        return audioBackgroundRemoteService.save(bo);
    }

    @Operation(summary = "重排序", description = "重排序背景音")
    @PostMapping("/reorder")
    public Integer reorder(@Valid @RequestBody List<Integer> bo) throws BusinessException {
        return audioBackgroundRemoteService.reorder(bo);
    }

    @Operation(summary = "查询自定义背景音", description = "查询自定义背景音列表")
    @PostMapping("/getDefinitionBgm")
    public List<AudioBackgroundDTO> getDefinitionBgm(@Valid @RequestBody AudioTypeBO bo) throws BusinessException {
        return audioBackgroundRemoteService.getDefinitionBgm(bo);
    }

}
