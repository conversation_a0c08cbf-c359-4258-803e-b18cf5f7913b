package com.dbj.classpal.admin.api.web.evaluation;

import com.dbj.classpal.admin.service.remote.appevaluation.AppEvaluationNodeRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeEditApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSortApiBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationController
 * Date:     2025-05-16 16:14:22
 * Description: 表名： ,描述： 表
 */
@Tag(name = "内容管理-评测-评测项", description = "内容管理-评测-评测项")
@RestController
@RequestMapping("/evaluationNode")
public class AppEvaluationNodeController {
    @Resource
    private AppEvaluationNodeRemoteService appEvaluationNodeRemoteService;

    /**
     * 新增评测项
     * @param bo
     * @return
     * @throws BusinessException
     */
    @Operation(summary = "新增评测项", description = "新增评测项")
    @PostMapping("/saveEvaluationNode")
    public Boolean saveEvaluationNode(@RequestBody @Valid AdminEvaluationNodeSaveApiBO bo) throws BusinessException{
        return appEvaluationNodeRemoteService.saveEvaluationNode(bo);
    }


    /**
     * 修改评测项
     * @param bo
     * @return
     * @throws BusinessException
     */
    @Operation(summary = "修改评测项", description = "修改评测项")
    @PostMapping("/editEvaluationNode")
    public Boolean editEvaluationNode(@RequestBody @Valid AdminEvaluationNodeEditApiBO bo) throws BusinessException{
        return appEvaluationNodeRemoteService.editEvaluationNode(bo);
    }

    /**
     * 评测项排序
     * @param bo
     * @return
     * @throws BusinessException
     */
    @Operation(summary = "评测项排序", description = "评测项排序")
    @PostMapping("/reSort")
    public Boolean reSort(@RequestBody @Valid AdminEvaluationNodeSortApiBO bo) throws BusinessException{
        return appEvaluationNodeRemoteService.reSort(bo);
    }

    /**
     * 删除评测项
     * @param bo
     * @return
     * @throws BusinessException
     */
    @Operation(summary = "删除评测项", description = "删除评测项")
    @PostMapping("/deleteNode")
    public Boolean deleteNode(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException{
        return appEvaluationNodeRemoteService.deleteNode(bo);
    }

}
