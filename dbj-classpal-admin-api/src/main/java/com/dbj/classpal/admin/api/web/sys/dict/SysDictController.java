package com.dbj.classpal.admin.api.web.sys.dict;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictDetailBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictUpdBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictUpdStatusBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictDTO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.service.sys.dict.ISysDictService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据字典主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/sys-dict")
@Tag(name = "字典信息接口", description = "字典相关操作")
public class SysDictController {

    @Resource
    private ISysDictService SysDictService;

    /**
     * 获取单个SysDict数据
     * @Title: saveSysDict
     * @Description: 添加SysDict数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "获取单个SysDict数据")
    @RequestMapping(value = "/getSysDictInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SysDictDTO getSysDictInfo(@Validated @RequestBody SysDictDetailBO reqBo) throws BusinessException {
        return this.SysDictService.getSysDictInfo(reqBo);
    }

    /**
     * 添加SysDict数据
     * @Title: saveSysDict
     * @Description: 添加SysDict数据
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "添加SysDict数据")
    @RequestMapping(value = "/saveSysDict", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean saveSysDict(@Validated @RequestBody SysDictSaveBO bo) throws BusinessException {
        return this.SysDictService.saveSysDict(bo);
    }

    /**
     * 修改数据
     * @Title: UpdateSysDict
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "修改数据")
    @RequestMapping(value = "/updateSysDict", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateSysDict(@Validated @RequestBody SysDictUpdBO bo) throws BusinessException {
        return this.SysDictService.updateSysDict(bo);
    }
    /**
     * 批量修改数据状态
     * @Title: UpdateSysDict
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "批量修改数据状态")
    @RequestMapping(value = "/batchUpdateStatus", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean batchUpdateStatus(@Validated @RequestBody SysDictUpdStatusBO bo) throws BusinessException {
        return this.SysDictService.batchUpdateStatus(bo);
    }


    /**
     * 批量删除字典数据
     * @Title: delSysDictInfo
     * @Description: 添加SysDict数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "批量删除字典数据")
    @RequestMapping(value = "/batchDelSysDictInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean batchDelSysDictInfo(@Validated @RequestBody BaseIdsBO baseIdsBO) throws BusinessException {
        return this.SysDictService.batchDelSysDictInfo(baseIdsBO);
    }

    /**
     * <AUTHOR>
     * @Description  分页获取字典数据
     * @Date 2025/3/17 9:22 
     * @param page
     * @return Page
     **/
    @Operation(summary =  "分页获取字典数据")
    @RequestMapping(value = "/pageSysDictInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SysDictDTO> pageSysDictInfo(@Validated @RequestBody PageInfo<SysDictBO> page) throws BusinessException {
        return this.SysDictService.pageSysDictInfo(page);
    }
    /**
     * <AUTHOR>
     * @Description  获取所有字典数据
     * @Date 2025/3/17 9:22 
     * @return List
     **/
    @Operation(summary =  "获取所有字典数据")
    @RequestMapping(value = "/getSysDictInfoAll", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Map<String,List<SysDictItemDTO>> getSysDictInfoAll() throws BusinessException {
        return this.SysDictService.getSysDictInfoAll();
    }
    /**
     * <AUTHOR>
     * @Description  刷新字典缓存
     * @Date 2025/3/17 9:22
     * @return List
     **/
    @Operation(summary =  "刷新字典缓存")
    @RequestMapping(value = "/refreshCacheDict", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean refreshCacheDict() throws BusinessException {
        return this.SysDictService.refreshCacheDict();
    }
}
