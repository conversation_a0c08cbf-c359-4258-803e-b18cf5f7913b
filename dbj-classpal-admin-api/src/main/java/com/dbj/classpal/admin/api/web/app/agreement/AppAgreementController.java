package com.dbj.classpal.admin.api.web.app.agreement;

import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementQueryBO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementSaveBO;
import com.dbj.classpal.admin.common.bo.app.agreement.AppAgreementUpdateBO;
import com.dbj.classpal.admin.common.dto.app.agreement.AppAgreementQueryDTO;
import com.dbj.classpal.admin.service.service.app.agreement.IAppAgreementService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/app-agreement")
@Tag(name = "APP协议管理接口", description = "APP协议管理相关操作")
public class AppAgreementController {

    @Autowired
    private IAppAgreementService appAgreementService;


    @GetMapping(value = "/getAllAppAgreements")
    @Operation(summary = "获取所有协议列表", description = "获取所有协议列表")
    public List<AppAgreementQueryDTO> getAllAppAgreements() throws BusinessException {
        return appAgreementService.getAllAppAgreements();
    }

    @GetMapping(value = "/getAppAgreementById")
    @Operation(summary = "根据id获取协议信息", description = "根据id获取协议信息")
    public AppAgreementQueryDTO getAppAgreementById(@RequestParam("id") Integer id) throws BusinessException {
        return appAgreementService.getAppAgreementById(id);
    }

    @PostMapping(value = "/getAppAgreementByTypeId")
    @Operation(summary = "根据协议类型获取协议信息", description = "根据协议类型获取协议信息")
    public AppAgreementQueryDTO getAppAgreementByTypeId(@RequestBody @Valid AppAgreementQueryBO appAgreementQueryBO){
        return appAgreementService.getAppAgreementByTypeId(appAgreementQueryBO);
    }


    @PostMapping(value = "/saveAppAgreement")
    @Operation(summary = "新增单个协议信息", description = "新增单个协议信息")
    public Boolean saveAppAgreement(@RequestBody @Valid AppAgreementSaveBO appAgreementSaveBO) throws BusinessException {
        return appAgreementService.saveAppAgreement(appAgreementSaveBO);
    }

    @PostMapping(value = "/updateAppAgreement")
    @Operation(summary = "修改单个协议信息", description = "修改单个协议信息")
    public Boolean updateAppAgreement(@RequestBody @Valid AppAgreementUpdateBO bo) throws BusinessException {
        return appAgreementService.updateAppAgreement(bo);
    }


}
