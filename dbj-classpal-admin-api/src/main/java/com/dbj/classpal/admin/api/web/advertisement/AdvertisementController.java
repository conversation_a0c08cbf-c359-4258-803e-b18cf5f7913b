package com.dbj.classpal.admin.api.web.advertisement;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.advertisement.AdvertisementApi;
import com.dbj.classpal.books.client.api.advertisement.AdvertisementResourceApi;
import com.dbj.classpal.books.client.bo.advertisement.AdevertisementDelBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementResourceBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementStatusUpdateBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementUpsertBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementResourceDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <p>
 * 广告 前端控制器
 * </p>
 *
 * <AUTHOR> Yi
 * @since 2025-04-24
 */
@Tag(name = "广告", description = "广告相关接口")
@RestController
@RequestMapping("/advertisement")
public class AdvertisementController {

	@Resource
	private AdvertisementApi advertisementApi;

	@Resource
	private AdvertisementResourceApi advertisementResourceApi;

	@Operation(summary = "广告列表", description = "广告列表")
	@PostMapping("/getAdvertisementPage")
	public Page<AdvertisementDTO> getAdvertisementPage(@Validated @RequestBody PageInfo<AdvertisementPageBO> pageInfo) throws BusinessException {
		RestResponse<Page<AdvertisementDTO>> result = advertisementApi.getAdvertisementPage(pageInfo);
		return result.returnProcess(result);
	}

	@Operation(summary = "广告详情", description = "广告详情")
    @PostMapping(value = "/getAdvertisementInfo")
    public AdvertisementDTO getAdvertisementInfo(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException {
		RestResponse<AdvertisementDTO> result = advertisementApi.getAdvertisementInfo(bo);
		return result.returnProcess(result);
    }

	@Operation(summary = "保存广告策略", description = "保存广告策略")
	@PostMapping(value = "/saveAdvertisement")
	public Boolean saveAdvertisement(@Validated @RequestBody AdvertisementUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = advertisementApi.saveAdvertisement(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "编辑广告策略", description = "编辑广告策略")
	@PostMapping(value = "/updateAdvertisement")
	public Boolean updateAdvertisement(@Validated @RequestBody AdvertisementUpsertBO bo) throws BusinessException {
		RestResponse<Boolean> result = advertisementApi.updateAdvertisement(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "删除广告策略", description = "删除广告策略")
	@PostMapping(value = "/deleteAdvertisement")
	public Boolean deleteAdvertisement(@Validated @RequestBody AdevertisementDelBO bo) throws BusinessException {
		RestResponse<Boolean> result = advertisementApi.deleteAdvertisement(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "广告启用/禁用", description = "广告启用/禁用")
	@PostMapping("/updateAdvertisementStatus")
	public Boolean updateAdvertisementStatus(@Validated @RequestBody AdvertisementStatusUpdateBO bo) throws BusinessException {
		RestResponse<Boolean> result = advertisementApi.updateAdvertisementStatus(bo);
		return result.returnProcess(result);
	}

	@Operation(summary = "广告资源列表", description = "广告资源列表")
	@PostMapping("/getAdvertisementResourceList")
	public List<AdvertisementResourceDTO> getAdvertisementResourceList(@Validated @RequestBody AdvertisementResourceBO bo) throws BusinessException {
		RestResponse<List<AdvertisementResourceDTO>> result = advertisementResourceApi.getAdvertisementResourceList(bo);
		return result.returnProcess(result);
	}
}
