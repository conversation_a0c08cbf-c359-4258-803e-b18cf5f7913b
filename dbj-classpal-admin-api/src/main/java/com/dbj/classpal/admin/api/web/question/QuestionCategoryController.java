package com.dbj.classpal.admin.api.web.question;

import com.dbj.classpal.admin.service.remote.books.question.AppQuestionCategoryRemoteService;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryRefApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/04/18 09:52:42
 */
@RestController
@RequestMapping("/question-category")
@Tag(name = "题库分类接口", description = "题库分类接口")
public class QuestionCategoryController {
    @Resource
    AppQuestionCategoryRemoteService questionCategoryRemoteService;
    /**
     * 获取分类列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取分类列表",description = "获取分类列表")
    List<QuestionCategoryApiDTO> getCategoryList(@RequestBody QuestionCategoryIdApiBO request) throws BusinessException {
        return questionCategoryRemoteService.getCategoryList(request);
    }

    /**
     * 创建分类
     */
    @PostMapping("/create")
    @Operation(summary = "创建分类",description = "创建分类")
    Integer createCategory(@RequestBody @Valid  QuestionCategoryApiBO request) throws BusinessException{
        return questionCategoryRemoteService.createCategory(request);
    }

    /**
     * 更新分类
     */
    @PostMapping("/edit")
    @Operation(summary = "更新分类",description = "更新分类")
    Void updateCategory(@RequestBody @Valid QuestionCategoryApiBO request) throws BusinessException{
        return questionCategoryRemoteService.updateCategory(request);
    }

    /**
     * 删除分类
     */
    @PostMapping("/batch/delete")
    @Operation(summary = "删除分类",description = "删除分类")
    Void batchDeleteCategory(@RequestBody @Valid  QuestionCategoryIdsApiBO request) throws BusinessException{
        return questionCategoryRemoteService.batchDeleteCategory(request);
    }

    /**
     * 更新分类排序
     */
    @PostMapping("/sort")
    @Operation(summary = "更新分类排序",description = "更新分类排序")
    Void updateSort(@RequestBody @Valid  QuestionCategorySortApiBO request) throws BusinessException{
        return  questionCategoryRemoteService.updateSort(request);
    }

    /**
     * 获取分类引用
     */
    @PostMapping("/category/reference")
    @Operation(summary = "获取分类引用",description = "获取分类引用")
    List<QuestionCategoryRefApiDTO> getBusinessRefs(@RequestBody QuestionCategoryIdQueryApiBO request) throws BusinessException {
        return  questionCategoryRemoteService.getBusinessRefs(request);
    }

}
