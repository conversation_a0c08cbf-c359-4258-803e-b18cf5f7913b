package com.dbj.classpal.admin.api.web.audio;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.audio.AudioIntroRemoteService;
import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioIntroDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 音频制作列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@Tag(name = "音频-简介")
@RequiredArgsConstructor
@RequestMapping("/audio-intro")
public class AudioIntroController {

    private final AudioIntroRemoteService audioIntroRemoteService;

    @Operation(summary = "音频分页列表")
    @PostMapping("/page")
    public IPage<AudioIntroDTO> page(@RequestBody PageInfo<AudioIntroQueryBO> pageInfo) throws BusinessException {
        return audioIntroRemoteService.page(pageInfo);
    }

    @Operation(summary = "保存")
    @PostMapping("/save")
    public Boolean save(@Validated @RequestBody AudioIntroSaveBO bo) throws BusinessException {
        return audioIntroRemoteService.save(bo);
    }

    @Operation(summary = "移动")
    @PostMapping("/move")
    public Boolean move(@Validated @RequestBody AudioIntroMoveBO bo) throws BusinessException {
        return audioIntroRemoteService.move(bo);
    }

    @Operation(summary = "删除")
    @PostMapping("/remove")
    public Boolean remove(@Validated @RequestBody AudioIntroDelBO bo) throws BusinessException {
        return audioIntroRemoteService.remove(bo);
    }

    @Operation(summary = "复制")
    @PostMapping("copy")
    public Boolean copy(@Validated @RequestBody AudioIntroCopyBO bo) throws BusinessException {
        return audioIntroRemoteService.copy(bo);
    }

    @Operation(summary = "查询详情")
    @PostMapping("getDetails")
    public AudioIntroDTO getDetails(@Validated @RequestBody AudioIntroIdBO bo) throws BusinessException {
        return audioIntroRemoteService.getDetails(bo);
    }
}
