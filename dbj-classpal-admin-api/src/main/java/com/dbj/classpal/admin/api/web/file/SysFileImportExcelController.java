package com.dbj.classpal.admin.api.web.file;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelClearBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelSaveBO;
import com.dbj.classpal.admin.common.bo.file.importfile.SysFileImportExcelTypeBO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelCountDTO;
import com.dbj.classpal.admin.common.dto.file.importfile.SysFileImportExcelDTO;
import com.dbj.classpal.admin.service.service.file.ISysFileImportExcelService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 导入文件记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "导入excel文件上传接口", description = "导入excel文件上传接口")
@RestController
@RequestMapping("/sys-file-import-excel")
public class SysFileImportExcelController {

    @Resource
    private ISysFileImportExcelService sysFileImportExcelService;

    /**
     * 获取一个File
     *
     * @param id
     * @return RestResponse<FileDTO>
     */
    @Operation(summary =  "获取一个File", description = "获取一个File")
    @GetMapping("getImportFileInfo")
    public SysFileImportExcelDTO getImportFileInfo(@Parameter(description = "主键id") @RequestParam Integer id) throws BusinessException{
        return sysFileImportExcelService.getImportFileInfo(id);
    }

    /**
     * 新增File
     *
     * @param bo
     * @return RestResponse<Integer>
     */
    @Operation(summary =  "新增File", description = "新增File")
    @PostMapping("saveImportFile")
    public Integer saveImportFile(@RequestBody SysFileImportExcelSaveBO bo) throws Exception{
        return sysFileImportExcelService.saveImportFile(bo);
    }

    /**
     * 取消上传
     * @Title: 取消上传
     * @Description: 取消上传
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "取消上传", description = "取消上传")
    @PostMapping("cancel")
    public Boolean cancel(@RequestBody BaseIdBO bo){
         return sysFileImportExcelService.cancel(bo);
    }
    /**
     * 清空接口
     * @Title: 清空接口
     * @Description: 清空接口
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "清空接口", description = "清空接口")
    @PostMapping("deleteClear")
    public Boolean deleteClear(@RequestBody SysFileImportExcelClearBO bo){
        return sysFileImportExcelService.deleteClear(bo);
    }

    @Operation(summary =  "获取文件上传记录统计", description = "获取文件上传记录")
    @PostMapping("sysFileImportExcelCount")
    public List<SysFileImportExcelCountDTO> sysFileImportExcelCount(){
        return sysFileImportExcelService.sysFileImportExcelCount();
    }

    /**
     * 获取用户最近上传的记录
     * @Title: 获取用户最近上传的记录
     * @Description: 获取用户最近上传的记录
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "获取用户最近上传的记录", description = "获取用户最近上传的记录")
    @PostMapping("pageSysFileImportExcel")
    public Page<SysFileImportExcelDTO> pageSysFileImportExcel(@RequestBody PageInfo<SysFileImportExcelBO> page){
        return sysFileImportExcelService.pageSysFileImportExcel(page);
    }

    /**
     * top3
     * @param bo
     * @return
     */
    @Operation(summary =  "获取top3", description = "获取top3")
    @PostMapping("getFileInfoTop3")
    public List<SysFileImportExcelDTO> getFileInfoTop3(@RequestBody SysFileImportExcelTypeBO bo){
        return sysFileImportExcelService.getFileInfoTop3(bo);
    }
}
