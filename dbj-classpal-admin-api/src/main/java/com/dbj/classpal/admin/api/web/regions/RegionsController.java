package com.dbj.classpal.admin.api.web.regions;


import com.dbj.classpal.app.client.api.regions.RegionsClientApi;
import com.dbj.classpal.app.client.bo.regions.RegionsBO;
import com.dbj.classpal.app.client.dto.regions.RegionsDataDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> Yi
 * @since 2025-03-31
 */
@RestController
@RequestMapping("/regions")
@Tag(name = "城市管理", description = "城市管理")
public class RegionsController {

    @Resource
    private RegionsClientApi regionsClientApi;

    @PostMapping(value = "/getRegionData")
    @Operation(summary = "获取所有省和市", description = "获取所有省和市")
    public RegionsDataDTO getRegionData() throws BusinessException {
        RestResponse<RegionsDataDTO> result = regionsClientApi.getRegionsData(new RegionsBO());
        return result.returnProcess(result);
    }
}
