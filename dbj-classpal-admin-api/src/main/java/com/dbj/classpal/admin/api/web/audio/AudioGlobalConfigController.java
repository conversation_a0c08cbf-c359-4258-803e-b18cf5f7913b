package com.dbj.classpal.admin.api.web.audio;


import com.dbj.classpal.admin.service.remote.audio.AudioGlobalConfigRemoteService;
import com.dbj.classpal.books.client.bo.audio.AudioGlobalConfigAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioIntroIdBO;
import com.dbj.classpal.books.client.dto.audio.AudioGlobalConfigDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 音频全局配置项 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Tag(name = "音频-全局配置", description = "音频-全局配置管理")
@RestController
@RequestMapping("/audio-global-config")
public class AudioGlobalConfigController {

    @Autowired
    private AudioGlobalConfigRemoteService audioGlobalConfigRemoteService;

    @Operation(summary = "获取全局配置", description = "获取全局配置")
    @PostMapping("/getGlobalConfig")
    public List<AudioGlobalConfigDTO> getGlobalConfig(@Valid @RequestBody AudioIntroIdBO bo) throws BusinessException {
        return audioGlobalConfigRemoteService.getGlobalConfig(bo);
    }

    @Operation(summary = "保存全局配置", description = "保存全局配置")
    @PostMapping("/saveGlobalConfig")
    public Integer saveGlobalConfig(@Valid @RequestBody List<AudioGlobalConfigAddBO> bo) throws BusinessException {
        return audioGlobalConfigRemoteService.saveGlobalConfig(bo);
    }
}
