package com.dbj.classpal.admin.api.web.app.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.app.config.*;
import com.dbj.classpal.admin.common.dto.app.config.AppConfigItemQueryDTO;
import com.dbj.classpal.admin.service.service.app.config.IAppConfigItemService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/app-config-item")
@Tag(name = "APP配置项接口", description = "APP配置项相关操作")
public class AppConfigItemController {

    @Autowired
    private IAppConfigItemService appConfigItemService;

    /**
     * 根据id查询单个配置项信息
     * @param id
     * @return
     */
    @GetMapping(value = "/getAppConfigItemById")
    @Operation(summary = "根据id查询单个配置项",description = "根据id查询单个配置项")
    public AppConfigItemQueryDTO getAppConfigItemById(@RequestParam("id") Integer id) throws BusinessException {
        return appConfigItemService.getAppConfigItemById(id);
    }

    /**
     * 根据配置编码查询配置项信息
     * @param typeCode
     * @return
     */
    @GetMapping(value = "/getAppConfigItemByTypeCode")
    @Operation(summary = "根据typeCode查询单个配置项",description = "根据typeCode查询单个配置项")
    public AppConfigItemQueryDTO getAppConfigItemByTypeCode(@RequestParam("typeCode") @Parameter(description = "配置类型编码 together_study_group共学群,feedback_suggestions反馈建议,customer客服") String typeCode) throws BusinessException {
        return appConfigItemService.getAppConfigItemByTypeCode(typeCode);
    }

    /**
     * 新增工学群信息
     * @param bo
     * @return
     */
    @PostMapping(value = "/saveTogetherStudyGroup")
    @Operation(summary = "新增工学群信息",description = "新增工学群信息")
    public Boolean saveTogetherStudyGroup(@RequestBody @Valid AppConfigItemTogetherStudyGroupSaveBO bo) throws BusinessException {
        return appConfigItemService.saveTogetherStudyGroup(bo);
    }

    /**
     * 修改工学群信息
     * @param bo
     * @return
     */
    @PostMapping(value = "/updateTogetherStudyGroup")
    @Operation(summary = "修改工学群信息",description = "修改工学群信息")
    public Boolean updateTogetherStudyGroup(@RequestBody @Valid AppConfigItemTogetherStudyGroupUpdateBO bo) throws BusinessException {
        return appConfigItemService.updateTogetherStudyGroup(bo);
    }


    /**
     * 新增反馈建议信息
     * @param bo
     * @return
     */
    @PostMapping(value = "/saveFeedBack")
    @Operation(summary = "新增反馈建议信息",description = "新增反馈建议信息")
    public Boolean saveFeedBackGroup(@RequestBody @Valid AppConfigItemFeedBackSaveBO bo) throws BusinessException {
        return appConfigItemService.saveFeedBack(bo);
    }

    /**
     * 修改反馈建议信息
     * @param bo
     * @return
     */
    @PostMapping(value = "/updateFeedBack")
    @Operation(summary = "修改反馈建议信息",description = "修改反馈建议信息")
    public Boolean saveFeedBackGroup(@RequestBody @Valid AppConfigItemFeedBackUpdateBO bo) throws BusinessException {
        return appConfigItemService.updateFeedBack(bo);
    }


    /**
     * 新增客服信息
     * @param bo
     * @return
     */
    @PostMapping(value = "/saveCustomer")
    @Operation(summary = "新增客服信息",description = "新增客服信息")
    public Boolean saveCustomer(@RequestBody @Valid AppConfigItemCustomerSaveBO bo) throws BusinessException {
        return appConfigItemService.saveCustomer(bo);
    }

    /**
     * 修改客服信息
     * @param bo
     * @return
     */
    @PostMapping(value = "/updateCustomer")
    @Operation(summary = "修改客服信息",description = "修改客服信息")
    public Boolean updateCustomer(@RequestBody @Valid AppConfigItemCustomerUpdateBO bo) throws BusinessException {
        return appConfigItemService.updateCustomer(bo);
    }

    /**
     * 分页查询分享海报列表
     * @param page
     * @return
     */
    @PostMapping(value = "/pageSharePoster")
    @Operation(summary = "分页查询分享海报列表",description = "分页查询分享海报列表")
    public Page<AppConfigItemQueryDTO> pageSharePoster(@RequestBody PageInfo<AppConfigItemSharePosterQueryBO> page) throws BusinessException {
        return appConfigItemService.PageSharePoster(page);
    }

    /**
     * 新增分享海报
     * @param bo
     * @return
     */
    @PostMapping(value = "/saveSharePoster")
    @Operation(summary = "新增分享海报",description = "新增分享海报")
    public Boolean saveSharePoster(@RequestBody @Valid AppConfigItemSharePosterSaveBO bo) throws BusinessException {
        return appConfigItemService.saveSharePoster(bo);
    }

    /**
     * 修改分享海报
     * @param bo
     * @return
     */
    @PostMapping(value = "/updateSharePoster")
    @Operation(summary = "修改分享海报",description = "修改分享海报")
    public Boolean updateSharePoster(@RequestBody @Valid AppConfigItemSharePosterUpdateBO bo) throws BusinessException {
        return appConfigItemService.updateSharePoster(bo);
    }

    /**
     * 启用|禁用分享海报
     * @param id
     * @return
     */
    @GetMapping(value = "/updateSharePosterStatus")
    @Operation(summary = "启用|禁用分享海报",description = "启用|禁用分享海报")
    public Boolean updateSharePosterStatus(@RequestParam("id") Integer id) throws BusinessException {
        return appConfigItemService.updateSharePosterStatus(id);
    }

    /**
     * 删除分享海报
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delSharePoster")
    @Operation(summary = "删除分享海报",description = "删除分享海报")
    public Boolean delSharePoster(@RequestParam("id") Integer id) throws BusinessException {
        return appConfigItemService.delSharePoster(id);
    }

    /**
     * 批量启用分享海报
     * @param bo
     * @return
     */
    @PostMapping(value = "/updateSharePosterEnableStatusList")
    @Operation(summary = "批量启用分享海报",description = "批量启用分享海报")
    public Boolean updateSharePosterEnableStatusList(@RequestBody AppConfigItemCommonQueryBO bo) throws BusinessException {
        return appConfigItemService.updateSharePosterEnableStatusList(bo);
    }

    /**
     * 批量禁用分享海报
     * @param bo
     * @return
     */
    @PostMapping(value = "/updateSharePosterDisableStatusList")
    @Operation(summary = "批量禁用分享海报",description = "批量禁用分享海报")
    public Boolean updateSharePosterDisableStatusList(@RequestBody AppConfigItemCommonQueryBO bo) throws BusinessException {
        return appConfigItemService.updateSharePosterDisableStatusList(bo);
    }

    /**
     * 批量删除分享海报
     * @param bo
     * @return
     */
    @PostMapping(value = "/delSharePosterList")
    @Operation(summary = "批量删除分享海报",description = "批量删除分享海报")
    public Boolean delSharePosterList(@RequestBody AppConfigItemCommonQueryBO bo) throws BusinessException {
        return appConfigItemService.delSharePosterList(bo);
    }
}
