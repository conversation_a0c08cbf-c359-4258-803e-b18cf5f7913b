package com.dbj.classpal.admin.api.web.sys.help;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.help.BatchUpdHelpStatusBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsDetailBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsSaveBO;
import com.dbj.classpal.admin.common.bo.sys.help.SysHelpItemsUpdBO;
import com.dbj.classpal.admin.common.dto.help.SysHelpItemsDTO;
import com.dbj.classpal.admin.service.service.sys.help.ISysHelpItemsService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 角色信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@RestController
@RequestMapping("/sys-help-items")
@Tag(name = "帮助说明信息接口", description = "帮助说明相关操作")
public class SysHelpItemsController {


    @Resource
    private ISysHelpItemsService sysHelpItemsService;

    /**
     * <AUTHOR>
     * @Description  获取单个帮助文档
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "获取单个帮助说明文档")
    @RequestMapping(value = "/getSysHelpItemsInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SysHelpItemsDTO getSysHelpItemsInfo(SysHelpItemsDetailBO sysHelpItemsBO ) throws BusinessException {
        return sysHelpItemsService.getSysHelpItemsInfo(sysHelpItemsBO);
    }
    /**
     * <AUTHOR>
     * @Description  根据页面id获取单个帮助说明文档
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "根据页面id获取单个帮助说明文档")
    @GetMapping(value = "/getSysHelpItemsInfoByPageId")
    public SysHelpItemsDTO getSysHelpItemsInfoByPageId(@Parameter(description = "页面id") @RequestParam Integer menuPageId ) throws BusinessException {
        return sysHelpItemsService.getSysHelpItemsInfoByPageId(menuPageId);
    }

    /**
     * <AUTHOR>
     * @Description  保存帮助文档
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "保存帮助说明文档")
    @RequestMapping(value = "/saveSysHelpItems", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean saveSysHelpItems(@Validated @RequestBody SysHelpItemsSaveBO bo) throws BusinessException{
        return sysHelpItemsService.saveSysHelpItems(bo);
    }

    /**
     * <AUTHOR>
     * @Description  修改帮助文档
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "修改帮助说明文档")
    @RequestMapping(value = "/updateSysHelpItems", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateSysHelpItems(@Validated @RequestBody SysHelpItemsUpdBO bo) throws BusinessException{
        return sysHelpItemsService.updateSysHelpItems(bo);
    }


    /**
     * <AUTHOR>
     * @Description  批量修改帮助文档状态
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "批量修改帮助文档说明状态")
    @RequestMapping(value = "/batchUpdHelpStatus", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean batchUpdHelpStatus(@Validated @RequestBody BatchUpdHelpStatusBO bo) throws BusinessException{
        return sysHelpItemsService.batchUpdHelpStatus(bo);
    }
    /**
     * <AUTHOR>
     * @Description  批量删除帮助文档状态
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "批量删除帮助文档")
    @RequestMapping(value = "/batchDelHelp", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean batchDelHelp(@Validated @RequestBody BatchUpdHelpStatusBO bo) throws BusinessException{
        return sysHelpItemsService.batchDelHelp(bo);
    }


    /**
     * <AUTHOR>
     * @Description  分页查询帮助文档状态
     * @Date 2025/3/17 11:28
     * @param
     * @return
     **/
    @Operation(summary =  "分页查询帮助文档状态")
    @RequestMapping(value = "/pageSysHelpItems", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SysHelpItemsDTO> pageSysHelpItems(@Validated @RequestBody PageInfo<SysHelpItemsBO> bo) throws BusinessException{
        return sysHelpItemsService.pageSysHelpItems(bo);
    }


}
