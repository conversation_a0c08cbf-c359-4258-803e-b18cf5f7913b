package com.dbj.classpal.admin.api.web.books;


import com.dbj.classpal.admin.service.remote.books.book.AdminBooksCompatibilityRemoteService;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilitySaveBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityUpdBO;
import com.dbj.classpal.books.client.dto.books.BooksCompatibilityApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 图书兼容链接表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Tag(name = "【图书】-兼容连接管理", description = "【图书】-兼容连接管理")
@RestController
@RequestMapping("/books-compatibility")
public class BooksCompatibilityController {

    @Resource
    private AdminBooksCompatibilityRemoteService adminBooksCompatibilityRemoteService;

    @Operation(summary =  "新增兼容连接", description = "新增兼容连接")
    @PostMapping("/books/compatibility/save")
    public Boolean save(@Valid @RequestBody BooksCompatibilitySaveBO booksCompatibilitySaveBO) throws BusinessException{
        return adminBooksCompatibilityRemoteService.save(booksCompatibilitySaveBO);
    }

    @Operation(summary =  "兼容连接集合", description = "兼容连接集合")
    @PostMapping("/books/compatibility/list")
    public List<BooksCompatibilityApiDTO> list(@Valid  @RequestBody BooksCompatibilityBO booksCompatibilityBO) throws BusinessException{
        return adminBooksCompatibilityRemoteService.list(booksCompatibilityBO);
    }

    @Operation(summary =  "修改兼容连接", description = "修改兼容连接")
    @PostMapping("/books/compatibility/update")
    public Boolean update(@Valid  @RequestBody BooksCompatibilityUpdBO booksCompatibilityUpdBO) throws BusinessException{
        return adminBooksCompatibilityRemoteService.update(booksCompatibilityUpdBO);
    }

    @Operation(summary =  "删除兼容连接", description = "删除兼容连接")
    @GetMapping("/books/compatibility/delete")
    public Boolean delete(@NotNull(message = "参数不能为空") @RequestParam  Integer id) throws BusinessException{
        return adminBooksCompatibilityRemoteService.delete(id);
    }


}
