package com.dbj.classpal.admin.api.web.app.config;

import com.dbj.classpal.admin.common.dto.app.config.AppConfigTypeQueryDTO;
import com.dbj.classpal.admin.service.service.app.config.IAppConfigTypeService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/app-config-type")
@Tag(name = "APP配置类型接口", description = "APP配置类型相关操作")
public class AppConfigTypeController {

    @Autowired
    private IAppConfigTypeService appConfigTypeService;

    @GetMapping(value = "/getAllAppConfigTypeList")
    @Operation(summary = "查询所有APP配置类型列表", description = "查询所有APP配置类型列表")
    public List<AppConfigTypeQueryDTO> getAllAppConfigTypeList() throws BusinessException {
        return appConfigTypeService.getAllAppConfigTypeList();
    }
}
