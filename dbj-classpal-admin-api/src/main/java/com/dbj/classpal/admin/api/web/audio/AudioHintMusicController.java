package com.dbj.classpal.admin.api.web.audio;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.audio.AudioHintMusicRemoteService;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicPageBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicUpdBO;
import com.dbj.classpal.books.client.dto.audio.AudioHintMusicPageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 预置提示音表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@Tag(name = "音频-制作配置")
@RequiredArgsConstructor
@RequestMapping("/audio-preset-music")
public class AudioHintMusicController {

    private final AudioHintMusicRemoteService audioHintMusicRemoteService;


    @Operation(summary = "新增", description = "新增背景音")
    @PostMapping("/save")
    public Boolean save(@Valid @RequestBody AudioHintMusicAddBO bo) throws BusinessException {
        return audioHintMusicRemoteService.save(bo);
    }

    @Operation(summary = "编辑", description = "编辑背景音")
    @PostMapping("/update")
    public Boolean update(@Valid @RequestBody AudioHintMusicUpdBO bo) throws BusinessException {
        return audioHintMusicRemoteService.update(bo);
    }

    @Operation(summary =  "分页查询", description = "分页查询预置音频")
    @PostMapping("/pageInfo")
    public Page<AudioHintMusicPageDTO> pageInfo(@RequestBody PageInfo<AudioHintMusicPageBO> pageRequest) throws BusinessException{
        return audioHintMusicRemoteService.pageInfo(pageRequest);
    }

    @Operation(summary = "批量删除", description = "批量删除背景音")
    @PostMapping("/delete")
    public Boolean update(@Valid @RequestBody AudioHintMusicDelBO bo) throws BusinessException {
        return audioHintMusicRemoteService.delete(bo);
    }

}
