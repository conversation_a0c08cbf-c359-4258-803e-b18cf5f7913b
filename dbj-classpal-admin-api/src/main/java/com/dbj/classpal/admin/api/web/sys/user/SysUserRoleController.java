package com.dbj.classpal.admin.api.web.sys.user;


import com.dbj.classpal.admin.common.bo.sys.user.AllocationRoleBO;
import com.dbj.classpal.admin.common.bo.sys.user.AllocationUserBO;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 用户与角色关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@RestController
@RequestMapping("/sys-user-role")
@Tag(name = "用户角色关联信息接口", description = "用户角色关联相关操作")
public class SysUserRoleController {

    @Resource
    private ISysUserRoleService sysUserRoleService;

    @PostMapping("/allocationUser")
    @Operation(summary = "角色分配用户", description = "角色分配用户")
    public Boolean allocationUser(@Validated @RequestBody AllocationUserBO allocationUser) {
        return sysUserRoleService.allocationUser(allocationUser);
    }

    @PostMapping("/allocationRole")
    @Operation(summary = "用户分配角色", description = "用户分配角色")
    public Boolean allocationRole(@Validated @RequestBody AllocationRoleBO allocationRoleBO) {
        return sysUserRoleService.allocationRole(allocationRoleBO);
    }

    @GetMapping("/getUserByRoleId")
    @Operation(summary = "查询角色用户", description = "查询角色用户")
    public List<Integer> getUserByRoleId(@Parameter(description = "用户ID") @RequestParam Integer roleId) {
        return sysUserRoleService.getUserByRoleId(roleId);
    }


}
