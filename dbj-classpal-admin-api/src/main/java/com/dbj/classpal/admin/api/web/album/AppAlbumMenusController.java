package com.dbj.classpal.admin.api.web.album;

import com.dbj.classpal.admin.service.remote.appalbum.AppAlbumElementsRemoteService;
import com.dbj.classpal.admin.service.remote.appalbum.AppAlbumMenusRemoteService;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.dto.album.AppAlbumMenusTreeApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenusController
 * Date:     2025-04-18 15:01:01
 * Description: 表名： ,描述： 表
 */
@RestController
@RequestMapping("/api/albumMenus")
@Tag(name = "内容管理-专辑管理-专辑分类")
public class AppAlbumMenusController {

    @Resource
    private AppAlbumMenusRemoteService service;

    @Operation(summary = "专辑分类-查询所有分类")
    @PostMapping("/getAllAlbumMenusTree")
    public AppAlbumMenusTreeApiDTO getAllAlbumMenusTree(@RequestBody AppAlbumMenusQueryApiBO bo) throws BusinessException {
        return service.getAllAlbumMenusTree(bo);
    }

    @Operation(summary = "专辑分类-重命名")
    @PostMapping("/reNameAlbumMenus")
    public Boolean reNameAlbumMenus(@RequestBody @Valid AppAlbumMenusReNameApiBO bo) throws BusinessException {
        return service.reNameAlbumMenus(bo);
    }

    @Operation(summary = "专辑分类-新增分类")
    @PostMapping("/saveAlbumMenus")
    public Boolean saveAlbumMenus(@RequestBody @Valid AppAlbumMenusSaveApiBO bo) throws BusinessException {
        return service.saveAlbumMenus(bo);
    }

    @Operation(summary = "专辑分类-删除分类")
    @PostMapping("/deleteAlbumMenus")
    public Boolean deleteAlbumMenus(@RequestBody @Valid AppAlbumMenusDeleteApiBO bo) throws BusinessException {
        return service.deleteAlbumMenus(bo);
    }

    @Operation(summary = "专辑分类-专辑分类排序")
    @PostMapping("/resetAlbumMenusOrderNum")
    public Boolean resetAlbumMenusOrderNum(@RequestBody @Valid AppAlbumMenusBatchMoveApiBO bo) throws BusinessException {
        return service.resetAlbumMenusOrderNum(bo);
    }
}
