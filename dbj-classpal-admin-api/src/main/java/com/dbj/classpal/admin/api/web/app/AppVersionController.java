package com.dbj.classpal.admin.api.web.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appversion.AppVersionRemoteService;
import com.dbj.classpal.app.client.bo.version.CheckVersionApiBO;
import com.dbj.classpal.app.client.bo.version.VersionEditApiBO;
import com.dbj.classpal.app.client.bo.version.VersionIdApiBO;
import com.dbj.classpal.app.client.bo.version.VersionIdsApiBO;
import com.dbj.classpal.app.client.bo.version.VersionQueryApiBO;
import com.dbj.classpal.app.client.bo.version.VersionSaveApiBO;
import com.dbj.classpal.app.client.dto.version.AppVersionApiDTO;
import com.dbj.classpal.app.client.dto.version.VersionDetailApiDTO;
import com.dbj.classpal.app.client.dto.version.VersionShareApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * APP版本管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "版本管理")
@RestController
@RequestMapping("/api/version")
public class AppVersionController {

    @Resource
    private AppVersionRemoteService versionService;

    @Operation(summary = "分页查询版本")
    @PostMapping("/page")
    public Page<VersionDetailApiDTO> pageVersions(@RequestBody @Valid PageInfo<VersionQueryApiBO> pageRequest) throws BusinessException {
        return versionService.pageVersions(pageRequest);
    }

    @Operation(summary = "保存版本")
    @PostMapping("/save")
    public void saveVersion(@RequestBody @Valid VersionSaveApiBO version) throws BusinessException {
        versionService.saveVersion(version);
    }

    @Operation(summary = "更新功能")
    @PostMapping("/edit")
    public void editVersion(@RequestBody @Valid VersionEditApiBO feature) throws BusinessException {
        versionService.editVersion(feature);
    }

    @Operation(summary = "获取版本详情")
    @PostMapping("/detail")
    public VersionDetailApiDTO getVersionDetail(@RequestBody VersionIdApiBO idApiBO) throws BusinessException {
        return versionService.getVersionDetail(idApiBO);
    }


    @Operation(summary = "批量删除")
    @PostMapping("/delete")
    public void batchDelete(@RequestBody VersionIdsApiBO ids) throws BusinessException {
        versionService.batchDelete(ids);
    }

    @Operation(summary = "批量发布")
    @PostMapping("/publish")
    public void batchPublish(@RequestBody VersionIdsApiBO ids) throws BusinessException {
        versionService.batchPublish(ids);
    }

    @Operation(summary = "批量撤销发布")
    @PostMapping("/cancel")
    public void batchCancel(@RequestBody VersionIdsApiBO ids) throws BusinessException {
        versionService.batchCancel(ids);
    }

    @Operation(summary = "获取分享信息")
    @PostMapping("/share")
    public VersionShareApiDTO getShareInfo(@RequestBody VersionIdApiBO id) throws BusinessException {
        return versionService.getShareInfo(id);
    }

    @Operation(summary = "检查版本更新")
    @PostMapping("/check")
    public AppVersionApiDTO checkVersion(@RequestBody CheckVersionApiBO apiBO) throws BusinessException {
        return versionService.checkVersion(apiBO);
    }
}
