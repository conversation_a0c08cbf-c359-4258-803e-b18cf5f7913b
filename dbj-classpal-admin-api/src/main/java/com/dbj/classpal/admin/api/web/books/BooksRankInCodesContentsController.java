package com.dbj.classpal.admin.api.web.books;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.service.remote.books.book.AdminBooksRankInCodeContentsRemoteService;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodeContentsUpdForceApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsMoveBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsSaveBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsTreeBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsUpdBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsUpdTypeBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsTreeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 图书书内码分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Tag(name = "【图书】-书内码内容管理", description = "【图书】-书内码内容管理")
@RestController
@RequestMapping("/books-rank-in-codes-contents")
public class BooksRankInCodesContentsController {

    @Resource
    private AdminBooksRankInCodeContentsRemoteService adminBooksRankInCodeContentsRemoteService;

    @Operation(summary =  "查询册数书内码目录树", description = "查询册数书内码目录树")
    @PostMapping("/books/rank/in/code/contents/list")
    public List<BooksRankInCodesContentsTreeDTO> list(@RequestBody BooksRankInCodesContentsTreeBO boardBooksRankClassifyBO) throws BusinessException {
        return adminBooksRankInCodeContentsRemoteService.list(boardBooksRankClassifyBO);
    }


    @Operation(summary =  "制码导出分页接口", description = "制码导出分页接口")
    @PostMapping("/books/rank/in/code/contents/page")
    public Page<BooksRankInCodesContentsPageDTO> page(@RequestBody PageInfo<BooksRankInCodesContentsPageBO> pageInfo) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.page(pageInfo);
    }
    @Operation(summary =  "保存目录数据", description = "保存目录数据")
    @PostMapping("/books/rank/in/code/contents/save")
    public Integer save(@RequestBody BooksRankInCodesContentsSaveBO booksRankInCodesContentsSaveBO) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.save(booksRankInCodesContentsSaveBO);
    }
    @Operation(summary =  "重命名", description = "重命名")
    @PostMapping("/books/rank/in/code/contents/update")
    public Boolean update(@RequestBody BooksRankInCodesContentsUpdBO booksRankInCodesContentsUpdBO) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.update(booksRankInCodesContentsUpdBO);
    }
    @Operation(summary =  "删除目录", description = "删除目录")
    @PostMapping("/books/rank/in/code/contents/delete")
    public Boolean delete(@RequestBody BaseIdBO baseIdBO) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.delete(baseIdBO.getId());
    }
    @Operation(summary =  "更改类型", description = "更改类型")
    @PostMapping("/books/rank/in/code/contents/updateType")
    public Boolean updateType(@RequestBody BooksRankInCodesContentsUpdTypeBO bo) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.updateType(bo);
    }

    @Operation(summary =  "查看链接详情", description = "查看链接详情")
    @GetMapping("/books/rank/in/code/contents/detail")
    public BooksRankInCodesContentsDetailDTO detail(@RequestParam Integer id) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.detail(id);

    }

    @Operation(summary =  "修改强跳连接", description = "修改强跳连接")
    @PostMapping("/books/rank/in/code/contents/forcePromotionUrl")
    public Boolean forcePromotionUrl(@RequestBody BooksRankInCodeContentsUpdForceApiBO bo) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.forcePromotionUrl(bo);
    }

    @Operation(summary =  "移动", description = "移动")
    @PostMapping("/books/rank/in/code/contents/move")
    public Boolean move(@RequestBody BooksRankInCodesContentsMoveBO bo) throws BusinessException{
        return adminBooksRankInCodeContentsRemoteService.move(bo);
    }
}
