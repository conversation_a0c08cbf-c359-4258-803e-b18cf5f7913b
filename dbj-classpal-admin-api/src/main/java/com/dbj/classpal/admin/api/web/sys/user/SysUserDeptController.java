package com.dbj.classpal.admin.api.web.sys.user;


import com.dbj.classpal.admin.common.bo.sys.user.AllocationDeptBO;
import com.dbj.classpal.admin.service.service.sys.user.ISysUserDeptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户与部门关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@RestController
@RequestMapping("/sys-user-dept")
@Tag(name = "用户部门关联信息接口", description = "用户部门关联相关操作")
public class SysUserDeptController {


    @Resource
    private ISysUserDeptService sysUserRoleService;

    @PostMapping("/allocationDept")
    @Operation(summary = "用户分配部门", description = "用户分配部门")
    public Boolean allocationDept(@Validated @RequestBody AllocationDeptBO allocationDeptBO) {
        return sysUserRoleService.allocationDept(allocationDeptBO);
    }
}
