package com.dbj.classpal.admin.api.web.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.service.remote.appchannel.AppChannelRemoteService;
import com.dbj.classpal.app.client.bo.channel.*;
import com.dbj.classpal.app.client.dto.channel.ChannelDetailApiDTO;
import com.dbj.classpal.app.client.dto.version.ChannelDownloadApiDTO;
import com.dbj.classpal.app.client.dto.version.VersionChannelApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * APP渠道管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Tag(name = "渠道管理")
@RestController
@RequestMapping("/api/channel")
public class AppChannelController {

    @Resource
    private AppChannelRemoteService channelService;

    @Operation(summary = "分页查询渠道")
    @PostMapping("/page")
    public Page<ChannelDetailApiDTO> pageChannels(@RequestBody @Valid PageInfo<ChannelQueryApiBO> pageRequest) throws BusinessException {
        return channelService.pageChannels(pageRequest);
    }
    
    @Operation(summary = "新增渠道")
    @PostMapping("/save")
    public Boolean saveChannel(@RequestBody @Valid ChannelSaveApiBO channel) throws BusinessException {
       return channelService.saveChannel(channel);
    }

    @Operation(summary = "修改渠道")
    @PostMapping("/update")
    public Boolean updateChannel(@RequestBody @Valid ChannelUpdateApiBO channel) throws BusinessException {
        return channelService.updateChannel(channel);
    }

    @Operation(summary = "删除渠道")
    @PostMapping("/delete")
    public Boolean deleteChannel(@RequestBody @Valid ChannelIdApiBO channel) throws BusinessException {
        return channelService.deleteChannel(channel);
    }

    @Operation(summary = "启用渠道")
    @PostMapping("/enable")
    public Boolean enableChannel(@RequestBody @Valid ChannelIdApiBO channel) throws BusinessException {
        return channelService.enableChannel(channel);
    }

    @Operation(summary = "禁用渠道")
    @PostMapping("/disable")
    public Boolean disableChannel(@RequestBody @Valid ChannelIdApiBO channel) throws BusinessException {
        return channelService.disableChannel(channel);
    }
    @Operation(summary = "获取二维码")
    @PostMapping("/getQrCode")
    public VersionChannelApiDTO getQrCode(@RequestBody BaseIdBO bo) throws BusinessException {
        return channelService.getQrCode(bo);
    }

    @Operation(summary = "获取渠道详情")
    @PostMapping("/detail")
    public ChannelDetailApiDTO getChannelDetail(@RequestBody ChannelIdApiBO idApiBO) throws BusinessException {
        return channelService.getChannelDetail(idApiBO);
    }
    
    @Operation(summary = "批量启用")
    @PostMapping("/batchEnable")
    public Boolean batchEnable(@RequestBody ChannelIdsApiBO ids) throws BusinessException {
       return channelService.batchEnable(ids);
    }
    
    @Operation(summary = "批量禁用")
    @PostMapping("/batchDisable")
    public Boolean batchDisable(@RequestBody ChannelIdsApiBO ids) throws BusinessException {
       return channelService.batchDisable(ids);
    }
    
    @Operation(summary = "批量删除")
    @PostMapping("/batchDelete")
    public Boolean batchDelete(@RequestBody ChannelIdsApiBO ids) throws BusinessException {
       return channelService.batchDelete(ids);
    }
}
