package com.dbj.classpal.admin.api.web.app;

import com.dbj.classpal.admin.service.remote.appchannelreview.AppChannelReviewRemoteService;
import com.dbj.classpal.app.client.bo.channel.ReviewSaveApiBO;
import com.dbj.classpal.app.client.bo.channel.ReviewUpdateApiBO;
import com.dbj.classpal.app.client.dto.channel.ChannelReviewApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "渠道审核管理")
@RestController
@RequestMapping("/api/channel/review")
public class AppChannelReviewController {

    @Resource
    private AppChannelReviewRemoteService channelReviewService;
    
    @Operation(summary = "新增审核记录")
    @PostMapping("/save")
    public Boolean saveReview(@RequestBody @Valid ReviewSaveApiBO review) throws BusinessException {
         return channelReviewService.saveReview(review);
    }

    @Operation(summary = "修改审核记录")
    @PostMapping("/update")
    public Boolean updateReview(@RequestBody @Valid ReviewUpdateApiBO review) throws BusinessException {
        return channelReviewService.updateReview(review);
    }

    @Operation(summary = "获取审核记录列表")
    @PostMapping("/list")
    public List<ChannelReviewApiDTO> getReviewDetail() throws BusinessException {
        return channelReviewService.list();
    }
} 