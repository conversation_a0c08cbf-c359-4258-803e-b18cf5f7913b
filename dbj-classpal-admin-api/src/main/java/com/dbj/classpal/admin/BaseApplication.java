/*
 * BaseApplication.java Copyright PrinceEgg Tech Co. Ltd. All Rights Reserved.
 */
package com.dbj.classpal.admin;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 应用启动基类
 *
 * <AUTHOR>
 * @version 0.1
 * @since 2018/07/04
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.dbj.classpal.**.client.api"})
@MapperScan("com.dbj.classpal.admin.service.mapper")
@ComponentScan("com.dbj.*")
@EnableKnife4j
public class BaseApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(BaseApplication.class);
//        springApplication.setApplicationContextClass(CoreEmbeddedWebApplicationContext.class);
//        springApplication.setAddCommandLineProperties(true);
        springApplication.run(args);
    }
}
