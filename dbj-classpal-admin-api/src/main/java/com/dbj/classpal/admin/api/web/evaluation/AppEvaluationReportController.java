package com.dbj.classpal.admin.api.web.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.appevaluation.AppEvaluationReportRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminUserPaperEvaluationQueryApiBO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationReportQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationQueryPageApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationController
 * Date:     2025-05-16 16:14:22
 * Description: 表名： ,描述： 表
 */
@Tag(name = "内容管理-评测-评测项-评测报告", description = "内容管理-评测-评测项-评测报告")
@RestController
@RequestMapping("/evaluationReport")
public class AppEvaluationReportController {
    @Resource
    private AppEvaluationReportRemoteService appEvaluationReportRemoteService;

    /**
     * 分页查询该评测表下所有用户的评测报告
     * @param pageRequest
     * @return
     * @throws BusinessException
     */
    @Operation(summary = "分页查询评测报告", description = "分页查询评测报告")
    @PostMapping("/pageReports")
    public Page<AdminUserPaperEvaluationQueryPageApiDTO> pageReports(@RequestBody PageInfo<AdminUserPaperEvaluationQueryApiBO> pageRequest) throws BusinessException{
        return appEvaluationReportRemoteService.pageReports(pageRequest);
    }

    /**
     * 查询某个评测报告详情
     * @param bo
     * @return
     * @throws BusinessException
     */
    @Operation(summary = "查询评测报告详情", description = "查询评测报告详情")
    @PostMapping("/getReportDetail")
    public AdminEvaluationReportQueryApiDTO getReportDetail(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException{
        return appEvaluationReportRemoteService.getReportDetail(bo);
    }

}
