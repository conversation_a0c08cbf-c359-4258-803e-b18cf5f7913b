package com.dbj.classpal.admin.api.web.poem;


import com.dbj.classpal.admin.common.bo.BaseIdBO;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.BaseSortBO;
import com.dbj.classpal.admin.service.remote.poem.AncientPoemReciteCollectionRemoteService;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateCoverUrlBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateDescBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateStatusBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateTitleBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 古诗背诵合集表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Tag(name = "古诗背诵-古诗背诵分类合集管理")
@RestController
@RequestMapping("/ancient-poem-recite-collection")
public class AncientPoemReciteCollectionController {

    @Resource
    private AncientPoemReciteCollectionRemoteService ancientPoemReciteCollectionRemoteService;


    /**
     * 分页查询
     */
    @Operation(summary =  "合集列表查询", description = "合集列表查询")
    @PostMapping("/listAncientPoemReciteCollection")
    public List<AncientPoemReciteCollectionDTO> listAncientPoemReciteCollection(@RequestBody AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO) throws BusinessException {
        return ancientPoemReciteCollectionRemoteService.listAncientPoemReciteCollection(ancientPoemReciteCollectionBO);
    }

    /**
     * 获取详情
     */
    @Operation(summary =  "合集详细", description = "合集详细")
    @PostMapping("/get")
    public AncientPoemReciteCollectionDetailDTO getAncientPoemReciteCollection(@RequestBody BaseIdBO baseIdBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.getAncientPoemReciteCollection(baseIdBO.getId());
    }

    /**
     * 删除
     */
    @Operation(summary =  "批量移除", description = "批量移除")
    @PostMapping("/batchDelete")
    public Boolean deleteAncientPoemReciteCollection(@RequestBody BaseIdsBO baseIdsBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.deleteAncientPoemReciteCollection(baseIdsBO.getIds());
    }
    /**
     * 保存
     */
    @Operation(summary =  "保存", description = "保存")
    @PostMapping("/save")
    public Boolean saveAncientPoemReciteCollection(@RequestBody AncientPoemReciteCollectionSaveBO ancientPoemReciteCollectionSaveBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.saveAncientPoemReciteCollection(ancientPoemReciteCollectionSaveBO);
    }
    @Operation(summary =  "修改名称", description = "修改名称")
    @PostMapping("/updateTitle")
    public Boolean updateTitle(@RequestBody AncientPoemReciteCollectionUpdateTitleBO ancientPoemReciteCollectionUpdateTitleBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.updateTitle(ancientPoemReciteCollectionUpdateTitleBO);
    }
    @Operation(summary =  "修改简介", description = "修改简介")
    @PostMapping("/updateDescription")
    public Boolean updateDescription(@RequestBody AncientPoemReciteCollectionUpdateDescBO ancientPoemReciteCollectionUpdateDescBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.updateDescription(ancientPoemReciteCollectionUpdateDescBO);
    }
    @Operation(summary =  "修改状态", description = "修改状态")
    @PostMapping("/updateStatus")
    public Boolean updateStatus(@RequestBody AncientPoemReciteCollectionUpdateStatusBO ancientPoemReciteCollectionUpdateStatusBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.updateStatus(ancientPoemReciteCollectionUpdateStatusBO);
    }
    @Operation(summary =  "修改封面", description = "修改封面")
    @PostMapping("/updateCoverUrl")
    public Boolean updateCoverUrl(@RequestBody AncientPoemReciteCollectionUpdateCoverUrlBO anotherCollectionUpdateCoverUrlBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.updateCoverUrl(anotherCollectionUpdateCoverUrlBO);
    }
    @Operation(summary =  "修改详情数据", description = "修改详情数据")
    @PostMapping("/update")
    public Boolean update(@RequestBody AncientPoemReciteCollectionUpdateBO ancientPoemReciteCollectionUpdateBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.update(ancientPoemReciteCollectionUpdateBO);
    }
    @Operation(summary =  "排序", description = "排序")
    @PostMapping("/sort")
    public Boolean sort(@RequestBody BaseSortBO<AncientPoemReciteCollectionSortBO> sortBO) throws BusinessException{
        return ancientPoemReciteCollectionRemoteService.sort(sortBO.getSorts());
    }

}
