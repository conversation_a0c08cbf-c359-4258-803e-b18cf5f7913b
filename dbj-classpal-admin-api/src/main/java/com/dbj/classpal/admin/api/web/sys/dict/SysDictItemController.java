package com.dbj.classpal.admin.api.web.sys.dict;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemDetailBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemSaveBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemUpdBO;
import com.dbj.classpal.admin.common.bo.sys.dict.SysDictItemUpdStatusBO;
import com.dbj.classpal.admin.common.dto.sys.dict.SysDictItemDTO;
import com.dbj.classpal.admin.service.service.sys.dict.ISysDictItemService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 数据字典项表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/sys-dict-item")
@Tag(name = "字典标签信息接口", description = "字典标签相关操作")
public class SysDictItemController {

    @Resource
    private ISysDictItemService SysDictItemService;

    /**
     * 获取单个SysDictItem数据
     * @Title: saveSysDictItem
     * @Description: 添加SysDictItem数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "获取单个SysDictItem数据")
    @RequestMapping(value = "/getSysDictItemInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public SysDictItemDTO getSysDictItemInfo(@Validated @RequestBody SysDictItemDetailBO reqBo) throws BusinessException {
        return this.SysDictItemService.getSysDictItemInfo(reqBo);
    }

    /**
     * 添加SysDictItem数据
     * @Title: saveSysDictItem
     * @Description: 添加SysDictItem数据
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "添加SysDictItem数据")
    @RequestMapping(value = "/saveSysDictItem", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean saveSysDictItem(@Validated @RequestBody SysDictItemSaveBO bo) throws BusinessException {
        return this.SysDictItemService.saveSysDictItem(bo);
    }

    /**
     * 修改数据
     * @Title: UpdateSysDictItem
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param bo
     * @return
     * @date: 2022年10月20日
     * @throws
     */
    @Operation(summary =  "修改数据")
    @RequestMapping(value = "/updateSysDictItem", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateSysDictItem(@Validated @RequestBody SysDictItemUpdBO bo) throws BusinessException {
        return this.SysDictItemService.updateSysDictItem(bo);
    }


    /**
     * 删除部门数据
     * @Title: delSysDictItemInfo
     * @Description: 添加SysDictItem数据
     * @param reqBo
     * @return
     * @date: 2022年10月20日
     * @author: Kang Liu
     * @throws
     */
    @Operation(summary =  "删除部门数据")
    @RequestMapping(value = "/batchDelSysDictItemInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean batchDelSysDictItemInfo(@Validated @RequestBody SysDictItemUpdStatusBO reqBo) throws BusinessException {
        return this.SysDictItemService.batchDelSysDictItemInfo(reqBo);
    }
    /**
     * <AUTHOR>
     * @Description  批量修改状态
     * @Date 2025/3/17 10:57 
     * @param 
     * @return 
     **/
    @Operation(summary =  "批量修改状态")
    @RequestMapping(value = "/batchUpdStatus", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean batchUpdStatus(@Validated @RequestBody SysDictItemUpdStatusBO reqBo) throws BusinessException {
        return this.SysDictItemService.batchUpdStatus(reqBo);
    }

    /**
     * <AUTHOR>
     * @Description  分页获取字典数据
     * @Date 2025/3/17 9:22 
     * @param page
     * @return Page
     **/
    @Operation(summary =  "分页获取字典数据")
    @RequestMapping(value = "/pageSysDictItemInfo", method = RequestMethod.POST, consumes = MediaType.ALL_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<SysDictItemDTO> pageSysDictItemInfo(@Validated @RequestBody PageInfo<SysDictItemBO> page) throws BusinessException {
        return this.SysDictItemService.pageSysDictItemInfo(page);
    }
}
