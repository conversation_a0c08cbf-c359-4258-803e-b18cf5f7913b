package com.dbj.classpal.admin.api.web.audio;


import com.dbj.classpal.admin.service.remote.audio.AudioClassifyRemoteService;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyMoveBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyQueryBO;
import com.dbj.classpal.books.client.dto.audio.AudioClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 音频分类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@Tag(name = "音频-分类")
@RequiredArgsConstructor
@RequestMapping("/audio-classify")
public class AudioClassifyController {

    private final AudioClassifyRemoteService audioClassifyRemoteService;

    @Operation(summary = "保存分类")
    @PostMapping("/save")
    public Boolean save(@Validated @RequestBody AudioClassifyAddBO bo) throws BusinessException {
        return audioClassifyRemoteService.save(bo);
    }

    @Operation(summary = "删除分类")
    @PostMapping("/remove")
    public Boolean remove(@Validated @RequestBody AudioClassifyDelBO bo) throws BusinessException {
        return audioClassifyRemoteService.remove(bo);
    }

    @Operation(summary = "移动")
    @PostMapping("/move")
    public Boolean remove(@Validated @RequestBody AudioClassifyMoveBO bo) throws BusinessException {
        return audioClassifyRemoteService.move(bo);
    }

    @Operation(summary = "分类列表")
    @PostMapping("/list")
    public List<AudioClassifyDTO> list(@Validated @RequestBody AudioClassifyQueryBO bo) throws BusinessException {
        return audioClassifyRemoteService.list(bo);
    }
}
