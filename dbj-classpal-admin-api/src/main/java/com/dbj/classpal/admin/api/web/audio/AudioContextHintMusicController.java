package com.dbj.classpal.admin.api.web.audio;


import com.dbj.classpal.admin.service.remote.audio.AudioContextHintMusicRemoteService;
import com.dbj.classpal.books.client.bo.audio.AudioBackgroundBO;
import com.dbj.classpal.books.client.bo.audio.AudioContextHintAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.books.client.dto.audio.AudioContextHintDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 音频文本提示音 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Tag(name = "新增音频-提示音", description = "新增音频-提示音管理")
@RestController
@RequestMapping("/audio-context-hint-music")
public class AudioContextHintMusicController {

    @Autowired
    private AudioContextHintMusicRemoteService audioContextHintMusicRemoteService;

    @Operation(summary = "新增", description = "新增提示音")
    @PostMapping("/save")
    public Integer save(@Valid @RequestBody List<AudioContextHintAddBO> bo) throws BusinessException {
        return audioContextHintMusicRemoteService.save(bo);
    }

    @Operation(summary = "重排序", description = "重排序提示音音")
    @PostMapping("/reorder")
    public Integer reorder(@Valid @RequestBody List<Integer> bo) throws BusinessException {
        return audioContextHintMusicRemoteService.reorder(bo);
    }

    @Operation(summary = "查询自定义提示音", description = "查询自定义提示音列表")
    @PostMapping("/getDefinitionHint")
    public List<AudioContextHintDTO> getDefinitionHint(@Valid @RequestBody AudioTypeBO bo) throws BusinessException {
        return audioContextHintMusicRemoteService.getDefinitionHint(bo);
    }
}
