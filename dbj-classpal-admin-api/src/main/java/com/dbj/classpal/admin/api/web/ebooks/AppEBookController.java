package com.dbj.classpal.admin.api.web.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.ebooks.AppEBookRemoteService;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.bo.share.GetShareInfoApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookAsyncProcessApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.PdfTaskStatusApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.ShareUrlResultApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/app-ebook")
@Tag(name = "电子样书-单书", description = "电子样书-单书")
public class AppEBookController {

    @Resource
    private AppEBookRemoteService appEBookRemoteService;

    @Operation(summary = "查询单书列表", description = "查询单书列表")
    @PostMapping("/pageInfo")
    public Page<AppEBookApiDTO> page(@RequestBody PageInfo<AppEBookQueryApiBO> pageRequest) throws BusinessException {
        return appEBookRemoteService.page(pageRequest);
    }

    @Operation(summary = "查看详情", description = "查看详情")
    @PostMapping("/detail")
    public AppEBookApiDTO detail(@RequestBody @Valid AppEBookIdApiBO idBO) throws BusinessException{
        return appEBookRemoteService.detail(idBO);
    }
    @Operation(summary = "添加单书", description = "添加单书")
    @PostMapping("/save")
    public Integer save(@RequestBody @Valid AppEBookSaveApiBO saveBO) throws BusinessException{
        return appEBookRemoteService.save(saveBO);
    }

    @Operation(summary = "修改单书", description = "修改单书")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid AppEBookUpdateApiBO saveBO) throws BusinessException{
        return appEBookRemoteService.update(saveBO);
    }

    @Operation(summary = "删除单书", description = "删除单书")
    @PostMapping("/delete")
    public Boolean delete(@RequestBody @Valid AppEBookIdApiBO idBO) throws BusinessException{
        return appEBookRemoteService.delete(idBO);
    }

    @Operation(summary = "批量删除", description = "批量删除")
    @PostMapping("/deleteBatch")
    public Boolean deleteBatch(@RequestBody @Valid AppEBookIdsApiBO idsBO) throws BusinessException{
        return appEBookRemoteService.deleteBatch(idsBO);
    }

    @Operation(summary = "批量启用", description = "批量启用")
    @PostMapping("/enableBatch")
    public Boolean enableBatch(@RequestBody @Valid AppEBookIdsApiBO idsBO) throws BusinessException{
        return appEBookRemoteService.enableBatch(idsBO);
    }

    @Operation(summary = "批量禁用", description = "批量禁用")
    @PostMapping("/disableBatch")
    public Boolean disableBatch(@RequestBody @Valid AppEBookIdsApiBO idsBO) throws BusinessException{
        return appEBookRemoteService.disableBatch(idsBO);
    }

    @Operation(summary = "批量允许下载", description = "批量允许下载")
    @PostMapping("/allowDownloadBatch")
    public Boolean allowDownloadBatch(@RequestBody @Valid AppEBookIdsApiBO idsBO) throws BusinessException{
        return appEBookRemoteService.allowDownloadBatch(idsBO);
    }

    @Operation(summary = "批量禁用下载", description = "批量禁用下载")
    @PostMapping("/disableDownloadBatch")
    public Boolean disableDownloadBatch(@RequestBody @Valid AppEBookIdsApiBO idsBO) throws BusinessException{
        return appEBookRemoteService.disableDownloadBatch(idsBO);
    }

    @Operation(summary = "修改单书文件", description = "修改单书文件")
    @PostMapping("/updateFile")
    public Boolean updateFile(@RequestBody @Valid AppEBookUpdateFileApiBO updateFileBO) throws BusinessException{
        return appEBookRemoteService.updateFile(updateFileBO);
    }

    @Operation(summary = "修改水印", description = "修改水印")
    @PostMapping("/updateWatermark")
    public Boolean updateWatermark(@RequestBody @Valid AppEBookUpdateWatermarkApiBO updateWatermarkBO) throws BusinessException{
        return appEBookRemoteService.updateWatermark(updateWatermarkBO);
    }

    @Operation(summary = "获取封面", description = "获取封面")
    @PostMapping("/coverUrl")
    public String coverUrl(@RequestBody @Valid AppEBookFileApiBO fileApiBO) throws BusinessException{
        return appEBookRemoteService.coverUrl(fileApiBO);
    }

    /**
     * 异步处理PDF文件（获取封面和切图）
     *
     * @param request 异步处理请求参数
     * @return 任务ID和基本信息
     */
    @Operation(summary = "获取封面和切图", description = "获取封面和切图")
    @PostMapping("/async/cover-url")
    public AppEBookAsyncProcessApiDTO asyncCoverUrl(@RequestBody @Validated AppEBookAsyncProcessApiBO request) throws BusinessException{
        return appEBookRemoteService.asyncCoverUrl(request);
    }

    /**
     * 查询PDF处理任务状态
     *
     * @param taskApiBO 任务ID
     * @return 任务状态信息
     */
    @Operation(summary = "查询PDF处理任务状态", description = "查询PDF处理任务状态")
    @PostMapping("/async/task/get")
    RestResponse<PdfTaskStatusApiDTO> getTaskStatus(@RequestBody AppEBookAsyncProcessTaskApiBO taskApiBO) throws BusinessException{
        return RestResponse.success(appEBookRemoteService.getTaskStatus(taskApiBO));
    }

    /**
     * 重新处理PDF文件（重新获取封面和切图）
     *
     * @param request 重新处理请求参数
     * @return 新的任务ID和基本信息
     */
    @Operation(summary = "重新处理PDF文件", description = "重新处理PDF文件")
    @PostMapping("/async/reprocess")
    RestResponse<AppEBookAsyncProcessApiDTO> reprocess(@RequestBody @Validated AppEBookReprocessApiBO request) throws BusinessException{
        return RestResponse.success(appEBookRemoteService.reprocess(request));
    }


    @Operation(summary = "获取分享信息", description = "获取分享链接和二维码，不存在时自动生成")
    @PostMapping("/share/info")
    ShareUrlResultApiDTO getShareInfo(@RequestBody @Validated GetShareInfoApiBO request) throws BusinessException{
        return appEBookRemoteService.getShareInfo(request);
    }

}
