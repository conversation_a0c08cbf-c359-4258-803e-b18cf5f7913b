package com.dbj.classpal.admin.api.web.poem;


import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.BaseSortBO;
import com.dbj.classpal.admin.service.remote.poem.AncientPoemBusinessRefRemoteService;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSortBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 古诗文背诵分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Tag(name = "古诗关联业务管理")
@RestController
@RequestMapping("/ancient-poem-business-ref")
public class AncientPoemBusinessRefController {


    @Resource
    private AncientPoemBusinessRefRemoteService ancientPoemBusinessRefService;

    @Operation(summary =  "列表查询", description = "列表查询")
    @PostMapping("/listAncientPoemBusinessRef")
    public List<AncientPoemBusinessRefListDTO> listAncientPoemBusinessRef(@RequestBody AncientPoemBusinessRefListBO anAncientPoemBusinessRefList) throws BusinessException {
        return ancientPoemBusinessRefService.listAncientPoemBusinessRef(anAncientPoemBusinessRefList);
    }

    /**
     * 保存
     */
    @Operation(summary =  "批量保存", description = "批量保存")
    @PostMapping("/batchSave")
    public Boolean batchSave(@RequestBody AncientPoemBusinessRefSaveBO anotherPoemBusinessRefSaveBO) throws BusinessException{
        return ancientPoemBusinessRefService.batchSave(anotherPoemBusinessRefSaveBO);
    }
    /**
     * 删除
     */
    @Operation(summary =  "批量删除", description = "批量删除")
    @PostMapping("/batchDelete")
    public Boolean batchDelete(@RequestBody BaseIdsBO baseIdsBO) throws BusinessException {
        return ancientPoemBusinessRefService.batchDelete(baseIdsBO.getIds());
    }
    /**
     * 排序
     */
    @Operation(summary =  "排序", description = "排序")
    @PostMapping("/batchSort")
    public Boolean batchSort(@RequestBody BaseSortBO<AncientPoemBusinessRefSortBO> sortBO) throws BusinessException {
        return ancientPoemBusinessRefService.batchSort(sortBO.getSorts());
    }

}
