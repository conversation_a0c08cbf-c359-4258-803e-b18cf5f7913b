package com.dbj.classpal.admin.api.web.sys.role;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.common.bo.BaseIdsBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRolePageBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleSaveBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleUpdBO;
import com.dbj.classpal.admin.common.bo.sys.role.SysRoleUpdStatusBO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRoleDTO;
import com.dbj.classpal.admin.common.dto.sys.role.SysRolePageDTO;
import com.dbj.classpal.admin.service.service.sys.role.ISysRoleService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Copyright (C), 2017-2020, cn.zlinks
 * FileName: SysRoleController
 * Date:     2024-2-2 13:36:06
 * Description: 控制层
 * <AUTHOR> Liu
 */
@RestController
@RequestMapping("/sys-role")
@Tag(name = "角色信息接口", description = "角色相关操作")
public class SysRoleController {

	@Resource
	private ISysRoleService sysRoleService;

	/**
	 * <AUTHOR>
	 * @Description  获取单个角色信息
	 * @Date 2025/3/17 15:54
	 * @param id 主键id
	 * @return SysRoleDTO 角色信息
	 **/
	@Operation(summary = "获取单个角色信息")
    @GetMapping(value = "/getSysRoleInfo")
    public SysRoleDTO getSysRoleInfo(@Parameter(description = "角色id") @RequestParam Integer id) throws BusinessException {
        return this.sysRoleService.getSysRoleInfo(id);
    }

	/**
	 * 添加SysRole数据
	 * @Title: saveSysRole
	 * @Description: 添加SysRole数据
	 * @param bo
	 * @return
	 * @date: 2022年10月20日
	 * @author: Kang Liu
	 * @throws
	 */
	@Operation(summary = "添加角色数据")
	@PostMapping(value = "/saveSysRole")
	public Boolean saveSysRole(@Validated @RequestBody SysRoleSaveBO bo) throws BusinessException {
		return this.sysRoleService.saveSysRole(bo);

	}

	/**
	 * 修改数据
	 * @Title: UpdateSysRole
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param bo
	 * @return
	 * @date: 2022年10月20日
	 * @throws
	 */
	@Operation(summary = "修改角色数据")
	@PostMapping(value = "/updateSysRole")
	public Boolean updateSysRole(@Validated @RequestBody SysRoleUpdBO bo) throws BusinessException {
		return this.sysRoleService.updateSysRole(bo);
	}
	/**
	 * 修改角色状态
	 * @Title: UpdateSysRole
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param bo
	 * @return
	 * @date: 2022年10月20日
	 * @throws
	 */
	@Operation(summary = "修改角色状态")
	@PostMapping(value = "/updateSysRoleStatus")
	public Boolean updateSysRoleStatus(@Validated @RequestBody SysRoleUpdStatusBO bo) throws BusinessException {
		return this.sysRoleService.updateSysRoleStatus(bo);
	}

	/**
	 * 获取单个SysRole数据
	 * @Title: saveSysRole
	 * @Description: 添加SysRole数据
	 * @param ids
	 * @return
	 * @date: 2022年10月20日
	 * @author: Kang Liu
	 * @throws
	 */
	@Operation(summary = "删除角色")
	@PostMapping(value = "/delSysRoleInfo")
	public Boolean delSysRoleInfo(@Validated @RequestBody BaseIdsBO baseIdsBO) throws BusinessException {
		return this.sysRoleService.delSysRoleInfo(baseIdsBO);
	}

	/**
	 * 分页查询数据
	 * @Title: getSysRoleAll
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @return
	 * @date: 2022年10月20日
	 * @throws
	 */
	@Operation(summary = "分页查询数据")
	@PostMapping(value = "/pageSysRole")
	public Page<SysRolePageDTO> pageSysRole(@Validated @RequestBody PageInfo<SysRolePageBO> bo){
		return this.sysRoleService.pageSysRole(bo);
	}
	/**
	 * 查询所有角色
	 * @Title: getSysRoleAll
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @return
	 * @date: 2022年10月20日
	 * @throws
	 */
	@Operation(summary = "查询所有角色")
	@PostMapping(value = "/getSysRoleAll")
	public List<SysRolePageDTO> getSysRoleAll(@Validated @RequestBody SysRolePageBO bo){
		return this.sysRoleService.getSysRoleAll(bo);
	}

}
