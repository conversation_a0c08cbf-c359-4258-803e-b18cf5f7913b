//package com.dbj.classpal.admin.api.web.login;
//
//import cn.hutool.json.JSON;
//import cn.hutool.json.JSONUtil;
//import com.fasterxml.jackson.databind.ObjectMapper;
//
//import java.io.File;
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.sql.Statement;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @program dbj-classpal-admin-bus
// * @className RegionImporter
// * @description
// * @date 2025-03-25 08:50
// **/
//public class RegionImporter {
//
//        private static final String DB_URL = "jdbc:mysql://***************:3306/classpal_app";
//        private static final String USER = "dbj_java";
//        private static final String PASSWORD = "kTQWf71YZvm4ap3i";
//
//
//        public static void main(String[] args) {
//            try {
//                // 1. 初始化数据库
//                Connection conn = DriverManager.getConnection(DB_URL, USER, PASSWORD);
//                // 2. 读取JSON文件
//                ObjectMapper mapper = new ObjectMapper();
////                Region root =  JSONUtil.parse(json,Region.class);
//                Region root = mapper.readValue(new File("C:\\Users\\<USER>\\Documents\\WXWork\\1688857890500199\\Cache\\File\\2025-03\\regions.json"), Region.class);
//
//                // 3. 插入数据到数据库
//                Map<String, Integer> regionMap = new HashMap<>();
//                insertRegion(root, null, 1, conn, regionMap);
//
//                conn.close();
//                System.out.println("Data imported successfully!");
//
//            } catch (Exception e) {
//                log.error(e.getMessage());
//            }
//        }
//
//        private static void insertRegion(Region region, Integer parentId, int level,
//                                         Connection conn, Map<String, Integer> regionMap) throws SQLException {
//            // 插入当前节点
//            String insertSql = "INSERT INTO regions (name, parent_id, level, adcode, citycode) " +
//                    "VALUES (?, ?, ?, ?, ?)";
//            try (PreparedStatement pstmt = conn.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS)) {
//                pstmt.setString(1, region.getName());
//                pstmt.setObject(2, parentId);
//                pstmt.setInt(3, level);
//                pstmt.setString(4, region.getAdcode());
//                pstmt.setString(5, region.getCitycode());
//
//                pstmt.executeUpdate();
//
//                // 获取生成的主键
//                try (ResultSet rs = pstmt.getGeneratedKeys()) {
//                    if (rs.next()) {
//                        int regionId = rs.getInt(1);
//
//                        // 缓存当前节点
//                        regionMap.put(region.getAdcode(), regionId);
//
//                        // 递归处理子节点
//                        if (region.getChildren() != null) {
//                            for (Region child : region.getChildren()) {
//                                insertRegion(child, regionId, level + 1, conn, regionMap);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//        // JSON数据对应的Java类
//        static class Region {
//            private String name;
//            private String adcode;
//            private String citycode;
//            private List<Region> children;
//
//            // Getters and Setters
//            public String getName() { return name; }
//            public void setName(String name) { this.name = name; }
//            public String getAdcode() { return adcode; }
//            public void setAdcode(String adcode) { this.adcode = adcode; }
//            public String getCitycode() { return citycode; }
//            public void setCitycode(String citycode) { this.citycode = citycode; }
//            public List<Region> getChildren() { return children; }
//            public void setChildren(List<Region> children) { this.children = children; }
//        }
//}
