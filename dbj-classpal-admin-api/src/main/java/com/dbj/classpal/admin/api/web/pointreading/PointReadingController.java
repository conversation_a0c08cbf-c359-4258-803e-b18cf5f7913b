package com.dbj.classpal.admin.api.web.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.service.remote.pointreading.PointReadingRemoteService;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookUpdateApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingBookApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 点读管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-XX
 */
@Tag(name = "点读管理", description = "点读相关接口")
@RestController
@RequestMapping("/point-reading")
public class PointReadingController {

    @Resource
    private PointReadingRemoteService pointReadingRemoteService;

    @Operation(summary = "分页查询点读列表", description = "分页查询点读列表")
    @PostMapping("/pageInfo")
    public Page<PointReadingBookApiDTO> pageInfo(@RequestBody @Valid PageInfo<PointReadingBookQueryApiBO> pageRequest) throws BusinessException {
        return pointReadingRemoteService.pageInfo(pageRequest);
    }

    @Operation(summary = "新增点读", description = "新增点读")
    @PostMapping("/save")
    public Integer save(@RequestBody @Valid PointReadingBookSaveApiBO saveBO) throws BusinessException {
        return pointReadingRemoteService.save(saveBO);
    }

    @Operation(summary = "修改点读", description = "修改点读")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid PointReadingBookUpdateApiBO updateBO) throws BusinessException {
        return pointReadingRemoteService.update(updateBO);
    }

    @Operation(summary = "查看详情", description = "查看详情")
    @GetMapping("/detail")
    public PointReadingBookApiDTO detail(@RequestParam Integer id) throws BusinessException {
        return pointReadingRemoteService.detail(id);
    }

    @Operation(summary = "删除点读", description = "删除点读")
    @PostMapping("/delete")
    public Boolean delete(@RequestParam Integer id) throws BusinessException {
        return pointReadingRemoteService.delete(id);
    }
}