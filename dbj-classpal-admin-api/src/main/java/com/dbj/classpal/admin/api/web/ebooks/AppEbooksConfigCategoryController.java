package com.dbj.classpal.admin.api.web.ebooks;

import com.dbj.classpal.admin.service.remote.appebooksconfig.AppEbooksConfigCategoryRemoteService;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategorySaveApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryUpdateApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigCategoryQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/app-ebooks-config-category")
@Tag(name = "电子样书-样书配置-图书分类", description = "电子样书-样书配置-图书分类")
public class AppEbooksConfigCategoryController {
    @Resource
    private AppEbooksConfigCategoryRemoteService appEbooksConfigCategoryRemoteService;

    @Operation(summary = "查询图书分类列表", description = "查询图书分类列表")
    @PostMapping("/getAllCategory")
    public List<AppEbooksConfigCategoryQueryApiDTO> getAllCategory(@Validated @RequestBody AppEbooksConfigCategoryQueryApiBO pageInfo) throws BusinessException {
        return appEbooksConfigCategoryRemoteService.getAllCategory(pageInfo);
    }

    @Operation(summary = "新增图书分类", description = "新增图书分类")
    @PostMapping("/saveCategory")
    public Boolean saveCategory(@Validated @RequestBody AppEbooksConfigCategorySaveApiBO bo) throws BusinessException {
        return appEbooksConfigCategoryRemoteService.saveCategory(bo);
    }

    @Operation(summary = "修改图书分类", description = "修改图书分类")
    @PostMapping("/updateCategory")
    public Boolean updateCategory(@Validated @RequestBody AppEbooksConfigCategoryUpdateApiBO bo) throws BusinessException {
        return appEbooksConfigCategoryRemoteService.updateCategory(bo);
    }

    @Operation(summary = "删除图书分类", description = "删除图书分类")
    @PostMapping("/deleteCategory")
    public Boolean deleteCategory(@Validated @RequestBody CommonIdsApiBO bo) throws BusinessException {
        return appEbooksConfigCategoryRemoteService.deleteCategory(bo);
    }
}
