package com.dbj.classpal.admin.api.web.audio;


import com.dbj.classpal.admin.service.remote.audio.AudioSpeakerRemoteService;
import com.dbj.classpal.books.client.bo.audio.AudioSpeakerBO;
import com.dbj.classpal.books.client.dto.audio.AudioSpeakerDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 发音人配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Tag(name = "音频-发音人", description = "音频-发音人管理")
@RestController
@RequestMapping("/audio-speaker")
public class AudioSpeakerController {

    @Autowired
    private AudioSpeakerRemoteService audioSpeakerRemoteService;


    @Operation(summary =  "发音人", description = "查询发音人列表")
    @PostMapping("/list")
    public List<AudioSpeakerDTO> list(@RequestBody AudioSpeakerBO bo) throws BusinessException {
        return audioSpeakerRemoteService.list(bo);
    }

}
