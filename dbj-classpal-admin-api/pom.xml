<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-admin-bus</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>dbj-classpal-admin-api</artifactId>
    <name>dbj-classpal-admin-api</name>

    <properties>
        <project.main.class>com.dbj.classpal.admin.BaseApplication</project.main.class>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-admin-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-admin-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>${project.main.class}</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>