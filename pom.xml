<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-framework</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dbj-classpal-admin-bus</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>dbj-classpal-admin-bus</name>
    <description>dbj-classpal-admin-bus</description>
    <modules>
        <module>dbj-classpal-admin-api</module>
        <module>dbj-classpal-admin-common</module>
        <module>dbj-classpal-admin-service</module>
        <module>dbj-classpal-admin-client</module>
    </modules>
    <properties>
    </properties>
    <dependencies>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-commons-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-mybatis-plus-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-admin-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-admin-service</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-admin-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-admin-common</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


    <distributionManagement>
        <repository>
            <!--id的名字可以任意取，但是在setting文件中的属性<server>的ID与这里一致 -->
            <id>releases</id>
            <!--指向仓库类型为host(宿主仓库）的储存类型为Release的仓库 -->
            <url>http://************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <!--指向仓库类型为host(宿主仓库）的储存类型为Snapshot的仓库 -->
            <url>http://************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>