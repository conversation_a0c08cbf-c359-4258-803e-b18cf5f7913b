
package com.dbj.classpal.admin.common.bo.sys.menu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysMenu
* Date:     2024-2-2 13:36:06
* Description: 描述：菜单信息表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysMenuUpdVisibleBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 菜单ID
	 */
	@Schema(description ="菜单ID")
	private Integer id;
	/**
	 * 菜单状态（0显示 1隐藏）
	 */
	@Schema(description ="菜单状态（0显示 1隐藏）")
	private Integer visible;

}
