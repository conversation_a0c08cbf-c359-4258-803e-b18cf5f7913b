package com.dbj.classpal.admin.common.bo.sys.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据字典主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysDict对象", description="数据字典主表")
public class SysDictUpdStatusBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @NotEmpty
    @Schema(description ="主键id")
    private List<Integer> ids;
    @NotNull
    @Schema(description ="启用状态 1-启用 0-停用")
    private Integer status;


}
