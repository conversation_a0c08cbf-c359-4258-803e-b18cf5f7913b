
package com.dbj.classpal.admin.common.bo.sys.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysRole
* Date:     2024-2-2 13:36:06
* Description: 描述：角色信息表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysRoleUpdBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 角色ID
	 */
	@Schema(description ="角色ID")
	@NotNull(message = "角色ID不能为空")
	private Integer id;

	/**
	 * 角色名称
	 */
	@Schema(description ="角色名称")
	@NotEmpty(message = "角色名称不能为空")
	private String name;
	/**
	 * 角色权限字符串
	 */
	@Schema(description ="角色描述")
	private String describes;
	/**
	 * 显示顺序
	 */
	@Schema(description ="显示顺序")
	private Integer roleSort;

	@Schema(description = "角色状态 0 禁用 1 启用")
	private Integer roleStatus;

	/**
	 * 菜单id集合
	 */
	@Schema(description ="菜单id集合")
	@NotEmpty(message = "菜单权限不能为空")
	private List<Integer> menuIds;


}
