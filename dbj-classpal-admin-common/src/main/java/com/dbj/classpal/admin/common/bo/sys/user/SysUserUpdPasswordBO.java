package com.dbj.classpal.admin.common.bo.sys.user;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name = "SysUser对象", description="用户表")
public class SysUserUpdPasswordBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 旧密码
     */
    @Schema(description ="旧密码")
    private String oldPassword;
    /**
     * 新密码
     */
    @Schema(description ="新密码")
    private String newPassword;
    /**
     * 密码
     */
    @Schema(description ="确认密码")
    private String validatePassword;

}
