package com.dbj.classpal.admin.common.bo.sys.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数据字典项表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysDictItem对象", description="数据字典项表")
public class SysDictItemBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "所属字典ID")
    @NotNull(message = "所属字典ID不能为空")
    private Long dictId;

    @Schema(description = "字典项名称")
    private String itemName;

    @Schema(description = "字典项标识")
    private String itemCode;

    @Schema(description = "字典项值（支持JSON格式扩展）")
    private String itemValue;

    @Schema(description = "启用状态")
    private Integer status;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "备注说明")
    private String remark;

}
