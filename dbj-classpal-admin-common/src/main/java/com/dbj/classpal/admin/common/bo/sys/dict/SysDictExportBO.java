package com.dbj.classpal.admin.common.bo.sys.dict;

import com.dbj.classpal.framework.utils.annotation.ExportExcel;
import com.dbj.classpal.framework.utils.annotation.ExportExcelColumn;
import com.dbj.classpal.framework.utils.annotation.ImportExcelColumn;
import com.dbj.classpal.framework.utils.dto.CommonExcelBO;
import com.dbj.classpal.framework.utils.dto.CommonExportExcelBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/24 08:45:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ExportExcel(sheetName = "字典")
public class SysDictExportBO extends CommonExportExcelBO implements Serializable {


    @Schema(description ="字典类型")
    @ExportExcelColumn(name = "字典类型",kv = "1-字典,2-标签" )
    public Integer dictType;

    @Schema(description ="字典名称")
    @ExportExcelColumn(name = "字典名称")
    public String dictName;

    @Schema(description ="字典标识")
    @ExportExcelColumn(name = "字典标识")
    public String dictCode;


    @Schema(description ="字典状态")
    @ExportExcelColumn(name = "字典状态",kv = "1-启用,0-禁用")
    public Integer status;

    @Schema(description ="字典备注")
    @ExportExcelColumn(name = "字典备注")
    public String description;

    @Schema(description ="标签名称")
    @ExportExcelColumn(name = "标签名称")
    public String itemName;

    @Schema(description ="数据键值")
    @ExportExcelColumn(name = "数据键值")
    public String itemValue;

    @Schema(description ="标签排序权重")
    @ExportExcelColumn(name = "标签排序权重")
    public Integer sort;

    @Schema(description ="标签状态")
    @ExportExcelColumn(name = "标签状态",kv = "1-启用,0-禁用")
    public Integer itemStatus;

    @Schema(description ="标签备注")
    @ExportExcelColumn(name = "标签备注")
    public String remark;

    private int rowNum;
}
