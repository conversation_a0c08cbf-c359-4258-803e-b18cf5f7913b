
package com.dbj.classpal.admin.common.dto.login;

import com.dbj.classpal.admin.common.dto.sys.menu.SysMenuDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysUser
* Date:     2024-2-2 13:36:06
* Description: 描述：用户表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysLoginUserDTO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@Schema(description ="主键id")
	private Integer id;
	/**
	 * 部门ID
	 */
	@Schema(description ="部门ID")
	private Integer deptId;
	/**
	 * 手机号码
	 */
	@Schema(description ="手机号码")
	private String phone;
	/**
	 * 用户昵称
	 */
	@Schema(description ="用户昵称")
	private String nickName;
	/**
	 * 用户邮箱
	 */
	@Schema(description ="用户邮箱")
	private String email;

	/**
	 * 用户性别 0-男 1-女
	 */
	@Schema(description ="用户性别 0-男 1-女")
	private Integer sex;
	/**
	 * 是否强制修改密码 0-否 1-是
	 */
	@Schema(description ="是否强制修改密码 0-否 1-是")
	private Integer isForce;
	/**
	 * 头像路径
	 */
	@Schema(description ="头像路径")
	private String avatar;
	/**
	 * 帐号
	 */
	@Schema(description ="帐号")
	private String accounts;

	/**
	 * 最后登录时间
	 */
	@Schema(description ="最后登录时间")
	private LocalDateTime lastLoginTime;
	/**
	 * 备注
	 */
	@Schema(description ="备注")
	private String remarks;
	/**
	 * 是否超管 0 否 1是
	 */
	@Schema(description ="是否超管 0 否 1是")
	private Integer isAdministrators;

	@Schema(description ="")
	private String token;


	@Schema(description ="是否管理员")
	private Boolean isAdmin;


	@Schema(description ="菜单列表")
	private List<SysMenuDTO> menuDTOS;

	@Schema(description ="按钮权限")
	private List<ButtonAuth> buttonAuthCodes;
}
