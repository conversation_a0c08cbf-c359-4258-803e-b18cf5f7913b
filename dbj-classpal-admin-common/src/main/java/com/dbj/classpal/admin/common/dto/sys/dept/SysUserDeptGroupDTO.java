
package com.dbj.classpal.admin.common.dto.sys.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysUser
* Date:     2024-2-2 13:36:06
* Description: 描述：用户表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysUserDeptGroupDTO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 数量
	 */
	@Schema(description ="数量")
	private Integer num;
	/**
	 * 部门ID
	 */
	@Schema(description ="部门ID")
	private Integer deptId;

}
