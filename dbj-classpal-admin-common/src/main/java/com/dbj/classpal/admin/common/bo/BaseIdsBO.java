package com.dbj.classpal.admin.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className BaseIdsBO
 * @description
 * @date 2025-03-18 13:58
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class BaseIdsBO implements Serializable {

    @Schema(description ="主键ids")
    @NotEmpty(message = "参数不能为空")
    private List<Integer> ids;
}
