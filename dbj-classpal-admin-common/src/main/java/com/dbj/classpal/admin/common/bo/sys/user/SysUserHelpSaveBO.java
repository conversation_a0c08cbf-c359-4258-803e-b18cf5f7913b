package com.dbj.classpal.admin.common.bo.sys.user;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户与部门关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name = "SysUserHelp对象", description="用户与部门关系表")
public class SysUserHelpSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description ="是否折叠 0 否 1 是")
    private Integer isFold;

    @Schema(description ="帮助文档id")
    @NotNull(message = "参数不能为空")
    private Integer helpId;

}
