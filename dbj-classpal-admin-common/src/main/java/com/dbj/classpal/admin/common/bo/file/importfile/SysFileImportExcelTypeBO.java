
package com.dbj.classpal.admin.common.bo.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: File
* Date:     2023-10-27 9:44:06
* Description: 描述：
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysFileImportExcelTypeBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@Schema(description = "上传文件业务类型")
	@NotEmpty(message = "参数不能为空")
	private String type;


}
