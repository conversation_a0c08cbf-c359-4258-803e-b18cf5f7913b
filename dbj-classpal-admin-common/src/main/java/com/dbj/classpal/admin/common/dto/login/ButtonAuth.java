package com.dbj.classpal.admin.common.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ButtonAuth
 * @description
 * @date 2025-03-20 16:27
 **/
@Tag(name="按钮权限",description = "按钮权限")
@Data
public class ButtonAuth {
    @Schema(name = "主键id")
    private Integer id;
    /**
     * 权限标识
     */
    @Schema(description ="权限标识")
    private String perms;
}
