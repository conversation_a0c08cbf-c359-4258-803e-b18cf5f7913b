package com.dbj.classpal.admin.common.bo.sys.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数据字典主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysDict对象", description="数据字典主表")
public class SysDictDetailBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description ="主键id")
    private Integer id;


}
