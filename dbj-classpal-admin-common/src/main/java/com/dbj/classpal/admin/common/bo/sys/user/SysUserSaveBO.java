package com.dbj.classpal.admin.common.bo.sys.user;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name = "SysUser对象", description="用户表")
public class SysUserSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "姓名")
    @NotEmpty(message = "姓名不能为空")
    private String nickName;

    @Schema(description = "登陆帐号")
    @NotEmpty(message = "登陆帐号不能为空")
    private String accounts;

    @Schema(description = "启用状态 0禁用 1启用")
    @NotNull(message = "启用状态不能为空")
    private Integer accountsStatus;

    @Schema(description = "角色ids")
    private List<Integer> roleIds;

    @Schema(description = "部门ids")
    @NotEmpty(message = "部门不能为空")
    private List<Integer> deptIds;


}
