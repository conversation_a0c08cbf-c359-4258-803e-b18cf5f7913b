package com.dbj.classpal.admin.common.bo.sys.help;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name ="SysHelpItems对象", description="角色信息表")
public class SysHelpItemsSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "页面id")
    private Integer menuPageId;

    @Schema(description = "角色描述")
    private String contentDetail;

    @Schema(description = "状态 0 禁用 1启用")
    private Integer status;



}
