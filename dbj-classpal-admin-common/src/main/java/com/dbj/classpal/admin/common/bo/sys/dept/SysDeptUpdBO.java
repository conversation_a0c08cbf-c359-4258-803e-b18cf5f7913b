
package com.dbj.classpal.admin.common.bo.sys.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysDept
* Date:     2024-2-2 13:36:06
* Description: 描述：部门表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysDeptUpdBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	@NotNull
	@Schema(description ="部门id")
	private Integer id;

	/**
	 * 部门名称
	 */
	@NotEmpty
	@Schema(description ="部门名称")
	private String deptName;
	/**
	 * 显示顺序
	 */
	@Schema(description ="显示顺序")
	private Integer orderNum;

}
