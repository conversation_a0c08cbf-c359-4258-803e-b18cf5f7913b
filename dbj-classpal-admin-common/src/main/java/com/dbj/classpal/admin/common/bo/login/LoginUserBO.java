package com.dbj.classpal.admin.common.bo.login;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * 登录用户信息
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Tag(name = "登录用户信息",description = "登录用户信息")
@Data
public class LoginUserBO {

    /**
     * 帐号
     */
    @Schema(description ="帐号")
    private String accounts;
    /**
     * 密码
     */
    @Schema(description ="密码")
    private String password;
    /**
     * preLogin传时间戳
     */
    @Schema(description ="preLogin传时间戳")
    private String timestamp;
    /**
     * 验证码
     */
    @Schema(description ="验证码")
    private String code;
    /**
     * 7天免登录，1：是，0：否
     */
    @Schema(description ="是否7天免登录")
    private Boolean noLogin;
}
