package com.dbj.classpal.admin.common.dto.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Classname AppConfigItemQueryDTO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 15:06:22
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Tag(name ="AppConfigItem对象", description="APP配置项表")
public class AppConfigItemQueryDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;
    
    @Schema(description = "配置类型ID")
    private Integer typeId;

    @Schema(description = "配置项标题")
    private String itemTitle;

    @Schema(description = "配置项内容(JSON格式)")
    private String itemContent;

    @Schema(description = "链接地址")
    private String itemUrl;

    @Schema(description = "图片地址")
    private String itemImage;
    
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer itemStatus;

    @Schema(description = "状态文本")
    private String itemStatusStr;
    
    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "备注说明")
    private String remark;

    @Schema(description = "创建者")
    private Integer createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private Integer updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0-否 1-是")
    private Integer isDeleted;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "租户id")
    private Integer tenantId;
}
    