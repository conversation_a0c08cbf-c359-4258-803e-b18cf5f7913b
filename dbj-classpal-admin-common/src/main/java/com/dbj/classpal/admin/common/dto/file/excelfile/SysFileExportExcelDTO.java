package com.dbj.classpal.admin.common.dto.file.excelfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 导出文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysFileExportExcel对象", description="导出文件记录")
public class SysFileExportExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "服务标识 system app books")
    private String sign;

    @Schema(description = "导出文件名称")
    private String fileName;

    @Schema(description = "导出文件url")
    private String fileUrl;

    @Schema(description = "文件处理后的url")
    private String processedUrl;

    @Schema(description = "开始处理时间")
    private LocalDateTime handleStartTime;

    @Schema(description = "结束处理时间")
    private LocalDateTime handleEndTime;

    @Schema(description = "上传文件业务类型")
    private String type;

    @Schema(description = "处理失败(还没有开始处理数据就失败的原因)")
    private String errorMsg;

    @Schema(description = "额外参数")
    private String paramJson;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 5 已取消")
    private Integer status;
}
