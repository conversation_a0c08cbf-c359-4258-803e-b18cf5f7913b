package com.dbj.classpal.admin.common.dto.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 导入文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name= "SysFileImportExcel对象", description="导入文件记录")
public class SysFileImportExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "所属模块名称")
    private String moduleName;

    @Schema(description = "服务标识 system app books")
    private String sign;

    @Schema(description = "源文件名称")
    private String fileName;

    @Schema(description = "源文件url")
    private String fileUrl;

    @Schema(description = "新增条数")
    private Integer addNum;

    @Schema(description = "更新条数")
    private Integer updNum;

    @Schema(description = "数据正确条数")
    private Integer subNum;

    @Schema(description = "错误条数")
    private Integer errorNum;

    @Schema(description = "文件处理后的url")
    private String processedUrl;

    @Schema(description = "开始处理时间")
    private LocalDateTime handleStartTime;

    @Schema(description = "结束处理时间")
    private LocalDateTime handleEndTime;

    @Schema(description = "上传文件业务类型")
    private String type;

    @Schema(description = "处理失败(还没有开始处理数据就失败的原因)")
    private String errorMsg;

    @Schema(description = "额外参数")
    private String paramJson;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
    private Integer status;

    @Schema(description = "创建人", hidden = true)
    private Integer createBy;
    @Schema(description = "创建人名称")
    private String createName;


}
