package com.dbj.classpal.admin.common.dto.sys.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-printer-system-bus
 * @className SysRolePageDTO
 * @description
 * @date 2024-02-16 16:42
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysRolePageDTO implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @Schema(description ="角色ID")
    private Integer id;
    /**
     * 角色名称
     */
    @Schema(description ="角色名称")
    private String name;
    /**
     * 角色权限字符串
     */
    @Schema(description ="角色权限字符串")
    private String roleCode;
    /**
     * 角色描述
     */
    @Schema(description ="角色描述")
    private String describes;
    /**
     * 显示顺序
     */
    @Schema(description ="显示顺序")
    private Integer roleSort;
    /**
     * 状态 0-正常 1-停用
     */
    @Schema(description ="状态 0-禁用 1-启用")
    private Integer roleStatus;
}
