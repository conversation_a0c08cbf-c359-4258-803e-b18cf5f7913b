package com.dbj.classpal.admin.common.bo.file.excelfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 导出文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysFileExportExcel对象", description="导出文件记录")
public class SysFileExportExcelSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "服务标识 admin app books")
    private String sign;

    @Schema(description = "导出文件名称")
    private String fileName;

    @Schema(description = "上传文件业务类型")
    private String type;

    @Schema(description = "额外参数")
    private String paramJson;


}
