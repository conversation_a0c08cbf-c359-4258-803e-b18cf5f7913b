package com.dbj.classpal.admin.common.bo.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;

/**
 * @Classname AppConfigItemSharePosterQueryBO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 17:29:46
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "分享海报查询BO",description = "分享海报查询BO")
public class AppConfigItemSharePosterQueryBO implements Serializable {

    @Schema(description = "海报标题")
    private String itemTitle;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer itemStatus;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "类型编码")
    private String typeCode;
}
    