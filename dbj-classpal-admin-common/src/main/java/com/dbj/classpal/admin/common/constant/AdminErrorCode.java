package com.dbj.classpal.admin.common.constant;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ErroeCode
 * @description admin的错误编码由 ADMIN_1000000 开始 一直向后编写
 * @date 2025-03-12 14:01
 **/
@Data
public class AdminErrorCode {

    public final static String LOGIN_FAIL_CODE = "ADMIN_1000000";
    public final static String LOGIN_FAIL_MSG = "登录失败, 请联系管理员! ";

    public final static String NO_USER_CODE = "ADMIN_1000001";
    public final static String NO_USER_MSG = "用户不存在, 请联系管理员添加! ";

    public final static String PW_ERROR_CODE = "ADMIN_1000002";
    public final static String PW_ERROR_MSG = "密码错误, 请重新输入或联系管理员! ";

    public final static String PARAM_ERROR_CODE = "ADMIN_1000003";
    public final static String PARAM_ERROR_MSG = "body参数异常! ";

    public final static String GTEST_ERROR_CODE = "ADMIN_1000004";
    public final static String GTEST_ERROR_MSG = "验证码错误或过期! ";

    public final static String NOT_LOGIN_CODE = "ADMIN_1000005";
    public final static String NOT_LOGIN_MSG = "没有权限, 请重新登录或联系管理员!";

    public final static String TOKEN_OVERTIME_CODE = "ADMIN_1000006";
    public final static String TOKEN_OVERTIME_MSG = "登录超时, 请重新登录!";

    public final static String KICK_OFF_CODE = "ADMIN_1000007";
    public final static String KICK_OFF_MSG = "您的账号在其他地方登录, 请重新登录或联系管理员!";

    public final static String DEPT_NOT_EXIST_CODE = "ADMIN_1000008";
    public final static String DEPT_NOT_EXIST_MSG = "部门信息不存在";

    public final static String DEPT_FATHER_ID_NOT_EXIST_CODE = "ADMIN_1000009";
    public final static String DEPT_FATHER_ID_NOT_EXIST_MSG = "请选择父级节点";

    public final static String DEPT_FATHER_NOT_EXIST_CODE = "ADMIN_1000010";
    public final static String DEPT_FATHER_NOT_EXIST_MSG = "父节点不存在";

    public final static String DEPT_NAME_REPEAT_CODE = "ADMIN_1000011";
    public final static String DEPT_NAME_REPEAT_MSG = "部门名称重复";

    public final static String DICT_CODE_REPEAT_CODE = "ADMIN_1000012";
    public final static String DICT_CODE_REPEAT_MSG = "字典标识重复 ";

    public final static String DICT_NOT_EXIST_CODE = "ADMIN_1000013";
    public final static String DICT_NOT_EXIST_MSG = "字典不存在";

    public final static String DICT_ITEM_NOT_EXIST_CODE = "ADMIN_1000014";
    public final static String DICT_ITEM_NOT_EXIST_MSG = "字典标签不存在";

    public final static String HELP_PAGE_EXIST_CODE = "ADMIN_1000015";
    public final static String HELP_PAGE_EXIST_MSG = "当前页面已经被关联";

    public final static String MENU_NOT_EXIST_CODE = "ADMIN_1000016";
    public final static String MENU_NOT_EXIST_MSG = "菜单不存在";

    public final static String MENU_NAME_REPEAT_CODE = "ADMIN_1000016";
    public final static String MENU_NAME_REPEAT_MSG = "菜单名称重复不存在";

    public final static String MENU_NODE_LOOP_CODE = "ADMIN_1000017";
    public final static String MENU_NODE_LOOP_MSG = "上级不能修改为自己的子节点及自己";

    public final static String USER_ROLE_NOT_EXIST_CODE = "ADMIN_1000018";
    public final static String USER_ROLE_NOT_EXIST_MSG = "用户角色不存在";

    public final static String ROLE_NOT_EXIST_CODE = "ADMIN_1000019";
    public final static String ROLE_NOT_EXIST_MSG = "角色不存在";

    public final static String ROLE_NAME_REPEAT_CODE = "ADMIN_1000020";
    public final static String ROLE_NAME_REPEAT_MSG = "角色名称重复";

    public final static String ROLE_ADMIN_NOT_UPDATE_CODE = "ADMIN_1000021";
    public final static String ROLE_ADMIN_NOT_UPDATE_MSG = "超级管理员不能修改";

    public final static String USER_NOT_EXIST_CODE = "ADMIN_1000022";
    public final static String USER_NOT_EXIST_MSG = "用户不存在";

    public final static String USER_ACCOUNTS_REPEAT_CODE = "ADMIN_1000023";
    public final static String USER_ACCOUNTS_REPEAT_MSG = "用户帐号已存在";


    public final static String MENU_CHILDREN_EXIST_CODE = "ADMIN_1000024";
    public final static String MENU_CHILDREN_EXIST_MSG = "菜单存在子节点，不允许删除";


    public final static String DEPT_CHILDREN_EXIST_CODE = "ADMIN_1000025";
    public final static String DEPT_CHILDREN_EXIST_MSG = "删除部门需要该部门下没有用户且没有子部门，请确认后重试!";

    public final static String OSS_FILE_NOT_EXIST_CODE = "ADMIN_1000026";
    public final static String OSS_FILE_NOT_EXIST_MSG = "文件不存在，请确认后重试!";

    public final static String PASSWORD_NOT_SAME_CODE = "ADMIN_1000027";
    public final static String PASSWORD_NOT_SAME_MSG = "两次密码输入不一致";

    public final static String EXCEL_FILE_NOT_EXIST_CODE = "ADMIN_1000028";
    public final static String EXCEL_FILE_NOT_EXIST_MSG = "文件不存在，请确认后重试!";

    public final static String EXPORT_EXCEL_FILE_NOT_EXIST_CODE = "ADMIN_1000029";
    public final static String EXPORT_EXCEL_FILE_NOT_EXIST_MSG = "导出文件不存在，请确认后重试!";


    public final static String HELP_PAGE_NOT_EXIST_CODE = "ADMIN_1000030";
    public final static String HELP_PAGE_NOT_EXIST_MSG = "当前页面不存在";

    public final static String EXPORT_EXCEL_FILE_UPDATE_FILE_CODE = "ADMIN_1000031";
    public final static String EXPORT_EXCEL_FILE_UPDATE_FILE_MSG = "导入文件修改失败，请确认后重试!";


    public final static String DICT_ITEM_EXIST_CODE = "ADMIN_1000032";
    public final static String DICT_ITEM_EXIST_MSG = "字典标签已存在";

    public final static String DICT_NAME_EXIST_CODE = "ADMIN_1000033";
    public final static String DICT_NAME_EXIST_MSG = "字典名称已存在";

    public final static String USER_ROLE_EXIST_CODE = "ADMIN_1000034";
    public final static String USER_ROLE_EXIST_MSG = "角色已分配给用户,不允许删除";



    public final static String APP_AGREEMENT_PARAM_ERROR_CODE = "APP_AGREEMENT_1000001";
    public final static String APP_AGREEMENT_PARAM_ERROR_MSG = "参数错误!";

    public final static String APP_AGREEMENT_EXIST_CODE = "APP_AGREEMENT_1000002";
    public final static String APP_AGREEMENT_EXIST_MSG = "协议已存在，请确认后重试!";

    public final static String APP_CONFIG_TYPE_EXIST_CODE = "APP_CONFIG_TYPE_1000001";
    public final static String APP_CONFIG_TYPE_EXIST_MSG = "配置不存在，请确认后重试!";

    public final static String APP_CONFIG_ITEM_PARAM_ERROR_CODE = "APP_CONFIG_ITEM_1000001";
    public final static String APP_CONFIG_ITEM_PARAM_ERROR_MSG = "参数错误!";

    public final static String APP_CONFIG_ITEM_NOT_EXIST_CODE = "APP_CONFIG_ITEM_1000002";
    public final static String APP_CONFIG_ITEM_NOT_EXIST_MSG = "配置项不存在，请确认后重试!";

    public final static String APP_CONFIG_ITEM_EXIST_CODE = "APP_CONFIG_ITEM_1000003";
    public final static String APP_CONFIG_ITEM_EXIST_MSG = "配置项已存在，请确认后重试!";

    public final static String APP_CONFIG_ITEM_SHARE_POSTER_UPDATE_CODE = "APP_CONFIG_ITEM_1000004";
    public final static String APP_CONFIG_ITEM_SHARE_POSTER_UPDATE_MSG = "禁用的分享海报不可编辑!";


    public final static String APP_CONFIG_ITEM_SHARE_POSTER_DEL_CODE = "APP_CONFIG_ITEM_1000005";
    public final static String APP_CONFIG_ITEM_SHARE_POSTER_DEL_MSG = "启用的分享海报不可删除!";

    public final static String APP_CONFIG_ITEM_SHARE_POSTER_LIST_DEL_CODE = "APP_CONFIG_ITEM_1000006";
    public final static String APP_CONFIG_ITEM_SHARE_POSTER_LIST_DEL_MSG = "所选项目中包含已启用状态的海报，请确认后重试!";


    public final static String APP_CONFIG_ITEM_ACCEPT_RESULT_ERROR_CODE = "APP_CONFIG_ITEM_1000007";
    public final static String APP_CONFIG_ITEM_ACCEPT_RESULT_ERROR_MSG = "期待返回结果错误，请确认后重试!";


    public final static String APP_COMMON_SAVE_FAIL_CODE = "APP_COMMON_1000001";
    public final static String APP_COMMON_SAVE_FAIL_MSG = "新增失败，请确认后重试!";

    public final static String APP_COMMON_UPDATE_FAIL_CODE = "APP_COMMON_1000002";
    public final static String APP_COMMON_UPDATE_FAIL_MSG = "修改失败，请确认后重试!";


    public final static String APP_COMMON_DELETE_FAIL_CODE = "APP_COMMON_1000003";
    public final static String APP_COMMON_DELETE_FAIL_MSG = "删除失败，请确认后重试!";

}
