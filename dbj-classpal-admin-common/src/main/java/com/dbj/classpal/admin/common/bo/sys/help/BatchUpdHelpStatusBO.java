package com.dbj.classpal.admin.common.bo.sys.help;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name ="SysHelpItems对象", description="帮助信息表")
public class BatchUpdHelpStatusBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "主键id")
    @NotEmpty(message = "主键标识不能为空")
    private List<Integer> ids;

    @Schema(description = "状态 0 禁用 1启用")
    @NotNull(message = "状态不能为空")
    private Integer status;



}
