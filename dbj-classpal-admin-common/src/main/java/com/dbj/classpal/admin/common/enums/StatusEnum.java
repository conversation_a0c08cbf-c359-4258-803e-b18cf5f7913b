package com.dbj.classpal.admin.common.enums;

/**
 * @Classname CommonEnum
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 12:00:40
 * @Created by xuezhi
 */
public enum StatusEnum {
    /**
     * 状态 0禁用 1启用
     */
    AGREEMENT_STATUS_NO(0,"禁用"),
    AGREEMENT_STATUS_YES(1,"启用");


    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    StatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static StatusEnum getByCode(Integer code) {
        for (StatusEnum typeEnum : StatusEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}