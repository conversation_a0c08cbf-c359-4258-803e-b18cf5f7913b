package com.dbj.classpal.admin.common.bo.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 导入文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name= "SysFileImportExcelQueryBO对象", description="查询文件记录")
public class SysFileImportExcelQueryBO implements Serializable {

    @Schema(description = "md5值")
    private String md5;


}
