
package com.dbj.classpal.admin.common.bo.sys.menu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysMenu
* Date:     2024-2-2 13:36:06
* Description: 描述：菜单信息表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysMenuUpdBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 菜单ID
	 */
	@Schema(description ="菜单ID")
	private Integer id;
	/**
	 * 菜单名称
	 */
	@Schema(description ="菜单名称")
	private String name;
	/**
	 * 菜单简称
	 */
	@Schema(description ="菜单简称")
	private String shortName;
	/**
	 * 父菜单ID
	 */
	@Schema(description ="父菜单ID")
	private Integer parentId;
	/**
	 * 显示顺序
	 */
	@Schema(description ="显示顺序")
	private Integer orderNum;
	/**
	 * 请求地址
	 */
	@Schema(description ="请求地址")
	private String url;
	/**
	 * 打开方式（menuItem页签 menuBlank新窗口）
	 */
	@Schema(description ="打开方式（menuItem页签 menuBlank新窗口）")
	private String target;
	/**
	 * 菜菜单类型（1 菜单 2 页面 3 按钮）
	 */
	@Schema(description ="菜单类型（1 菜单 2 页面 3 按钮）")
	private Integer menuType;
	/**
	 * 菜单状态（0显示 1隐藏）
	 */
	@Schema(description ="菜单状态（0显示 1隐藏）")
	private Integer visible;
	/**
	 * 是否刷新（0刷新 1不刷新）
	 */
	@Schema(description ="是否刷新（0刷新 1不刷新）")
	private Integer isRefresh;
	/**
	 * 权限标识
	 */
	@Schema(description ="权限标识")
	private String perms;
	/**
	 * 菜单图标
	 */
	@Schema(description ="菜单图标")
	private String icon;

}
