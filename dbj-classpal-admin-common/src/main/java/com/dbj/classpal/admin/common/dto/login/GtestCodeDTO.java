package com.dbj.classpal.admin.common.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

/**
 * 极验
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Tag(name="极验",description = "极验")
@Data
public class GtestCodeDTO {
    /**
     * 时间戳
     */
    @Schema(description ="时间戳")
    private String timestamp;
    /**
     * 验证码
     */
    @Schema(description ="验证码")
    private String code;
    /**
     * 极验appId
     */
    @Schema(description ="极验appId")
    private String appId;
    /**
     * 极验secret
     */
    @Schema(description ="极验secret")
    private String appSecret;
}
