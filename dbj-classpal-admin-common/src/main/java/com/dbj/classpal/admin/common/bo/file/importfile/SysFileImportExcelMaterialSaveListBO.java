package com.dbj.classpal.admin.common.bo.file.importfile;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: SysFileImportExcelSaveListBO
 * Date:     2025-04-08 17:08:58
 * Description: 表名： ,描述： 表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name= "SysFileImportExcel批量保存BO", description="SysFileImportExcel批量保存BO")
public class SysFileImportExcelMaterialSaveListBO implements Serializable {
    private List<SysFileImportExcelMaterialSaveBO>saveBOList;
}
