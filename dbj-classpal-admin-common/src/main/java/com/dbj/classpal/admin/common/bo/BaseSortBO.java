package com.dbj.classpal.admin.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className BaseIdsBO
 * @description
 * @date 2025-03-18 13:58
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class BaseSortBO<T> implements Serializable {

    @Schema(description ="排序类")
    @NotNull(message = "排序类")
    private List<T> sorts;
}
