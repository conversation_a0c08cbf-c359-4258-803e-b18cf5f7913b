
package com.dbj.classpal.admin.common.dto.sys.dept;

import com.dbj.classpal.admin.common.dto.sys.user.SysDeptUserNameDTO;
import com.dbj.classpal.admin.common.dto.sys.user.SysUserDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysDept
* Date:     2024-2-2 13:36:06
* Description: 描述：部门表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysDeptDTO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	@Schema(description ="部门id")
	private Integer id;
	/**
	 * 父部门id
	 */
	@Schema(description ="父部门id")
	private Integer fatherId;
	/**
	 * 部门名称
	 */
	@Schema(description ="部门名称")
	private String deptName;

	@Schema(description = "头像")
	private String avatar;

	/**
	 * 显示顺序
	 */
	@Schema(description ="显示顺序")
	private Integer orderNum;
	/**
	 * 显示顺序
	 */
	@Schema(description ="1 部门 2人员")
	private Integer type;
	/**
	 * 用户数量
	 */
	@Schema(description ="用户数量")
	private Integer userNum;
	/**
	 * 子部门列表
	 */
	@Schema(description ="子部门列表")
	private List<SysDeptDTO> children;



	public void addChild(SysDeptDTO child) {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		this.children.add(child);
	}

}
