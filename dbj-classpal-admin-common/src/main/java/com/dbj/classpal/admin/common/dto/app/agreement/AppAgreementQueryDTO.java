package com.dbj.classpal.admin.common.dto.app.agreement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Tag(name ="AppAgreement对象", description="APP协议管理表")
public class AppAgreementQueryDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "协议类型 1-用户服务协议 2-用户隐私政策 3-儿童隐私政策 4-第三方共享信息清单 5-个人信息收集清单 6-注销账号申请")
    private Integer agreementType;

    @Schema(description = "协议类型文本")
    private String agreementTypeStr;

    @Schema(description = "协议标题")
    private String agreementTitle;

    @Schema(description = "协议内容")
    private String agreementContent;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer agreementStatus;

    @Schema(description = "状态文本")
    private String agreementStatusStr;

    @Schema(description = "版本号(三段式)")
    private Integer versionCode;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "创建者")
    private Integer createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private Integer update_by;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0-否 1-是")
    private Integer isDeleted;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "租户id")
    private Integer tenantId;
}
