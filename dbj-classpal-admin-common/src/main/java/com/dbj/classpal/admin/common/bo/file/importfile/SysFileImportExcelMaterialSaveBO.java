package com.dbj.classpal.admin.common.bo.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 导入文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name= "SysFileImportExcel对象", description="导入文件记录")
public class SysFileImportExcelMaterialSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "所属模块名称")
    private String moduleName;

    @Schema(description = "服务标识 admin app books")
    private String sign;

    @Schema(description = "源文件名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "源文件名称不能为空")
    private String fileName;

    @Schema(description = "源文件url")
    private String fileUrl;

    @Schema(description = "文件处理业务类型 1普通文件 2素材中心 3音频tts制作")
    private Integer businessType;

    @Schema(description = "额外参数 {materialId:'所在文件夹ID(必填)',fileName:'文件名(必填)',size:'文件大小(kb)(必填)',dirPath:'文件夹路径（上传文件不填，上传文件夹时必填）,md5:'文件md5值(必填)'}",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "额外参数不能为空")
    private String paramJson;


}
