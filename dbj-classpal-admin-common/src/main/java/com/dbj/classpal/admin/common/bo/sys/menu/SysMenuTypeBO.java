
package com.dbj.classpal.admin.common.bo.sys.menu;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysMenu
* Date:     2024-2-2 13:36:06
* Description: 描述：菜单信息表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysMenuTypeBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	@Serial
	private static final long serialVersionUID = 1L;
	/**
	 * 菜单类型 1 菜单 2 页面 3按钮
	 */
	@Schema(description ="菜单类型 1 菜单 2 页面 3按钮")
	@NotEmpty(message = "菜单类型不能为空")
	private List<Integer> menuTypeList;

}
