
package com.dbj.classpal.admin.common.bo.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: File
* Date:     2023-10-27 9:44:06
* Description: 描述：
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysFileImportExcelClearBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	@Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
	private List<Integer> status;

}
