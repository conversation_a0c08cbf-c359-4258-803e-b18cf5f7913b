package com.dbj.classpal.admin.common.bo.sys.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据字典项表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysDictItem对象", description="数据字典项表")
public class SysDictItemUpdStatusBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "字典状态ids")
    private List<Long> ids;

    @Schema(description = "字典状态")
    private Long status;


}
