
package com.dbj.classpal.admin.common.bo.sys.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysDept
* Date:     2024-2-2 13:36:06
* Description: 描述：部门表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysDeptBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	@Schema(description ="部门id")
	@NotNull(message = "参数不能为空")
	private Integer id;


}
