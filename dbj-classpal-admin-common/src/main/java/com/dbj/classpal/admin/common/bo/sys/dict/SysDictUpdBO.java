package com.dbj.classpal.admin.common.bo.sys.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数据字典主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysDict对象", description="数据字典主表")
public class SysDictUpdBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @NotNull
    @Schema(description ="主键id")
    private Integer id;

    @Schema(description ="字典名称")
    private String dictName;

    @Schema(description ="字典标识")
    private String dictCode;

    @Schema(description ="启用状态 1-启用 0-停用")
    private Integer status;

    @Schema(description ="字典描述")
    private String description;

    @Schema(description ="排序权重")
    private Integer sort;


}
