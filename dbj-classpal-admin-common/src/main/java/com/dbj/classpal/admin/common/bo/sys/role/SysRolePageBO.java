
package com.dbj.classpal.admin.common.bo.sys.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysRole
* Date:     2024-2-2 13:36:06
* Description: 描述：角色信息表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysRolePageBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 角色名称
	 */
	@Schema(description ="角色名称")
	private String name;
	/**
	 * 角色描述
	 */
	@Schema(description ="角色描述")
	private String describes;

	@Schema(description ="角色状态")
	private List<Integer> roleStatusList;


}
