package com.dbj.classpal.admin.common.dto.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Classname AppConfigTypeQueryDTO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 15:06:05
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Tag(name ="AppConfigType对象", description="APP配置类型表")
public class AppConfigTypeQueryDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "父级ID")
    private Integer parentId;

    @Schema(description = "类型编码")
    private String typeCode;

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "层级 1-一级 2-二级")
    private Integer typeLevel;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer typeStatus;

    @Schema(description = "状态文本")
    private String typeStatusStr;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "创建者")
    private Integer createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private Integer update_by;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0-否 1-是")
    private Integer isDeleted;

    @Schema(description = "租户id")
    private Integer tenantId;
}
    