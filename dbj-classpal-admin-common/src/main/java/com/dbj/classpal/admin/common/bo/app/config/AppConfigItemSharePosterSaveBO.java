package com.dbj.classpal.admin.common.bo.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serializable;

/**
 * @Classname AppConfigItemSharePosterSaveBO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 17:29:46
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "分享海报新增BO",description = "分享海报新增BO")
public class AppConfigItemSharePosterSaveBO implements Serializable {

    @Schema(description = "配置类型ID",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotNull(message = "配置类型ID不能为空")
    private Integer typeId;

    @Schema(description = "海报标题",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "海报标题不能为空")
    @Size(max = 24,message = "海报标题不能超过24个字符")
    private String itemTitle;

    @Schema(description = "配置项内容(JSON格式)")
    private String itemContent;

    @Schema(description = "链接地址")
    private String itemUrl;

    @Schema(description = "海报",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "海报不能为空")
    private String itemImage;

    @Schema(description = "状态 0-禁用 1-启用",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态不能为空")
    private Integer itemStatus;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "备注说明")
    private String remark;

    @Schema(description = "版本号")
    private Integer version;
}
    