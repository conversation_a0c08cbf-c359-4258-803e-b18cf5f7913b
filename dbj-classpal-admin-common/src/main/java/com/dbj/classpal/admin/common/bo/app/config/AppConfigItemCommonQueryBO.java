package com.dbj.classpal.admin.common.bo.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.util.List;

/**
 * @Classname AppConfigItemCommonBO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-20 16:24:56
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "配置项通用查询BO",description = "配置项通用查询BO")
public class AppConfigItemCommonQueryBO {

    @Schema(description = "主键ids")
    private List<Integer>ids;
}
    