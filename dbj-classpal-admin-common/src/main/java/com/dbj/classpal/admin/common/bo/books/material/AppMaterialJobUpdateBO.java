package com.dbj.classpal.admin.common.bo.books.material;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "定时任务素材中心文件job查询bo",description = "定时任务素材中心文件job查询bo")
public class AppMaterialJobUpdateBO {

    @Schema(description ="文件处理状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
    private Integer status;

    @Schema(description ="需要修改的id列表")
    private List<Integer> ids;

    @Schema(description ="错误消息")
    private String msg;
}
