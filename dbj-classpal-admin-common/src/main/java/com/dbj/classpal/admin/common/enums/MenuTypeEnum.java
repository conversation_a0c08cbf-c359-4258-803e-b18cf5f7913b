package com.dbj.classpal.admin.common.enums;

/**
 * <AUTHOR>
 * @description: 菜单类型枚举
 * @since 2024-03-28 11:54
 */
public enum MenuTypeEnum {

    DIRECTORY(1, "菜单"),

    MENU(2, "页面"),

    BUTTON(3, "按钮");

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    MenuTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MenuTypeEnum getByCode(Integer code) {
        for (MenuTypeEnum typeEnum : MenuTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
