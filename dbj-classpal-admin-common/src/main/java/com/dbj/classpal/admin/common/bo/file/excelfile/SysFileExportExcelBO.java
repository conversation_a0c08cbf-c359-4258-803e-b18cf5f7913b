package com.dbj.classpal.admin.common.bo.file.excelfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 导出文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="SysFileExportExcel对象", description="导出文件记录")
public class SysFileExportExcelBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 5 已取消")
    private List<Integer> status;
    @Schema(description = "用户id",hidden = true)
    private Integer createBy;

}
