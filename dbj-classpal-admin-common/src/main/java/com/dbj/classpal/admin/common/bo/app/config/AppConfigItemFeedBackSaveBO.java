package com.dbj.classpal.admin.common.bo.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serializable;

/**
 * @Classname AppConfigItemFeedBackSaveBO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 17:29:46
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "反馈建议新增BO",description = "反馈建议新增BO")
public class AppConfigItemFeedBackSaveBO implements Serializable {

    @Schema(description = "配置类型ID",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotNull(message = "配置类型ID不能为空")
    private Integer typeId;

    @Schema(description = "小程序APPID",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "小程序APPID不能为空")
    @Size(max = 128,message = "小程序APPID不能超过128个字符长度")
    private String itemTitle;

    @Schema(description = "配置项内容(JSON格式)")
    private String itemContent;

    @Schema(description = "小程序路径",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "小程序路径不能为空")
    @Size(max = 256,message = "小程序路径不能超过256个字符长度")
    private String itemUrl;

    @Schema(description = "企业二维码",requiredMode=Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "企业二维码不能为空")
    private String itemImage;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer itemStatus;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "备注说明")
    private String remark;

    @Schema(description = "版本号")
    private Integer version;
}
    