package com.dbj.classpal.admin.common.enums.file;

import com.dbj.classpal.admin.common.bo.sys.dict.SysDictImportBO;
import com.dbj.classpal.admin.common.bo.sys.menu.SysMenuImportBO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileStrategyEnum
 * @date 2023-10-27 11:02
 **/
@AllArgsConstructor
@Getter
public enum ExcelFileStrategyEnum {
    /**
     * 菜单导入
     */
    SYS_MENU_EXCEL_FILE("sysMenuExcelFileStrategy", "系统菜单", null, SysMenuImportBO.class),
    /**
     * 字典导入
     */
    SYS_DICT_EXCEL_FILE("sysDictExcelFileStrategy", "数据字典", null, SysDictImportBO.class),;

    private String handler;

    private String value;

    private String routingKey;

    private Class<?> importClass;

    public static ExcelFileStrategyEnum getEnum(String code){

        if(StringUtils.isEmpty(code)){
            return null;
        }
        for(ExcelFileStrategyEnum strategyEnum : ExcelFileStrategyEnum.values()){
            if(StringUtils.equals(strategyEnum.getHandler(),code)){
                return strategyEnum;
            }
        }
        return null;
    }

}
