package com.dbj.classpal.admin.common.bo.sys.menu;

import com.dbj.classpal.framework.utils.annotation.ExportExcel;
import com.dbj.classpal.framework.utils.annotation.ExportExcelColumn;
import com.dbj.classpal.framework.utils.annotation.ImportExcelColumn;
import com.dbj.classpal.framework.utils.dto.CommonExcelBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/24 08:45:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ExportExcel(sheetName = "菜单")
public class SysMenuImportBO extends CommonExcelBO implements Serializable {

    @Schema(description ="名称")
    @ImportExcelColumn(value = "名称",required = true)
    @ExportExcelColumn(name = "名称")
    public String name;

    @Schema(description ="资源ID")
    @ImportExcelColumn(value = "资源ID",unique = true)
    @ExportExcelColumn(name = "资源ID")
    public String identifier;


    @Schema(description ="上级")
    @ImportExcelColumn(value = "上级")
    @ExportExcelColumn(name = "上级")
    public String parentName;

    @Schema(description ="上级资源ID")
    @ImportExcelColumn(value = "上级资源ID")
    @ExportExcelColumn(name = "上级资源ID")
    public String parentIdentifier;

    @Schema(description ="类型")
    @ImportExcelColumn(value = "类型",kv = "1-菜单;2-页面;3-按钮")
    @ExportExcelColumn(name = "类型")
    public Integer menuType;

    @Schema(description ="折叠名称")
    @ImportExcelColumn(value = "折叠名称")
    @ExportExcelColumn(name = "折叠名称")
    public String shortName;

    @Schema(description ="排序权重")
    @ExportExcelColumn(name = "排序权重")
    public Integer orderNum;

    @Schema(description ="路由")
    @ImportExcelColumn(value = "路由")
    @ExportExcelColumn(name = "路由")
    public String url;

    @Schema(description ="权限标识")
    @ImportExcelColumn(value = "权限标识")
    @ExportExcelColumn(name = "权限标识")
    public String perms;

    @Schema(description ="图标")
    @ImportExcelColumn(value = "图标")
    @ExportExcelColumn(name = "图标")
    public String icon;

    @Schema(description ="打开方式")
    @ImportExcelColumn(value = "打开方式",kv = "menuItem-页签;menuBlank-新窗口")
    @ExportExcelColumn(name = "打开方式")
    public String target;

    @Schema(description ="菜单显示")
    @ImportExcelColumn(value = "菜单显示",kv = "0-显示;1-隐藏")
    @ExportExcelColumn(name = "菜单显示")
    public Integer visible;

    @Schema(description ="是否刷新")
    @ImportExcelColumn(value = "是否刷新",kv = "0-刷新;1-不刷新")
    @ExportExcelColumn(name = "是否刷新")
    public Integer isRefresh;

    /**
     * 层级
     */
    @Schema(description ="层级")
    private Integer level;
    private List<SysMenuImportBO> children = new ArrayList<>();
}
