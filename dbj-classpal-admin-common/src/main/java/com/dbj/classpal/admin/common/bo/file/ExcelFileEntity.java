package com.dbj.classpal.admin.common.bo.file;

import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileEntity
 * @description
 * @date 2023-10-27 10:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelFileEntity extends RabbitmqEntity implements Serializable {

    /**
     * file 表文件的id
     */
    @Schema(name = "file 表文件的id")
    private Integer id;

    /**
     * file 表文件的id
     */
    @Schema(name = "file 表文件的ids")
    private Set<Integer> ids;


    @Schema(name = "额外参数")
    private String paramJson;
}
