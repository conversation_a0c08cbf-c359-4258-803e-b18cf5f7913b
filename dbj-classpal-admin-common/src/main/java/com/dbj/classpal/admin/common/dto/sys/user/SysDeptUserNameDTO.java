package com.dbj.classpal.admin.common.dto.sys.user;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户与部门关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name ="SysUserDept对象", description="用户与部门关系表")
public class SysDeptUserNameDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    private Integer userId;

    @Schema(description = "部门id")
    private Integer deptId;

    @Schema(description = "用户名称")
    private String nickName;
    @Schema(description = "头像")
    private String avatar;

}
