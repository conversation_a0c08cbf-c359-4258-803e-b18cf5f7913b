
package com.dbj.classpal.admin.common.bo.sys.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: SysRole
* Date:     2024-2-2 13:36:06
* Description: 描述：角色信息表
* <AUTHOR> <PERSON>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class SysRoleUpdStatusBO implements Serializable  {
	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 角色ID
	 */
	@Schema(description ="角色ID")
	@NotEmpty(message = "角色ID不能为空")
	private List<Integer> ids;
	/**
	 * 状态 0-正常 1-停用
	 */
	@Schema(description = "角色状态 0 禁用 1 启用")
	@NotNull(message = "角色状态不能为空")
	private Integer roleStatus;

}
