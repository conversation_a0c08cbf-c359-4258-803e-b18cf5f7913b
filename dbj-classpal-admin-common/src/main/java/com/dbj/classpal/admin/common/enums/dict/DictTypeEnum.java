package com.dbj.classpal.admin.common.enums.dict;

/**
 * <AUTHOR>
 * @description: 菜单类型枚举
 * @since 2024-03-28 11:54
 */
public enum DictTypeEnum {

    DICT(1, "字典"),

    DICT_ITEM(2, "字典标签");

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    DictTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
