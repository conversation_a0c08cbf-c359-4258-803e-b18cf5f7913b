package com.dbj.classpal.admin.common.dto.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 导入文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name= "SysFileImportExcel对象", description="导入文件记录")
public class SysFileImportExcelCountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
    private Integer status;



    @Schema(description = "统计数量", hidden = true)
    private Integer num;


}
