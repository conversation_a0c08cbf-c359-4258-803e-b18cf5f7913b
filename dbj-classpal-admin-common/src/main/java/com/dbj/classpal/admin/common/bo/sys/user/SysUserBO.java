package com.dbj.classpal.admin.common.bo.sys.user;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name = "SysUser对象", description="用户表")
public class SysUserBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "用户昵称")
    private String nickName;



    @Schema(description = "帐号")
    private String accounts;

    @Schema(description = "帐号状态 0禁用 1启用")
    private Integer accountsStatus;

    @Schema(description = "部门id")
    private Integer deptId;

    @Schema(description = "用户角色")
    private String roleName;

    @Schema(description = "用户部门")
    private String deptName;




}
