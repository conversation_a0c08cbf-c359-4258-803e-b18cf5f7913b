package com.dbj.classpal.admin.client.api.file.importfile;

import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportSaveApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileImportApi
 * Date:     2025-04-08 11:04:00
 * Description: 表名： ,描述： 表
 */
@FeignClient(name = "dbj-classpal-admin-api-${spring.application.version}",path = "/api/classpal/system/${spring.application.version}")
public interface ExcelFileImportApi {

    /**
     * 根据导入文件的id查询文件信息
     * @param bo
     * @return
     */
    @PostMapping("/import/getExcelFileImportById")
    RestResponse<ExcelFileImportQueryApiDTO> getExcelFileImportById(@RequestBody ExcelFileImportQueryApiBO bo) throws BusinessException;

    /**
     * 根据导入文件的id查询文件信息
     * @param id
     * @return
     */
    @GetMapping("/import/getById")
    RestResponse<SysFileImportExcelBO> getById(@NotNull @RequestParam Integer id) throws BusinessException;
    /**
     * 修改业务为系统失败
     * @param fileDomain
     * @return
     */
    @PostMapping("/import/handleProcessingFailedSys")
    RestResponse<Boolean> handleProcessingFailedSys(@RequestBody SysFileImportExcelBO fileDomain);
    /**
     * 修改业务为系统失败
     * @param fileDomain
     * @return
     */
    @PostMapping("/import/handleProcessing")
    RestResponse<Boolean> handleProcessing(@RequestBody SysFileImportExcelBO fileDomain);
    /**
     * 修改任务为已完成
     * @param fileDomain
     * @return
     */
    @PostMapping("/import/updateFileProcessed")
    RestResponse<Boolean> updateFileProcessed(@RequestBody SysFileImportExcelBO fileDomain);
    /**
     * 修改任务为业务失败
     * @param fileDomain
     * @return
     */
    @PostMapping("/import/handleProcessingFailedBusiness")
    RestResponse<Boolean> handleProcessingFailedBusiness(@RequestBody SysFileImportExcelBO fileDomain);


    /**
     * 新增一条导入文件信息
     * @param bo
     * @return
     */
    @PostMapping("/import/saveExcelFileImportById")
    RestResponse<Boolean> saveExcelFileImportById(@RequestBody ExcelFileImportSaveApiBO bo) throws BusinessException;


    /**
     * 判断是否存在文件正在处理的md5文件
     * @param bo
     * @return
     */
    @PostMapping("/import/checkProcessMd5File")
    RestResponse<Boolean> checkProcessMd5File(@RequestBody ExcelFileImportQueryApiBO bo) throws BusinessException;

    /**
     * 修改导入文件信息
     * @param bo
     * @return
     */
    @PostMapping("/import/updateExcelFileImportById")
    RestResponse<Boolean> updateExcelFileImportById(@RequestBody ExcelFileImportUpdateApiBO bo) throws BusinessException;


    /**
     * 修改导入文件信息根据analysisJobId
     * @param bo
     * @return
     */
    @PostMapping("/import/getByAnalysisJobId")
    RestResponse<ExcelFileImportQueryApiDTO> getByAnalysisJobId(@RequestBody ExcelFileImportQueryApiBO bo) throws BusinessException;


    /**
     * 修改导入文件信息根据transcodeJobId
     * @param bo
     * @return
     */
    @PostMapping("/import/getByTransCodeJobId")
    RestResponse<ExcelFileImportQueryApiDTO> getByTransCodeJobId(@RequestBody ExcelFileImportQueryApiBO bo) throws BusinessException;

    /**
     * 根据文件状态查询处理中且存在jobId的列表数据
     * @param bo
     * @return
     */
    @PostMapping("/import/getExcelFileImportListByStatus")
    RestResponse<List<ExcelFileImportQueryApiDTO>> getExcelFileImportListByStatus(@RequestBody ExcelFileImportQueryApiBO bo) throws BusinessException;
}
