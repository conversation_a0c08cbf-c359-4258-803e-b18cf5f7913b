package com.dbj.classpal.admin.client.api.file.exports;

import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileImportApi
 * Date:     2025-04-08 11:04:00
 * Description: 表名： ,描述： 表
 */
@FeignClient(name = "dbj-classpal-admin-api-${spring.application.version}",path = "/api/classpal/system/${spring.application.version}")
public interface ExportlFileImportApi {

    /**
     * 根据导入文件的id查询文件信息
     * @param id
     * @return
     */
    @GetMapping("/export/getById")
    RestResponse<SysFileExportExcelBO> getById(@RequestParam Integer id) throws BusinessException;

    /**
     * 根据导入文件的id查询文件信息
     * @param fileDomain
     * @return
     */
    @PostMapping("/export/handleProcessingFailedSys")
    RestResponse<Boolean> handleProcessingFailedSys(@RequestBody SysFileExportExcelBO fileDomain);
    /**
     * 根据导入文件的id查询文件信息
     * @param fileDomain
     * @return
     */
    @PostMapping("/export/updateFileProcessed")
    RestResponse<Boolean> updateFileProcessed(@RequestBody SysFileExportExcelBO fileDomain);
    /**
     * 根据导入文件的id查询文件信息
     * @param fileDomain
     * @return
     */
    @PostMapping("/export/updateFileProcessing")
    RestResponse<Boolean> updateFileProcessing(@RequestBody SysFileExportExcelBO fileDomain);

}
