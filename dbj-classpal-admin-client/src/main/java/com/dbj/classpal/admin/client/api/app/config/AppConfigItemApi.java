package com.dbj.classpal.admin.client.api.app.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.app.config.AppConfigSharePosterApiQueryBO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigCustomerApiQueryDTO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigFeedBackApiQueryDTO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigSharePosterApiQueryDTO;
import com.dbj.classpal.admin.client.dto.app.config.AppConfigTogetherApiQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 11:31:13
 * @Created by xuezhi
 */
@FeignClient(name = "dbj-classpal-admin-api-${spring.application.version}",path = "/api/classpal/system/${spring.application.version}")
public interface AppConfigItemApi {
    /**
     * 查询共学群信息
     * @return
     */
    @GetMapping("/app-config-item/getTogether")
    RestResponse< AppConfigTogetherApiQueryDTO> getTogether() throws BusinessException;


    /**
     * 查询反馈建议信息
     * @return
     */
    @GetMapping("/app-config-item/getFeedBack")
    RestResponse<AppConfigFeedBackApiQueryDTO> getFeedBack() throws BusinessException;


    /**
     * 获取客服列表
     * @return
     * @throws BusinessException
     */
    @GetMapping("/app-config-item/getCustomers")
    RestResponse<List<AppConfigCustomerApiQueryDTO>> getCustomers() throws BusinessException;


    /**
     * 获取所有海报列表
     * @return
     * @throws BusinessException
     */
    @GetMapping("/app-config-item/getAllSharePoster")
    RestResponse<List<AppConfigSharePosterApiQueryDTO>> getAllSharePoster() throws BusinessException;

    /**
     * 分页获取海报列表
     * @return
     * @throws BusinessException
     */
    @PostMapping("/app-config-item/getPageSharePoster")
    RestResponse<Page<AppConfigSharePosterApiQueryDTO>> getPageSharePoster(@RequestBody PageInfo<AppConfigSharePosterApiQueryBO> pageable) throws BusinessException;
}
