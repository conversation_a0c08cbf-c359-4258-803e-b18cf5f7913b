package com.dbj.classpal.admin.client.enums.sys.dept;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-gateway
 * @className WebFluxEnum
 * @description
 * @date 2025-03-21 10:10
 **/
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum UserDeptTreeTypeEnum {

    DEPT(1, "部门"),
    USER(2, "人员");

    private Integer code;

    private String value;
}
