package com.dbj.classpal.admin.client.dto.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;

/**
 * @Classname AppConfigSharePosterApiQueryDTO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-21 10:26:06
 * @Created by xuezhi
 */
@Data
@EqualsAndHashCode
@ToString
@Tag(name ="分享海报", description="分享海报")
public class AppConfigSharePosterApiQueryDTO implements Serializable {
    @Schema(description = "海报标题")
    private String itemTitle;

    @Schema(description = "海报")
    private String itemImage;
}
    