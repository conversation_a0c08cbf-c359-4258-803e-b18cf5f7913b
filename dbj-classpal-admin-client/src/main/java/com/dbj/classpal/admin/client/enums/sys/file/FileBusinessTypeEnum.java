package com.dbj.classpal.admin.client.enums.sys.file;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-gateway
 * @className WebFluxEnum
 * @description
 * @date 2025-03-21 10:10
 **/
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum FileBusinessTypeEnum {

    BUSINESS_NORMAL_FILE(1, "普通文件"),
    BUSINESS_MATERIAL(2, "素材中心"),
    BUSINESS_TTS(3, "音频tts制作");

    private Integer code;
    private String desc;

    public static FileBusinessTypeEnum getByCode(Integer code) {
        for (FileBusinessTypeEnum typeEnum : FileBusinessTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
