package com.dbj.classpal.admin.client.dto.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;

/**
 * @Classname AppConfigFeedBackApiQueryDTO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-21 09:21:42
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Tag(name ="反馈建议", description="反馈建议")
public class AppConfigFeedBackApiQueryDTO implements Serializable {
    @Schema(description = "小程序APP ID")
    private String itemTitle;

    @Schema(description = "小程序路径")
    private String itemUrl;

    @Schema(description = "企微二维码")
    private String itemImage;

}
    