package com.dbj.classpal.admin.client.bo.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

/**
 * @Classname AppConfigApiQueryBO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-21 13:34:43
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "APP配置项查询BO",description = "APP配置项查询BO")
public class AppConfigSharePosterApiQueryBO {

    @Schema(description = "海报标题")
    private String itemTitle;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer itemStatus;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "类型编码")
    private String typeCode;
}
    