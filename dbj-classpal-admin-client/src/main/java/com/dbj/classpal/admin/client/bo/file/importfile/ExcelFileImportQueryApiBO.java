package com.dbj.classpal.admin.client.bo.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileImportQueryApiBO
 * Date:     2025-04-08 11:23:31
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Tag(name = "文件导入查询ApiBO",description = "文件导入查询ApiBO")
public class ExcelFileImportQueryApiBO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "阿里云分析模板任务jobId")
    private String analysisSubmitJobId;

    @Schema(description = "阿里云查询分析模板结果任务jobId")
    private String analysisQueryJobId;

    @Schema(description = "阿里云转码jobId")
    private String transSubmitJobId;

    @Schema(description = "阿里云查询转码任务jobId")
    private String transQueryJobId;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
    private Integer status;

    @Schema(description = "md5")
    private String md5;
}
