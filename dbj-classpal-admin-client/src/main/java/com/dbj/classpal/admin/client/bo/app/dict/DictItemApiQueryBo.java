package com.dbj.classpal.admin.client.bo.app.dict;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DictItemApiQueryBo {
    /**
     * 字典ID
     */
    private Integer dictId;

    /**
     * 字典项ID
     */
    private String dictItemValue;
    
    /**
     * 字典编码
     */
    private String dictCode;
    
    /**
     * 方向 (-1:上一个, 1:下一个)
     */
    private Integer direction;
}