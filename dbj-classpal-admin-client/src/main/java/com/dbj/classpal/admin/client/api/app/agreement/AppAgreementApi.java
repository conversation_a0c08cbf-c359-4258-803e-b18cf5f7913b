package com.dbj.classpal.admin.client.api.app.agreement;

import com.dbj.classpal.admin.client.dto.app.agreement.AppAgreementApiQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 11:31:13
 * @Created by xuezhi
 */
@FeignClient(name = "dbj-classpal-admin-api-${spring.application.version}",path = "/api/classpal/system/${spring.application.version}")
public interface AppAgreementApi {

    /**
     * 查询所有协议列表
     * @return
     */
    @GetMapping("/app-agreement/getApiAllAppAgreement")
    RestResponse<List<AppAgreementApiQueryDTO>> getApiAllAppAgreements();

    /**
     * 根据id查询协议信息
     * @param id
     * @return
     */
    @Operation(summary = "根据id查询协议信息", description = "根据id查询协议信息")
    @GetMapping("/app-agreement/getApiAppAgreementById" )
    RestResponse<AppAgreementApiQueryDTO> getApiAppAgreementById(@RequestParam("id") @Parameter(description="主键id")Integer id) throws BusinessException;


    /**
     * 根据协议类型协议信息
     * @param agreementType
     * @return
     */
    @GetMapping("/app-agreement/getApiAppAgreementByTypeId")
    RestResponse<AppAgreementApiQueryDTO> getApiAppAgreementByTypeId(@RequestParam("agreementType")Integer agreementType) throws BusinessException;
}
