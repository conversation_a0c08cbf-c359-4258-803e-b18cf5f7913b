package com.dbj.classpal.admin.client.dto.app.agreement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Tag(name ="APP协议", description="APP协议")
public class AppAgreementApiQueryDTO implements Serializable {
    @Schema(description = "协议类文本")
    private String agreementTypeStr;

    @Schema(description = "协议标题")
    private String agreementTitle;

    @Schema(description = "协议内容")
    private String agreementContent;

    @Schema(description = "版本号(三段式)")
    private Integer versionCode;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;
}
