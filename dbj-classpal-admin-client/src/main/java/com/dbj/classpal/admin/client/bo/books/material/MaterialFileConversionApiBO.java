package com.dbj.classpal.admin.client.bo.books.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 导入文件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name= "SysFileImportExcel对象", description="导入文件记录")
public class MaterialFileConversionApiBO implements Serializable {

    @Schema(description = "源文件文件名")
    @NotEmpty(message = "源文件文件名不能为空")
    private String originalFileName;

    @Schema(description = "源文件url")
    @NotEmpty(message = "源文件url不能为空")
    private String fileUrl;

    @Schema(description = "文件处理业务类型 1普通文件 2素材中心 3音频tts制作")
    @NotNull(message = "文件处理业务类型不能为空")
    private Integer businessType;

    @Schema(description = "额外参数 {materialId:'所在文件夹ID(必填)',fileName:'文件名(必填)',size:'文件大小(kb)(必填)',dirPath:'文件夹路径（上传文件不填，上传文件夹时必填）,md5:'文件md5值(必填)'}",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "额外参数不能为空")
    private String paramJson;

}
