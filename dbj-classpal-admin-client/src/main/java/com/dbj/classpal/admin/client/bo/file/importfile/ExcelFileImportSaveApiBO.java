package com.dbj.classpal.admin.client.bo.file.importfile;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileImportUpdateBO
 * Date:     2025-04-08 11:11:55
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Tag(name = "文件导入修改ApiBO",description = "文件导入修改ApiBO")
public class ExcelFileImportSaveApiBO {
    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "源文件url")
    private String fileUrl;

    @Schema(description = "文件处理后的url")
    private String processedUrl;

    @Schema(description = "开始处理时间")
    private LocalDateTime handleStartTime;

    @Schema(description = "结束处理时间")
    private LocalDateTime handleEndTime;

    @Schema(description = "状态 0-待处理 1-处理中 2 已完成 3处理失败 (文件错误 ) 4处理失败(超时等待) 5 已取消")
    private Integer status;

}
