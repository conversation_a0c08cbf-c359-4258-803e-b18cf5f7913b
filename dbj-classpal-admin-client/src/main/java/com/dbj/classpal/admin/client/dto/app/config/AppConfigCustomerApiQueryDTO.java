package com.dbj.classpal.admin.client.dto.app.config;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;

import java.io.Serializable;

/**
 * @Classname AppConfigCustomerApiQueryDTO
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-21 09:25:46
 * @Created by xuezhi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Tag(name ="客服", description="客服")
public class AppConfigCustomerApiQueryDTO implements Serializable {

    @Schema(description = "客服名称")
    private String itemTitle;

    @Schema(description = "客服链接")
    private String itemUrl;
}
    