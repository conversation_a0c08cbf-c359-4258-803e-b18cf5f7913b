package com.dbj.classpal.admin.client.api.books.material;

import com.dbj.classpal.admin.client.bo.books.material.MaterialFileConversionApiBO;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "dbj-classpal-admin-api-${spring.application.version}",path = "/api/classpal/system/${spring.application.version}")
public interface AppMaterialUploadApi {


    /**
     * 文件上传素材中心文件-对外接口（转码）
     *
     * @param bo
     * @return RestResponse<Integer>
     */
    @PostMapping("/material/saveApiImportMaterialFile")
    RestResponse<Boolean> saveApiImportMaterialFile(@RequestBody @Valid MaterialFileConversionApiBO bo) throws Exception;
}
