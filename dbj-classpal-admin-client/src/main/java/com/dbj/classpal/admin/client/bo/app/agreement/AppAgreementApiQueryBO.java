package com.dbj.classpal.admin.client.bo.app.agreement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@Tag(name = "APP协议管理查询BO",description = "APP协议管理查询BO")
public class AppAgreementApiQueryBO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "协议类型 1-用户服务协议 2-用户隐私政策 3-儿童隐私政策 4-第三方共享信息清单 5-个人信息收集清单 6-注销账号申请")
    @NotNull(message = "协议类型不能为空")
    private Integer agreementType;

    @Schema(description ="版本号(三段式)")
    private String versionCode;

    @Schema(description ="版本号")
    private Integer version;

}
