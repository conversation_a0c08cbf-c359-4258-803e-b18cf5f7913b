package com.dbj.classpal.admin.client.api.sys.dict;

import com.dbj.classpal.admin.client.bo.app.dict.DictItemApiQueryBo;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictApiDTO;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-admin-bus
 * @className ISysDictService
 * @description
 * @date 2025-03-14 15:58
 **/
@FeignClient(name = "dbj-classpal-admin-api-${spring.application.version}",path = "/api/classpal/system/${spring.application.version}")
public interface ISysDictApi {



    /**
     * <AUTHOR>
     * @Description 获取所有字典数据
     * @Date 2025/3/17 9:23
     * @return SysDictDTO
     **/
    @GetMapping("/sys_dict/getSysDictInfoAll")
    RestResponse<Map<String,List<SysDictItemApiDTO>>> getSysDictInfoAll() throws BusinessException;
    /**
     * 获取相邻的字典项
     * @param queryBo 查询参数
     * @return 相邻的字典项
     */
    @PostMapping("/sys_dict/getAdjacentDictItem")
    RestResponse<SysDictItemApiDTO> getAdjacentDictItem(@RequestBody DictItemApiQueryBo queryBo) throws BusinessException;

    /**
     * 根据dictItemValue和dictCode查询字典数据
     * @param queryBo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/sys_dict/getDictItem")
    RestResponse<SysDictItemApiDTO>getDictItem(@RequestBody DictItemApiQueryBo queryBo) throws BusinessException;
}
