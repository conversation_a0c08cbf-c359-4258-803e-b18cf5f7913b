package com.dbj.classpal.admin.client.bo.app.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DictItemApiQueryByCodeBo {

    /**
     * 字典编码
     */
    @Schema(name = "字典编码",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "字典编码不能为空")
    private String dictCode;
}