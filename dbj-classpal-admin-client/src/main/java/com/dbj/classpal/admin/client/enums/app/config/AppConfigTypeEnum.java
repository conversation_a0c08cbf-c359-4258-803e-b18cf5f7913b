package com.dbj.classpal.admin.client.enums.app.config;

/**
 * @Classname AppConfigTypeEnum
 * @Description TODO
 * @Version 1.0
 * @Date 2025-03-19 14:16:27
 * @Created by xuezhi
 */
public enum AppConfigTypeEnum {

    /**
     * 运营类配置
     */
    TOGETHER_STUDY_GROUP("together_study_group","共学群"),
    FEEDBACK_SUGGESTIONS("feedback_suggestions","反馈建议"),
    CUSTOMER("customer","客服"),
    SHARE_POSTER("share_poster","分享海报");

    private String code;
    private String name;

    AppConfigTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static AppConfigTypeEnum getByCode(String code) {
        for (AppConfigTypeEnum typeEnum : AppConfigTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}