package com.dbj.classpal.admin.client.enums.app.agreement;

public enum AppAgreementEnum {

    /**
     * 协议类型 1-用户服务协议 2-用户隐私政策 3-儿童隐私政策 4-第三方共享信息清单 5-个人信息收集清单 6-注销账号申请
     */
    AGREEMENT_TYPE_USER_SERVICE(1,"用户服务协议"),
    AGREEMENT_TYPE_USER_PRIVACY(2,"用户隐私政策"),
    AGREEMENT_TYPE_CHILDREN_PRIVACY(3,"儿童隐私政策"),
    AGREEMENT_TYPE_ANOTHER_INFO_SHARE(4,"第三方共享信息清单"),
    AGREEMENT_TYPE_INDIVIDUAL_COLLECTION(5,"个人信息收集清单"),
    AGREEMENT_TYPE_APPLY_CANCEL_ACCOUNT(6,"注销账号申请");

    AppAgreementEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static AppAgreementEnum getByCode(Integer code) {
        for (AppAgreementEnum typeEnum : AppAgreementEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
