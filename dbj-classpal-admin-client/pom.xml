<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-admin-bus</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>dbj-classpal-admin-client</artifactId>
    <name>dbj-classpal-admin-client</name>
    <description>Generated Project by dbj-framework-codegen.</description>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-commons-starter</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-excel-starter</artifactId>
            <version>${parent.version}</version>
        </dependency>
    </dependencies>

    <build>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version><!--$NO-MVN-MAN-VER$ -->
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version><!--$NO-MVN-MAN-VER$ -->
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>


</project>